// Script to create missing Firestore collections
// Run this in Firebase Console > Firestore > Start collection

const collections = [
  {
    name: 'reports',
    document: 'default',
    data: {
      created: new Date(),
      type: 'system',
      status: 'active'
    }
  },
  {
    name: 'shippingLabels', 
    document: 'default',
    data: {
      created: new Date(),
      type: 'system'
    }
  },
  {
    name: 'walletReports',
    document: 'default', 
    data: {
      created: new Date(),
      type: 'system'
    }
  },
  {
    name: 'universityAnalytics',
    document: 'default',
    data: {
      created: new Date(),
      type: 'system'
    }
  },
  {
    name: 'systemMetrics',
    document: 'default',
    data: {
      created: new Date(),
      type: 'system'
    }
  },
  {
    name: 'adminLogs',
    document: 'default',
    data: {
      created: new Date(),
      type: 'system'
    }
  }
];

// Instructions:
// 1. Go to Firestore Database in Firebase Console
// 2. For each collection above:
//    - Click "Start collection"
//    - Enter the collection name
//    - Enter the document ID
//    - Add the fields from data object
//    - Click Save
