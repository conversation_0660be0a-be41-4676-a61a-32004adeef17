import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { AuthProvider } from '@/contexts/AuthContext'
import { mockUser } from '../mocks/firebase'
import { vi } from 'vitest'

// Create a wrapper component that includes all providers
function createTestWrapper() {
  return ({ children }: { children: React.ReactNode }) => {
    return (
      <BrowserRouter>
        <AuthProvider>
          {children}
        </AuthProvider>
      </BrowserRouter>
    )
  }
}

// Custom render function that includes providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: createTestWrapper(), ...options })

// Re-export testing library functions (without using export *)
export {
  screen,
  waitFor,
  fireEvent,
  act,
  cleanup,
  renderHook,
  within,
} from '@testing-library/react'

// Export custom render
export { customRender as render }

// Test utilities
export const mockAuthContext = {
  user: mockUser,
  loading: false,
  signIn: vi.fn(),
  signUp: vi.fn(),
  signOut: vi.fn(),
  updateUserProfile: vi.fn(),
  resetPassword: vi.fn(),
}

export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 100))
}

// Helper to mock router navigation
export const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

// Helper to create mock events
export const createMockEvent = (type: string, data: Record<string, unknown> = {}) => ({
  type,
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: '', ...data },
  currentTarget: { value: '', ...data },
  ...data,
})