// Ultra-minimal index.ts to avoid deployment timeouts
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin only once
if (!admin.apps.length) {
  admin.initializeApp();
}

// Essential admin fix function only
export const fixAdminUser = functions.https.onCall(async (_data, _context) => {
  try {
    const adminEmail = '<EMAIL>';
    const userRecord = await admin.auth().getUserByEmail(adminEmail);

    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin'
    });

    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });

    return {
      success: true,
      message: `Admin user fixed for ${adminEmail}`,
      uid: userRecord.uid
    };
  } catch (error) {
    console.error('Error fixing admin user:', error);
    throw new functions.https.HttpsError('internal', 'Failed to fix admin user');
  }
});

// Quick HTTP function for immediate access
export const quickFixAdmin = functions.https.onRequest(async (_req, res) => {
  try {
    const adminEmail = '<EMAIL>';
    const userRecord = await admin.auth().getUserByEmail(adminEmail);

    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin'
    });

    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });

    res.json({
      success: true,
      message: `Admin user fixed for ${adminEmail}`,
      uid: userRecord.uid,
      customClaims: { admin: true, role: 'admin' }
    });
  } catch (error: any) {
    console.error('Error:', error);
    res.status(500).json({ error: error.message || 'Unknown error' });
  }
});