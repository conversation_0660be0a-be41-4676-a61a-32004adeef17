import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { environmentConfig } from '../config/environment';

// Environment variables
const SENTRY_DSN = environmentConfig.sentry.dsn || process.env.SENTRY_DSN;
const ENVIRONMENT = environmentConfig.environment || 'development';
const RELEASE = environmentConfig.app.version || 'unknown';

/**
 * Initialize Sentry for Firebase Functions
 */
export const initSentry = () => {
  if (!SENTRY_DSN) {
    console.warn('Sentry DSN not configured. Skipping Sentry initialization.');
    return;
  }

  Sentry.init({
    dsn: SENTRY_DSN,
    environment: ENVIRONMENT,
    release: RELEASE,
    
    // Performance monitoring
    tracesSampleRate: ENVIRONMENT === 'production' ? 0.1 : 1.0,
    
    // Profiling
    profilesSampleRate: ENVIRONMENT === 'production' ? 0.1 : 1.0,
    
    // Integrations
    integrations: [
      nodeProfilingIntegration(),
      Sentry.httpIntegration(),
      Sentry.onUncaughtExceptionIntegration({
        exitEvenIfOtherHandlersAreRegistered: false,
      }),
      Sentry.onUnhandledRejectionIntegration({
        mode: 'warn',
      }),
    ],

    // Filter out sensitive data
    beforeSend(event: any, _hint: any) {
      // Remove sensitive headers
      if (event.request?.headers) {
        delete event.request.headers['authorization'];
        delete event.request.headers['cookie'];
        delete event.request.headers['x-api-key'];
      }

      // Remove sensitive data from extra context
      if (event.extra) {
        delete event.extra.password;
        delete event.extra.token;
        delete event.extra.secret;
      }

      // Filter out health check and monitoring requests
      if (event.request?.url?.includes('/health') || 
          event.request?.url?.includes('/ping') ||
          event.request?.url?.includes('/metrics')) {
        return null;
      }

      return event;
    },

    // Filter performance transactions
    beforeSendTransaction(event: any) {
      // Filter out health check transactions
      if (event.transaction?.includes('health') || 
          event.transaction?.includes('ping') ||
          event.transaction?.includes('metrics')) {
        return null;
      }

      return event;
    },

    // Debug mode for development
    debug: ENVIRONMENT === 'development',

    // Server name for better grouping
    serverName: 'hive-campus-functions',

    // Additional tags
    initialScope: {
      tags: {
        component: 'firebase-functions',
        service: 'hive-campus-marketplace',
      },
    },
  });

  console.log(`Sentry initialized for environment: ${ENVIRONMENT}`);
};

/**
 * Set user context for Sentry
 */
export const setSentryUser = (userId: string, userData?: Record<string, unknown>) => {
  Sentry.setUser({
    id: userId,
    ...userData,
  });
};

/**
 * Set function context for better error tracking
 */
export const setFunctionContext = (functionName: string, data?: Record<string, unknown>) => {
  Sentry.setContext('function', {
    name: functionName,
    timestamp: new Date().toISOString(),
    ...data,
  });
};

/**
 * Capture exception with context
 */
export const captureException = (
  error: Error,
  context?: Record<string, unknown>,
  functionName?: string
) => {
  Sentry.withScope((scope: any) => {
    if (functionName) {
      scope.setTag('function', functionName);
    }

    if (context) {
      scope.setContext('additional_context', context);
    }

    // Set level based on error type
    if (error.name === 'ValidationError') {
      scope.setLevel('warning');
    } else if (error.name === 'AuthenticationError') {
      scope.setLevel('info');
    } else {
      scope.setLevel('error');
    }

    Sentry.captureException(error);
  });
};

/**
 * Capture custom event
 */
export const captureEvent = (
  message: string,
  level: 'info' | 'warning' | 'error' = 'info',
  extra?: Record<string, unknown>
) => {
  Sentry.captureEvent({
    message,
    level,
    extra,
    timestamp: Date.now() / 1000,
  });
};

/**
 * Start a performance transaction
 */
export const startTransaction = (name: string, operation: string = 'function') => {
  return Sentry.startSpan({
    name,
    op: operation,
  }, () => {});
};

/**
 * Sentry error handler middleware for Express
 */
export const sentryErrorHandler = () => {
  return Sentry.expressErrorHandler({
    shouldHandleError(error: any) {
      // Only handle server errors (5xx) and authentication errors
      return error.status >= 500 || error.status === 401;
    },
  });
};

/**
 * Sentry request handler middleware for Express
 */
export const sentryRequestHandler = () => {
  return (req: any, _res: any, next: any) => {
    // Simple middleware that adds Sentry context
    Sentry.setContext('request', {
      url: req.url,
      method: req.method,
    });
    next();
  };
};

/**
 * Sentry tracing handler middleware for Express
 */
export const sentryTracingHandler = () => {
  return (req: any, _res: any, next: any) => {
    // Simple tracing middleware
    Sentry.setTag('route', req.route?.path || req.url);
    next();
  };
};

// Export Sentry for direct access if needed
export { Sentry };