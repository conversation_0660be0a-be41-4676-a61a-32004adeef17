{"name": "functions", "scripts": {"build": "tsc --incremental", "build:clean": "rmdir /s /q lib 2>nul & tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "deploy:minimal": "firebase deploy --only functions:fixAdminUser,functions:quickFixAdmin", "logs": "firebase functions:log", "test": "mocha --require ts-node/register 'src/**/*.test.ts'", "test:watch": "npm run test -- --watch", "test:coverage": "nyc npm run test", "test:emulator": "firebase emulators:exec --only functions 'npm run test'"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@sentry/node": "^9.34.0", "@sentry/profiling-node": "^9.34.0", "@types/stripe": "^8.0.416", "axios": "^1.6.2", "busboy": "^1.6.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^4.18.2", "firebase-admin": "^12.7.0", "firebase-functions": "^6.3.2", "nodemailer": "^6.9.7", "shippo": "^1.7.1", "stripe": "^18.2.1"}, "devDependencies": {"@types/busboy": "^1.5.0", "@types/chai": "^4.3.16", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/mocha": "^10.0.7", "@types/node": "^18.0.0", "@types/nodemailer": "^6.4.14", "@types/shippo": "^1.7.8", "@types/sinon": "^17.0.3", "chai": "^4.4.1", "firebase-functions-test": "^3.1.0", "mocha": "^10.5.2", "nyc": "^15.1.0", "sinon": "^18.0.0", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "private": true}