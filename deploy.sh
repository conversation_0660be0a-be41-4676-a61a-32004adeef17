#!/bin/bash

# Hive Campus Deployment Script
set -e

echo "🚀 Starting Hive Campus deployment..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
ENVIRONMENT="production"
SKIP_BUILD=false
FUNCTIONS_ONLY=false
HOSTING_ONLY=false

while [[ $# -gt 0 ]]; do
  case $1 in
    -e|--environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    --skip-build)
      SKIP_BUILD=true
      shift
      ;;
    --functions-only)
      FUNCTIONS_ONLY=true
      shift
      ;;
    --hosting-only)
      HOSTING_ONLY=true
      shift
      ;;
    -h|--help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  -e, --environment    Set environment (production|staging) [default: production]"
      echo "  --skip-build        Skip the build process"
      echo "  --functions-only    Deploy only Firebase Functions"
      echo "  --hosting-only      Deploy only Firebase Hosting"
      echo "  -h, --help          Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option $1"
      exit 1
      ;;
  esac
done

echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"

# Validate environment
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
  echo -e "${RED}Error: Environment must be 'production' or 'staging'${NC}"
  exit 1
fi

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
  echo -e "${RED}Error: Firebase CLI is not installed${NC}"
  echo "Install it with: npm install -g firebase-tools"
  exit 1
fi

# Login check
if ! firebase projects:list &> /dev/null; then
  echo -e "${YELLOW}Please login to Firebase:${NC}"
  firebase login
fi

# Set Firebase project based on environment
if [[ "$ENVIRONMENT" == "staging" ]]; then
  FIREBASE_PROJECT="hivecampus-staging"
else
  FIREBASE_PROJECT="hivecampus-prod"
fi

echo -e "${BLUE}Using Firebase project: ${FIREBASE_PROJECT}${NC}"

# Build process
if [[ "$SKIP_BUILD" == false ]]; then
  echo -e "${YELLOW}📦 Installing dependencies...${NC}"
  npm ci
  
  echo -e "${YELLOW}📦 Installing Functions dependencies...${NC}"
  cd functions
  npm ci
  cd ..
  
  if [[ "$FUNCTIONS_ONLY" == false ]]; then
    echo -e "${YELLOW}🏗️  Building frontend...${NC}"
    npm run build
  fi
  
  if [[ "$HOSTING_ONLY" == false ]]; then
    echo -e "${YELLOW}🏗️  Building functions...${NC}"
    cd functions
    npm run build
    cd ..
  fi
else
  echo -e "${YELLOW}⏭️  Skipping build process...${NC}"
fi

# Deploy based on options
if [[ "$FUNCTIONS_ONLY" == true ]]; then
  echo -e "${YELLOW}🚀 Deploying Firebase Functions only...${NC}"
  firebase deploy --only functions --project "$FIREBASE_PROJECT"
elif [[ "$HOSTING_ONLY" == true ]]; then
  echo -e "${YELLOW}🚀 Deploying Firebase Hosting only...${NC}"
  firebase deploy --only hosting --project "$FIREBASE_PROJECT"
else
  echo -e "${YELLOW}🚀 Deploying full application...${NC}"
  firebase deploy --project "$FIREBASE_PROJECT"
fi

# Set Firebase Functions config for production
if [[ "$ENVIRONMENT" == "production" && "$HOSTING_ONLY" == false ]]; then
  echo -e "${YELLOW}⚙️  Setting production environment variables...${NC}"
  
  # Note: These should be set manually for security
  echo -e "${BLUE}Don't forget to set the following Firebase Functions config:${NC}"
  echo "firebase functions:config:set stripe.api_key=\"YOUR_STRIPE_SECRET_KEY\" --project $FIREBASE_PROJECT"
  echo "firebase functions:config:set stripe.webhook_secret=\"YOUR_STRIPE_WEBHOOK_SECRET\" --project $FIREBASE_PROJECT"
  echo "firebase functions:config:set openai.api_key=\"YOUR_OPENAI_API_KEY\" --project $FIREBASE_PROJECT"
  echo "firebase functions:config:set slack.webhook_url=\"YOUR_SLACK_WEBHOOK_URL\" --project $FIREBASE_PROJECT"
  echo "firebase functions:config:set shippo.api_key=\"YOUR_SHIPPO_API_KEY\" --project $FIREBASE_PROJECT"
  echo "firebase functions:config:set app.url=\"https://hivecampus.app\" --project $FIREBASE_PROJECT"
fi

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"

# Post-deployment checks
echo -e "${YELLOW}🔍 Running post-deployment checks...${NC}"

if [[ "$HOSTING_ONLY" == false ]]; then
  echo -e "${BLUE}Functions deployed:${NC}"
  firebase functions:list --project "$FIREBASE_PROJECT" | head -10
fi

if [[ "$FUNCTIONS_ONLY" == false ]]; then
  echo -e "${BLUE}Hosting URL:${NC}"
  firebase hosting:sites:list --project "$FIREBASE_PROJECT"
fi

echo -e "${GREEN}🎉 Hive Campus deployment completed!${NC}"