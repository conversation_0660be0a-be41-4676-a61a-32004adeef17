import React, { useState } from 'react';
import { Mail, Loader, CheckCircle, AlertCircle } from 'lucide-react';
import { sendEmailVerification } from 'firebase/auth';
import { auth } from '../firebase/config';

interface ResendVerificationEmailProps {
  userEmail?: string;
  onSuccess?: () => void;
  className?: string;
}

const ResendVerificationEmail: React.FC<ResendVerificationEmailProps> = ({
  userEmail,
  onSuccess,
  className = ''
}) => {
  const [isResending, setIsResending] = useState(false);
  const [resendStatus, setResendStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [lastSentTime, setLastSentTime] = useState<number | null>(null);

  // Cooldown period in seconds (60 seconds)
  const COOLDOWN_PERIOD = 60;

  const canResend = () => {
    if (!lastSentTime) return true;
    const timeSinceLastSent = (Date.now() - lastSentTime) / 1000;
    return timeSinceLastSent >= COOLDOWN_PERIOD;
  };

  const getTimeRemaining = () => {
    if (!lastSentTime) return 0;
    const timeSinceLastSent = (Date.now() - lastSentTime) / 1000;
    return Math.max(0, COOLDOWN_PERIOD - timeSinceLastSent);
  };

  const handleResendVerification = async () => {
    if (!canResend() || isResending) return;

    setIsResending(true);
    setResendStatus('idle');
    setErrorMessage('');

    try {
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        throw new Error('No user is currently signed in');
      }

      // Send verification email using Firebase Auth
      await sendEmailVerification(currentUser, {
        url: `${window.location.origin}/verify-email`,
        handleCodeInApp: true,
      });

      setResendStatus('success');
      setLastSentTime(Date.now());
      
      if (onSuccess) {
        onSuccess();
      }

      // Reset success status after 5 seconds
      setTimeout(() => {
        setResendStatus('idle');
      }, 5000);

    } catch (error) {
      console.error('Error resending verification email:', error);
      setResendStatus('error');
      
      if (error instanceof Error) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Failed to resend verification email. Please try again.');
      }

      // Reset error status after 5 seconds
      setTimeout(() => {
        setResendStatus('idle');
        setErrorMessage('');
      }, 5000);
    } finally {
      setIsResending(false);
    }
  };

  const timeRemaining = Math.ceil(getTimeRemaining());

  return (
    <div className={`${className}`}>
      {/* Resend Button */}
      <button
        onClick={handleResendVerification}
        disabled={!canResend() || isResending}
        className={`
          flex items-center justify-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
          ${canResend() && !isResending
            ? 'bg-primary-600 hover:bg-primary-700 text-white shadow-md hover:shadow-lg'
            : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }
          ${resendStatus === 'success' ? 'bg-green-600 hover:bg-green-700' : ''}
          ${resendStatus === 'error' ? 'bg-red-600 hover:bg-red-700' : ''}
        `}
      >
        {isResending ? (
          <>
            <Loader className="w-4 h-4 animate-spin" />
            <span>Sending...</span>
          </>
        ) : resendStatus === 'success' ? (
          <>
            <CheckCircle className="w-4 h-4" />
            <span>Email Sent!</span>
          </>
        ) : resendStatus === 'error' ? (
          <>
            <AlertCircle className="w-4 h-4" />
            <span>Failed to Send</span>
          </>
        ) : (
          <>
            <Mail className="w-4 h-4" />
            <span>
              {canResend() 
                ? 'Resend Verification Email' 
                : `Resend in ${timeRemaining}s`
              }
            </span>
          </>
        )}
      </button>

      {/* Status Messages */}
      {resendStatus === 'success' && (
        <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0" />
            <p className="text-sm text-green-800 dark:text-green-200">
              Verification email sent successfully! Please check your inbox
              {userEmail && ` at ${userEmail}`}.
            </p>
          </div>
        </div>
      )}

      {resendStatus === 'error' && errorMessage && (
        <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0" />
            <p className="text-sm text-red-800 dark:text-red-200">
              {errorMessage}
            </p>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
        <p>
          Didn't receive the email? Check your spam folder or try resending.
          {userEmail && (
            <>
              <br />
              <span className="font-medium">Sending to: {userEmail}</span>
            </>
          )}
        </p>
      </div>
    </div>
  );
};

export default ResendVerificationEmail;
