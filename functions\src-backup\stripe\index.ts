import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import express, { Request, Response } from 'express';
import cors from 'cors';
import { stripe, stripeConfig } from './config';
import { 
  createCheckoutSession, 
  handlePaymentSucceeded, 
  releaseFundsWithCode, 
  autoReleaseF<PERSON>, 
  createConnectAccount
} from './service';
import { createShippingLabel } from './shippo';
import { CreateCheckoutSessionRequest, ReleaseFundsRequest } from './types';

// Initialize Express app
const app = express();
app.use(cors({ origin: true }));

// Global error handler to ensure JSON responses
app.use((err: Error, _req: Request, res: Response, _next: express.NextFunction) => {
  console.error('Unhandled error in stripe API:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// Middleware to verify Firebase authentication
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const validateFirebaseAuth = async (req: functions.https.Request, res: express.Response, next: express.NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(403).json({ error: 'Unauthorized' });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Add the user ID to the request
    req.body.userId = decodedToken.uid;
    
    next();
  } catch (error) {
    console.error('Error verifying Firebase token:', error);
    res.status(403).json({ error: 'Unauthorized' });
  }
};

// Create a checkout session
app.post('/create-checkout-session', async (req: Request, res: Response) => {
  try {
    // Check authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(403).json({ error: 'Unauthorized' });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Parse request body
    const requestBody = req.body as unknown;
    if (typeof requestBody !== 'object' || requestBody === null) {
      res.status(400).json({ error: 'Invalid request body' });
      return;
    }
    
    const { listingId, buyerId, useWalletBalance } = requestBody as CreateCheckoutSessionRequest;
    
    // Validate that the authenticated user is the buyer
    if (decodedToken.uid !== buyerId) {
      res.status(403).json({ error: 'You can only create checkout sessions for yourself' });
      return;
    }
    
    const session = await createCheckoutSession(listingId, buyerId, useWalletBalance);
    res.status(200).json(session);
  } catch (error: unknown) {
    console.error('Error creating checkout session:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
});

// Release funds with secret code
app.post('/release-funds', async (req: Request, res: Response) => {
  try {
    // Check authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(403).json({ error: 'Unauthorized' });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Parse request body
    const requestBody = req.body as unknown;
    if (typeof requestBody !== 'object' || requestBody === null) {
      res.status(400).json({ error: 'Invalid request body' });
      return;
    }
    
    const { orderId, secretCode } = requestBody as ReleaseFundsRequest;
    
    // Get the order to verify the buyer
    const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      res.status(404).json({ error: 'Order not found' });
      return;
    }
    
    const order = orderDoc.data();
    
    // Validate that the authenticated user is the buyer
    if (decodedToken.uid !== order?.buyerId) {
      res.status(403).json({ error: 'You can only release funds for your own orders' });
      return;
    }
    
    const success = await releaseFundsWithCode(orderId, secretCode);
    res.status(200).json({ success });
  } catch (error: unknown) {
    console.error('Error releasing funds:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
});

// Create a Connect account
app.post('/create-connect-account', async (req: Request, res: Response) => {
  try {
    // Check authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(403).json({ error: 'Unauthorized' });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Parse request body
    const requestBody = req.body as unknown;
    if (typeof requestBody !== 'object' || requestBody === null) {
      res.status(400).json({ error: 'Invalid request body' });
      return;
    }
    
    const { accountType } = requestBody as { accountType: 'student' | 'merchant' };
    const userId = decodedToken.uid;
    
    const account = await createConnectAccount(userId, accountType);
    res.status(200).json(account);
  } catch (error: unknown) {
    console.error('Error creating Connect account:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
});

// Generate a shipping label
app.post('/generate-shipping-label', async (req: Request, res: Response) => {
  try {
    // Check authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(403).json({ error: 'Unauthorized' });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Parse request body
    const requestBody = req.body as unknown;
    if (typeof requestBody !== 'object' || requestBody === null) {
      res.status(400).json({ error: 'Invalid request body' });
      return;
    }
    
    const { orderId } = requestBody as { orderId: string };
    
    // Get the order to verify the seller
    const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      res.status(404).json({ error: 'Order not found' });
      return;
    }
    
    const order = orderDoc.data();
    
    // Validate that the authenticated user is the seller
    if (decodedToken.uid !== order?.sellerId) {
      res.status(403).json({ error: 'You can only generate shipping labels for your own orders' });
      return;
    }
    
    const label = await createShippingLabel(orderId);
    res.status(200).json(label);
  } catch (error: unknown) {
    console.error('Error generating shipping label:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
});

// Stripe webhook handler
app.post('/webhook', express.raw({ type: 'application/json' }), async (req: Request, res: Response) => {
  try {
    const sig = req.headers['stripe-signature'] as string;
    
    if (!sig || !stripeConfig.webhookSecret) {
      res.status(400).json({ error: 'Missing Stripe signature or webhook secret' });
      return;
    }
    
    // Verify the webhook signature
    let event;
    try {
      event = stripe.webhooks.constructEvent(
        req.body,
        sig,
        stripeConfig.webhookSecret
      );
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error(`Webhook signature verification failed: ${errorMessage}`);
      res.status(400).json({ error: `Webhook Error: ${errorMessage}` });
      return;
    }
    
    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as unknown as Record<string, unknown>);
        break;
      case 'account.updated': {
        // Handle Connect account updates
        const account = event.data.object as unknown as { 
          metadata?: { userId?: string }, 
          charges_enabled?: boolean, 
          payouts_enabled?: boolean 
        };
        
        if (account.metadata?.userId) {
          // Update the Connect account status in Firestore
          if (account.charges_enabled && account.payouts_enabled) {
            await admin.firestore().collection('connectAccounts')
              .doc(account.metadata.userId)
              .update({
                isOnboarded: true,
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
              });
          }
        }
        break;
      }
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
    
    res.status(200).json({ received: true });
  } catch (error: unknown) {
    console.error('Error handling webhook:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
});

// Catch-all route for unmatched endpoints
app.all('*', (req: Request, res: Response) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.path,
    method: req.method
  });
});

// Export the Express app as a Firebase Function
export const stripeApi = functions.https.onRequest(app);

// Scheduled function to auto-release funds after escrow period
export const scheduleAutoReleaseFunds = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async () => {
    try {
      await autoReleaseFunds();
      return null;
    } catch (error) {
      console.error('Error in scheduled auto-release funds:', error);
      return null;
    }
  });

// Function to handle order status changes
export const onOrderStatusChange = functions.firestore
  .document('orders/{orderId}')
  .onUpdate(async (change) => {
    try {
      const before = change.before.data();
      const after = change.after.data();
      
      // If the status changed to 'delivered', set the auto-release date
      if (before.status !== 'delivered' && after.status === 'delivered') {
        const autoReleaseDate = new Date();
        autoReleaseDate.setDate(autoReleaseDate.getDate() + stripeConfig.autoReleaseEscrowDays);
        
        await change.after.ref.update({
          autoReleaseDate: admin.firestore.Timestamp.fromDate(autoReleaseDate),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
      }
      
      return null;
    } catch (error) {
      console.error('Error handling order status change:', error);
      return null;
    }
  });
