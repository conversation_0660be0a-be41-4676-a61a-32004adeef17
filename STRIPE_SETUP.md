# Hive Campus Stripe Integration Setup

This document provides instructions for setting up the Stripe integration for the Hive Campus marketplace app.

## Prerequisites

1. A Stripe account (create one at [stripe.com](https://stripe.com) if you don't have one)
2. Firebase project with Firestore and Firebase Functions enabled
3. Node.js and npm installed

## Setup Steps

### 1. Stripe Account Setup

1. Create a Stripe account or log in to your existing account
2. Go to the Stripe Dashboard > Developers > API keys
3. Copy your Secret Key (starts with `sk_test_` for test mode or `sk_live_` for production)
4. Enable Stripe Connect in your Stripe Dashboard (Dashboard > Connect > Settings)

### 2. Firebase Functions Environment Variables

1. Copy the `.env.example` file to `.env` in the `functions` directory
2. Update the `.env` file with your Stripe API keys and webhook secret
3. Set up Firebase Functions environment variables:

```bash
cd functions
firebase functions:config:set stripe.secret_key="sk_test_your_stripe_secret_key" stripe.webhook_secret="whsec_your_stripe_webhook_secret" app.url="https://your-app-url.com"
```

### 3. Stripe Webhook Setup

1. Go to the Stripe Dashboard > Developers > Webhooks
2. Add an endpoint with the URL of your Firebase Function webhook:
   - For local development: Use [Stripe CLI](https://stripe.com/docs/stripe-cli) to forward webhooks
   - For production: `https://your-region-your-project-id.cloudfunctions.net/stripeApi/webhook`
3. Add the following events to listen for:
   - `payment_intent.succeeded`
   - `account.updated`

### 4. Deploy Firebase Functions

```bash
cd functions
npm install
npm run deploy
```

## Testing the Integration

### Test Stripe Connect Account Creation

1. Log in to the Hive Campus app
2. Go to Settings > Payment Settings
3. Click "Create Student Seller Account" or "Create Merchant Partner Account"
4. Complete the Stripe Connect onboarding process

### Test Checkout Flow

1. Create a listing in the app
2. As another user, view the listing and click "Buy Now"
3. Complete the checkout process
4. Verify that the order is created in Firestore
5. Verify that the payment is captured in Stripe

### Test Secret Code Verification

1. After a successful purchase, go to the Order Tracking page
2. Enter the secret code (in a real app, this would be sent to the buyer)
3. Verify that the funds are released to the seller

## Firestore Collections

The Stripe integration uses the following Firestore collections:

- `/orders/{orderId}` - Order information
- `/wallets/{userId}` - User wallet balances
- `/shippingLabels/{orderId}` - Shipping label information
- `/codes/{orderId}` - Secret codes for order verification
- `/connectAccounts/{userId}` - Stripe Connect account information

## Commission Rates

- Textbooks and course materials: 8% (including Stripe fees)
- All other categories: 10% (including Stripe fees)

## Wallet and Cashback

- Buyers earn 2% cashback on all purchases
- Wallet balance can be applied to future purchases
- Minimum Stripe charge is $0.50, so wallet balance cannot be used for the entire purchase amount

## Escrow and Fund Release

- Funds are held in escrow until the buyer confirms receipt
- Buyer enters a 6-digit secret code to release funds
- If the buyer doesn't enter the code, funds are automatically released after 3 days