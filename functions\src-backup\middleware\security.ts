import { Request, Response, NextFunction } from 'express';

/**
 * Security middleware to add security headers to all API responses
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent content type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // XSS Protection
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer Policy - Send full URL when same-origin, only origin when cross-origin HTTPS, nothing for HTTP
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Permissions Policy - Restrict access to sensitive browser features
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), payment=*');
  
  // Strict Transport Security - Force HTTPS
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  
  // Content Security Policy is handled by Firebase Hosting headers
  // Don't override the main CSP policy here
  
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  // Cache control for API responses
  if (req.path.includes('/api/')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
  }
  
  next();
};

/**
 * CORS middleware with security-focused configuration
 */
export const secureCors = (req: Request, res: Response, next: NextFunction) => {
  const allowedOrigins = [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'https://staging-hivecampus.web.app',
    'http://localhost:5173', // Development
    'http://localhost:3000', // Development
    'http://localhost:5000'  // Firebase hosting emulator
  ];
  
  const origin = req.headers.origin;
  
  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control'
  );
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }
  
  next();
};

/**
 * Rate limiting middleware to prevent abuse
 */
export const rateLimit = (windowMs: number = 15 * 60 * 1000, max: number = 100) => {
  const requests = new Map<string, { count: number; timestamp: number }>();
  
  return (req: Request, res: Response, next: NextFunction) => {
    const clientId = req.ip || req.headers['x-forwarded-for'] || 'unknown';
    const now = Date.now();
    
    // Clean up old entries
    for (const [key, value] of requests.entries()) {
      if (now - value.timestamp > windowMs) {
        requests.delete(key);
      }
    }
    
    const clientData = requests.get(clientId as string);
    
    if (!clientData) {
      requests.set(clientId as string, { count: 1, timestamp: now });
    } else if (now - clientData.timestamp < windowMs) {
      clientData.count++;
      if (clientData.count > max) {
        res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.ceil((windowMs - (now - clientData.timestamp)) / 1000)
        });
        return;
      }
    } else {
      requests.set(clientId as string, { count: 1, timestamp: now });
    }
    
    next();
  };
};

/**
 * Input validation middleware
 */
export const validateInput = (req: Request, res: Response, next: NextFunction) => {
  // Check for common injection patterns
  const dangerousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /data:text\/html/gi,
    /vbscript:/gi
  ];
  
  const checkObject = (obj: unknown): boolean => {
    if (typeof obj === 'string') {
      return dangerousPatterns.some(pattern => pattern.test(obj));
    }
    
    if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).some(value => checkObject(value));
    }
    
    return false;
  };
  
  // Check request body, query parameters, and URL parameters
  const hasXSS = [req.body, req.query, req.params].some(data => 
    data && checkObject(data)
  );
  
  if (hasXSS) {
    res.status(400).json({
      error: 'Invalid input detected',
      message: 'Request contains potentially malicious content'
    });
    return;
  }
  
  next();
};

/**
 * Request size limiter
 */
export const limitRequestSize = (maxSize: string = '10mb') => {
  const maxBytes = parseSize(maxSize);
  
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.headers['content-length'];
    
    if (contentLength && parseInt(contentLength) > maxBytes) {
      res.status(413).json({
        error: 'Request too large',
        maxSize: maxSize
      });
      return;
    }
    
    next();
  };
};

/**
 * Helper function to parse size strings
 */
function parseSize(size: string): number {
  const units: { [key: string]: number } = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  if (!match) throw new Error('Invalid size format');
  
  const [, num, unit = 'b'] = match;
  return parseFloat(num) * units[unit];
}

/**
 * Security logging middleware
 */
export const securityLogger = (req: Request, res: Response, next: NextFunction) => {
  // Log suspicious requests
  const suspiciousPatterns = [
    '/admin',
    '/config',
    '/.env',
    '/backup',
    '/wp-admin',
    '/phpmyadmin'
  ];
  
  const isSuspicious = suspiciousPatterns.some(pattern => 
    req.path.toLowerCase().includes(pattern)
  );
  
  if (isSuspicious) {
    console.warn(`Suspicious request: ${req.method} ${req.path} from ${req.ip}`);
  }
  
  // Log failed authentication attempts
  if (req.path.includes('/auth') && req.method === 'POST') {
    const originalEnd = res.end;
    
    // Create a wrapper that matches the original function signature
    const wrappedEnd = function(this: Response, chunk?: string | Buffer, encoding?: BufferEncoding | (() => void), cb?: (() => void)) {
      if (res.statusCode >= 400) {
        console.warn(`Failed auth attempt: ${req.path} from ${req.ip} - Status: ${res.statusCode}`);
      }
      return originalEnd.call(this, chunk, encoding as BufferEncoding, cb);
    };
    
    res.end = wrappedEnd as typeof res.end;
  }
  
  next();
};
