# Deploy Wallet Functions Script
# This script deploys the wallet functions to fix CORS errors

Write-Host "🚀 Deploying Wallet Functions..." -ForegroundColor Blue
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "functions")) {
    Write-Host "❌ Error: functions directory not found" -ForegroundColor Red
    Write-Host "Please run this script from the project root directory" -ForegroundColor Yellow
    exit 1
}

# Navigate to functions directory
Push-Location "functions"

try {
    # Install dependencies
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    npm install cors
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to install cors package"
    }
    
    npm install
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to install dependencies"
    }
    
    # Build functions
    Write-Host "🔨 Building functions..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to build functions"
    }
    
    # Deploy functions
    Write-Host "🚀 Deploying to Firebase..." -ForegroundColor Yellow
    firebase deploy --only functions
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to deploy functions"
    }
    
    Write-Host ""
    Write-Host "✅ Wallet functions deployed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next Steps:" -ForegroundColor Blue
    Write-Host "1. Test the wallet page - should load without CORS errors" -ForegroundColor White
    Write-Host "2. Log into admin panel and go to 'Wallet Settings'" -ForegroundColor White
    Write-Host "3. Configure signup and referral bonuses as desired" -ForegroundColor White
    Write-Host "4. Test with new user accounts" -ForegroundColor White
    Write-Host ""
    Write-Host "🎯 Default Configuration:" -ForegroundColor Blue
    Write-Host "- Signup bonus: DISABLED ($0)" -ForegroundColor White
    Write-Host "- Referral bonus: DISABLED ($0)" -ForegroundColor White
    Write-Host "- Admin has full control via Wallet Settings page" -ForegroundColor White
    
} catch {
    Write-Host ""
    Write-Host "❌ Deployment failed: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Make sure you're logged into Firebase: firebase login" -ForegroundColor White
    Write-Host "2. Check your Firebase project: firebase use --list" -ForegroundColor White
    Write-Host "3. Verify functions directory exists and has package.json" -ForegroundColor White
    Write-Host "4. Try running commands manually:" -ForegroundColor White
    Write-Host "   cd functions" -ForegroundColor Gray
    Write-Host "   npm install" -ForegroundColor Gray
    Write-Host "   npm run build" -ForegroundColor Gray
    Write-Host "   firebase deploy --only functions" -ForegroundColor Gray
} finally {
    # Return to original directory
    Pop-Location
}

Write-Host ""
Write-Host "📚 For more details, see WALLET_CORS_FIX.md" -ForegroundColor Cyan
