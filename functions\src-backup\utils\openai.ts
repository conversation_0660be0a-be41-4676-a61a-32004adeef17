import axios from 'axios';
import * as functions from 'firebase-functions';

// Define interfaces for data structures used in reports
interface ErrorDetail {
  type: string;
  message: string;
  route: string;
  userId?: string;
  timestamp: string;
  sentryEventId?: string;
}

interface PageInfo {
  route: string;
  duration?: number;
  count?: number;
  timestamp?: string;
}

interface ResourceInfo {
  url: string;
  type: string;
  duration: number;
}

interface UserQuote {
  message: string;
  type: string;
  category: string;
  route: string;
}

interface ReportData {
  timeRange: {
    start: string;
    end: string;
  };
  summary: {
    totalEvents: number;
    totalErrors: number;
    totalFeedback: number;
    totalPageViews: number;
    totalInteractions: number;
  };
  errors: {
    count: number;
    byType: Record<string, number>;
    details: ErrorDetail[];
  };
  performance: {
    issues: number;
    slowestPages: PageInfo[];
    slowestResources: ResourceInfo[];
  };
  userBehavior: {
    topPages: PageInfo[];
    checkoutFunnel: {
      started: number;
      completed: number;
      failed: number;
      dropOffRate: string;
    };
  };
  feedback: {
    positive: number;
    negative: number;
    neutral: number;
    byCategory: Record<string, unknown[]>;
    userQuotes: UserQuote[];
  };
}

// Define interfaces for OpenAI data structures
interface ReportInsights {
  keyFindings?: string[];
  recommendations?: string[];
  trends?: Record<string, unknown>;
  [key: string]: unknown;
}

interface OpenAIOptions {
  model?: string;
  includeJson?: boolean;
  temperature?: number;
}

interface OpenAISummaryResult {
  summary: string;
  insights?: ReportInsights;
}

// OpenAI API configuration
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';
const DEFAULT_MODEL = 'gpt-4'; // Can be downgraded to gpt-3.5-turbo if needed

/**
 * Interface for OpenAI API response
 */
interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * Generate an AI summary using OpenAI's API
 * 
 * @param reportData The structured report data
 * @param options Configuration options
 * @returns An object containing the summary text and structured insights
 */
export async function generateOpenAISummary(
  reportData: ReportData,
  options: OpenAIOptions = {}
): Promise<OpenAISummaryResult> {
  // Check if OpenAI API key is configured
  if (!OPENAI_API_KEY) {
    throw new Error('OpenAI API key is not configured. Set the OPENAI_API_KEY environment variable.');
  }

  try {
    // Prepare the prompt for OpenAI
    const prompt = createOpenAIPrompt(reportData, options.includeJson);
    
    // Make the API request
    const response = await axios.post<OpenAIResponse>(
      OPENAI_API_URL,
      {
        model: options.model || DEFAULT_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are ReeFlex, an intelligent observability agent for a React application called Hive Campus. Your task is to analyze app usage data and provide actionable insights to the app owner.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: options.temperature || 0.3,
        max_tokens: 2000
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        }
      }
    );

    // Extract the response content
    const content = response.data.choices[0].message.content;
    
    // If JSON output was requested, try to parse it
    if (options.includeJson) {
      try {
        // Extract the JSON part from the response
        const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/);
        if (jsonMatch && jsonMatch[1]) {
          const jsonContent = jsonMatch[1];
          const insights = JSON.parse(jsonContent);
          
          // Return both the text summary and structured insights
          return {
            summary: content.replace(/```json\n[\s\S]*?\n```/, '').trim(),
            insights
          };
        }
      } catch (jsonError) {
        functions.logger.warn('Failed to parse JSON insights from OpenAI response', jsonError);
      }
    }
    
    // Return just the text summary if JSON parsing failed or wasn't requested
    return { summary: content };
  } catch (error) {
    functions.logger.error('Error generating OpenAI summary:', error);
    throw new Error(`Failed to generate AI summary: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Create a prompt for the OpenAI API based on the report data
 */
function createOpenAIPrompt(reportData: ReportData, includeJson: boolean = false): string {
  // Format the time range
  const timeRange = `${new Date(reportData.timeRange.start).toLocaleString()} to ${new Date(reportData.timeRange.end).toLocaleString()}`;
  
  // Create a detailed prompt with all the report data
  const prompt = `
Analyze the following 24-hour app usage data from Hive Campus (a student marketplace app) and provide actionable insights:

TIME PERIOD: ${timeRange}

SUMMARY STATISTICS:
- Total Events: ${reportData.summary.totalEvents}
- Total Errors: ${reportData.summary.totalErrors}
- Total Feedback: ${reportData.summary.totalFeedback}
- Page Views: ${reportData.summary.totalPageViews}
- User Interactions: ${reportData.summary.totalInteractions}

ERRORS (${reportData.errors.count}):
${reportData.errors.count > 0 
  ? reportData.errors.details.slice(0, 10).map((error: ErrorDetail) => 
      `- Type: ${error.type}, Message: "${error.message}", Route: ${error.route}, Sentry ID: ${error.sentryEventId || 'N/A'}`
    ).join('\n')
  : 'No errors reported in this period.'
}

PERFORMANCE ISSUES:
${reportData.performance.issues > 0
  ? `Slowest Pages:
${reportData.performance.slowestPages.map((page: PageInfo) => 
  `- ${page.route}: ${page.duration}ms`
).join('\n')}

Slowest Resources:
${reportData.performance.slowestResources.map((resource: ResourceInfo) => 
  `- ${resource.type}: ${resource.url.substring(0, 50)}${resource.url.length > 50 ? '...' : ''} (${resource.duration}ms)`
).join('\n')}`
  : 'No significant performance issues detected.'
}

USER BEHAVIOR:
Most Visited Pages:
${reportData.userBehavior.topPages.slice(0, 5).map((page: PageInfo) => 
  `- ${page.route}: ${page.count || 0} views`
).join('\n')}

Checkout Funnel:
- Started: ${reportData.userBehavior.checkoutFunnel.started}
- Completed: ${reportData.userBehavior.checkoutFunnel.completed}
- Failed: ${reportData.userBehavior.checkoutFunnel.failed}
- Drop-off Rate: ${reportData.userBehavior.checkoutFunnel.dropOffRate}

USER FEEDBACK:
- Positive: ${reportData.feedback.positive}
- Negative: ${reportData.feedback.negative}
- Neutral: ${reportData.feedback.neutral}

${reportData.feedback.userQuotes.length > 0
  ? `Sample User Quotes:
${reportData.feedback.userQuotes.slice(0, 5).map((quote: UserQuote) => 
  `- "${quote.message}" (${quote.type}, ${quote.category}, on ${quote.route})`
).join('\n')}`
  : 'No user quotes in this period.'
}

Based on this data, please provide:
1. A concise summary of the app's health and performance
2. Key insights about user behavior and pain points
3. Specific recommendations for improvements
4. Any critical issues that need immediate attention
5. A confidence score (High/Medium/Low) based on the data volume

Format your response as a clear, actionable report with sections for:
- 📉 Issues & Breakages
- 🧩 User Confusion Points
- ⏱ Performance Issues
- 📊 Usage Patterns
- 🧠 Recommended Actions
- 🔥 Critical Issues

${includeJson ? `

After your main response, please also include a structured JSON summary with the following format:
\`\`\`json
{
  "confidence": "High|Medium|Low",
  "criticalIssues": [
    { "issue": "Description", "severity": "High|Medium|Low", "relatedTo": "Route or feature" }
  ],
  "keyInsights": [
    { "category": "Performance|Errors|Feedback|Usage", "insight": "Description" }
  ],
  "recommendations": [
    { "action": "What to do", "priority": "High|Medium|Low", "impact": "Description of expected impact" }
  ],
  "confusionPoints": [
    { "route": "Path", "issue": "Description", "feedbackCount": 0 }
  ]
}
\`\`\`
` : ''}

Please be specific, actionable, and focus on the most important insights that will help improve the app.
`;

  return prompt;
}