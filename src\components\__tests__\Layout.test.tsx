import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import Layout from '../Layout'
import { mockUser } from '@/test/mocks/firebase'

// Mock useAuth hook
const mockUseAuth = vi.fn()
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => mockUseAuth()
}))

// Mock child components
vi.mock('../UserProfileDropdown', () => ({
  default: ({ user }: { user: { name: string } }) => <div data-testid="user-dropdown">{user.name}</div>
}))

vi.mock('../MerchantProfileDropdown', () => ({
  default: ({ merchant }: { merchant: { name: string } }) => <div data-testid="merchant-dropdown">{merchant.name}</div>
}))

const renderWithRouter = (component: React.ReactElement, route = '/') => {
  return render(
    <MemoryRouter initialEntries={[route]}>
      {component}
    </MemoryRouter>
  )
}

describe('Layout', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders loading state correctly', () => {
    mockUseAuth.mockReturnValue({
      currentUser: null,
      userProfile: null,
      isLoading: true,
      isAdmin: false,
      userRole: null
    })

    renderWithRouter(
      <Layout>
        <div data-testid="content">Test Content</div>
      </Layout>
    )

    expect(screen.getByText('Hive Campus')).toBeInTheDocument()
    expect(screen.getByTestId('content')).toBeInTheDocument()
    expect(screen.getByRole('generic', { hidden: true })).toHaveClass('animate-pulse')
  })

  it('renders student navigation correctly', () => {
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: {
        role: 'student',
        name: 'Test Student',
        email: '<EMAIL>',
        university: 'Test University'
      },
      isLoading: false,
      isAdmin: false,
      userRole: 'student'
    })

    renderWithRouter(
      <Layout>
        <div data-testid="content">Test Content</div>
      </Layout>
    )

    // Check for student navigation items
    expect(screen.getByText('Home')).toBeInTheDocument()
    expect(screen.getByText('Search')).toBeInTheDocument()
    expect(screen.getByText('Sell')).toBeInTheDocument()
    expect(screen.getByText('Wallet')).toBeInTheDocument()
    expect(screen.getByText('Profile')).toBeInTheDocument()

    // Should render user dropdown
    expect(screen.getByTestId('user-dropdown')).toBeInTheDocument()
  })

  it('renders admin navigation correctly', () => {
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: {
        role: 'admin',
        name: 'Test Admin',
        email: '<EMAIL>'
      },
      isLoading: false,
      isAdmin: true,
      userRole: 'admin'
    })

    renderWithRouter(
      <Layout>
        <div data-testid="content">Test Content</div>
      </Layout>
    )

    // Check for admin navigation items
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Users')).toBeInTheDocument()
    expect(screen.getByText('Listings')).toBeInTheDocument()
    expect(screen.getByText('Reports')).toBeInTheDocument()
    expect(screen.getByText('Security')).toBeInTheDocument()
    expect(screen.getByText('ReeFlex')).toBeInTheDocument()
    expect(screen.getByText('Analytics')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()

    // Should show admin badge
    expect(screen.getByText('Admin')).toBeInTheDocument()

    // Should render user dropdown (admin uses regular user dropdown)
    expect(screen.getByTestId('user-dropdown')).toBeInTheDocument()
  })

  it('renders merchant navigation correctly', () => {
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: {
        role: 'merchant',
        name: 'Test Merchant',
        email: '<EMAIL>',
        businessName: 'Test Business'
      },
      isLoading: false,
      isAdmin: false,
      userRole: 'merchant'
    })

    renderWithRouter(
      <Layout>
        <div data-testid="content">Test Content</div>
      </Layout>
    )

    // Check for merchant navigation items
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Products')).toBeInTheDocument()
    expect(screen.getByText('Messages')).toBeInTheDocument()
    expect(screen.getByText('Profile')).toBeInTheDocument()

    // Should show partner badge
    expect(screen.getByText('Partner')).toBeInTheDocument()

    // Should render merchant dropdown
    expect(screen.getByTestId('merchant-dropdown')).toBeInTheDocument()
  })

  it('renders unauthenticated state correctly', () => {
    mockUseAuth.mockReturnValue({
      currentUser: null,
      userProfile: null,
      isLoading: false,
      isAdmin: false,
      userRole: null
    })

    renderWithRouter(
      <Layout>
        <div data-testid="content">Test Content</div>
      </Layout>
    )

    // Should show sign in/up buttons
    expect(screen.getByRole('link', { name: 'Sign In' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Sign Up' })).toBeInTheDocument()

    expect(screen.getByRole('link', { name: 'Sign In' })).toHaveAttribute('href', '/login')
    expect(screen.getByRole('link', { name: 'Sign Up' })).toHaveAttribute('href', '/signup')
  })

  it('highlights active navigation item', () => {
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: {
        role: 'student',
        name: 'Test Student',
        email: '<EMAIL>'
      },
      isLoading: false,
      isAdmin: false,
      userRole: 'student'
    })

    renderWithRouter(
      <Layout>
        <div data-testid="content">Test Content</div>
      </Layout>,
      '/home'
    )

    // Home should be active
    const homeLinks = screen.getAllByText('Home')
    expect(homeLinks[0].closest('a')).toHaveClass('text-primary-600')
  })

  it('renders correct logo and branding for different user types', () => {
    // Test admin branding
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: { role: 'admin' },
      isLoading: false,
      isAdmin: true,
      userRole: 'admin'
    })

    const { rerender } = renderWithRouter(
      <Layout>
        <div>Content</div>
      </Layout>
    )

    expect(screen.getByText('Admin')).toBeInTheDocument()

    // Test merchant branding
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: { role: 'merchant' },
      isLoading: false,
      isAdmin: false,
      userRole: 'merchant'
    })

    rerender(
      <MemoryRouter initialEntries={['/']}>
        <Layout>
          <div>Content</div>
        </Layout>
      </MemoryRouter>
    )

    expect(screen.getByText('Partner')).toBeInTheDocument()

    // Test student branding (no special badge)
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: { role: 'student' },
      isLoading: false,
      isAdmin: false,
      userRole: 'student'
    })

    rerender(
      <MemoryRouter initialEntries={['/']}>
        <Layout>
          <div>Content</div>
        </Layout>
      </MemoryRouter>
    )

    expect(screen.queryByText('Admin')).not.toBeInTheDocument()
    expect(screen.queryByText('Partner')).not.toBeInTheDocument()
  })

  it('renders children content correctly', () => {
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      userProfile: { role: 'student' },
      isLoading: false,
      isAdmin: false,
      userRole: 'student'
    })

    renderWithRouter(
      <Layout>
        <div data-testid="custom-content">Custom Test Content</div>
      </Layout>
    )

    expect(screen.getByTestId('custom-content')).toBeInTheDocument()
    expect(screen.getByText('Custom Test Content')).toBeInTheDocument()
  })
})