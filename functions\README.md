# Hive Campus Firebase Functions Backend

This directory contains the Firebase Functions backend for the Hive Campus student marketplace application.

## Setup and Deployment

### Prerequisites

1. Node.js (v18 or later)
2. Firebase CLI (`npm install -g firebase-tools`)
3. A Firebase project

### Installation

1. Install dependencies:
   ```
   cd functions
   npm install
   ```

2. Build the functions:
   ```
   npm run build
   ```

3. Deploy to Firebase:
   ```
   firebase deploy --only functions
   ```

## Authentication

The backend uses Firebase Authentication with email/password and Microsoft SSO for .edu email addresses.

### Functions

- `createUserRecord`: Triggered when a new user signs up, creates a user document in Firestore
- `updateUserProfile`: Updates a user's profile information
- `getUserProfile`: Gets the current user's profile
- `getUserById`: Gets a user's public profile by ID
- `changeUserRole`: Changes a user's role (admin only)

## Listings

Manage marketplace listings with CRUD operations.

### Functions

- `createListing`: Creates a new listing
- `editListing`: Updates an existing listing
- `deleteListing`: Deletes a listing (soft delete)
- `getListingById`: Gets a single listing by ID
- `getListings`: Gets listings with filtering options
- `searchListings`: Searches listings by title or description

## Messaging

Real-time messaging between users.

### Functions

- `createOrGetChat`: Creates or retrieves a chat between two users
- `sendMessage`: Sends a message in a chat
- `getMessages`: Gets messages from a chat
- `getChats`: Gets all chats for a user
- `markChatAsRead`: Marks all messages in a chat as read

## Uploads

Image upload functionality for listings, profiles, and issue reports.

### Functions

- `getSignedUploadUrl`: Generates a signed URL for direct uploads to Firebase Storage
- `uploadFile`: HTTP function for direct file uploads
- `deleteFile`: Deletes a file from Firebase Storage

## Feedback and Reporting

User feedback and issue reporting system.

### Functions

- `submitFeedback`: Submits user feedback
- `reportIssue`: Reports an issue with the platform
- `getFeedback`: Gets all feedback (admin only)
- `getIssueReports`: Gets all issue reports (admin only)
- `updateIssueStatus`: Updates the status of an issue report (admin only)
- `getUserIssueReports`: Gets a user's own issue reports

## Security Rules

The backend includes Firestore and Storage security rules to ensure data protection:

- Only authenticated users can create content
- Users can only modify their own content
- Admins have elevated privileges
- File uploads are restricted by type and size

## Data Structure

### Firestore Collections

- `/users/{uid}`: User profiles
- `/listings/{listingId}`: Marketplace listings
- `/chats/{chatId}`: Chat conversations
- `/chats/{chatId}/messages/{messageId}`: Chat messages
- `/feedback/{id}`: User feedback
- `/issues/{id}`: Issue reports

### Storage Structure

- `/users/{uid}/profile/{fileName}`: User profile pictures
- `/listings/{uid}/{fileName}`: Listing images
- `/issues/{uid}/{fileName}`: Issue screenshots

## Client Integration

To use these functions in your React frontend, initialize Firebase and call the functions:

```typescript
import { initializeApp } from 'firebase/app';
import { getFunctions, httpsCallable } from 'firebase/functions';

// Initialize Firebase
const app = initializeApp({
  // Your Firebase config
});

const functions = getFunctions(app);

// Example: Create a listing
const createListing = httpsCallable(functions, 'createListing');
createListing({
  title: 'iPhone 14 Pro',
  description: 'Like new condition',
  price: 899,
  category: 'Electronics',
  condition: 'like_new',
  type: 'sell',
  imageURLs: ['https://example.com/image.jpg']
})
  .then((result) => {
    console.log('Listing created:', result.data);
  })
  .catch((error) => {
    console.error('Error creating listing:', error);
  });
```