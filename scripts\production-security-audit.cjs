#!/usr/bin/env node

/**
 * Production Security Audit Script for Hive Campus
 * Comprehensive security testing before production deployment
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

class SecurityAudit {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      overallScore: 0,
      passed: 0,
      failed: 0,
      warnings: 0,
      categories: {}
    };
  }

  async runAudit() {
    console.log('🔒 Starting Production Security Audit for Hive Campus');
    console.log('=' .repeat(60));

    // 1. Firebase Security Rules Audit
    await this.auditFirebaseRules();
    
    // 2. Environment Variables & Secrets
    await this.auditSecrets();
    
    // 3. Dependencies & Vulnerabilities
    await this.auditDependencies();
    
    // 4. Client-side Security
    await this.auditClientSecurity();
    
    // 5. Network & HTTPS Configuration
    await this.auditNetworkSecurity();
    
    // 6. Data Privacy & GDPR Compliance
    await this.auditDataPrivacy();
    
    // 7. Content Security Policy
    await this.auditCSP();
    
    // 8. Authentication & Authorization
    await this.auditAuthentication();

    this.generateReport();
  }

  async auditFirebaseRules() {
    console.log('\n🔥 Auditing Firebase Security Rules...');
    const category = 'firebase-rules';
    this.results.categories[category] = { tests: [], score: 0 };

    try {
      // Check if firestore.rules exists
      const rulesPath = path.join(process.cwd(), 'firestore.rules');
      if (!fs.existsSync(rulesPath)) {
        this.addResult(category, 'Firestore rules file missing', 'FAIL', 'Critical');
        return;
      }

      const rules = fs.readFileSync(rulesPath, 'utf8');
      
      // Test 1: No allow read, write without authentication
      const hasUnsafeRules = /allow\s+read,\s*write:\s*if\s+true/.test(rules);
      this.addResult(
        category,
        'No unsafe global read/write rules',
        hasUnsafeRules ? 'FAIL' : 'PASS',
        hasUnsafeRules ? 'Critical' : 'Info'
      );

      // Test 2: Authentication checks present
      const hasAuthChecks = /isAuthenticated\(\)/.test(rules);
      this.addResult(
        category,
        'Authentication checks implemented',
        hasAuthChecks ? 'PASS' : 'FAIL',
        hasAuthChecks ? 'Info' : 'High'
      );

      // Test 3: Role-based access control
      const hasRoleChecks = /hasRole\(/.test(rules);
      this.addResult(
        category,
        'Role-based access control implemented',
        hasRoleChecks ? 'PASS' : 'WARN',
        hasRoleChecks ? 'Info' : 'Medium'
      );

      // Test 4: User ownership validation
      const hasOwnershipChecks = /isOwner\(/.test(rules);
      this.addResult(
        category,
        'User ownership validation present',
        hasOwnershipChecks ? 'PASS' : 'FAIL',
        hasOwnershipChecks ? 'Info' : 'High'
      );

      // Test 5: Input validation in rules
      const hasInputValidation = /request\.resource\.data/.test(rules);
      this.addResult(
        category,
        'Input validation in security rules',
        hasInputValidation ? 'PASS' : 'WARN',
        hasInputValidation ? 'Info' : 'Medium'
      );

    } catch (error) {
      this.addResult(category, `Firebase rules audit failed: ${error.message}`, 'FAIL', 'Critical');
    }
  }

  async auditSecrets() {
    console.log('\n🔐 Auditing Secrets & Environment Variables...');
    const category = 'secrets';
    this.results.categories[category] = { tests: [], score: 0 };

    // Test 1: Check for hardcoded secrets in source code
    try {
      const sensitivePatterns = [
        /sk_live_[a-zA-Z0-9]+/, // Stripe live keys
        /pk_live_[a-zA-Z0-9]+/, // Stripe public live keys
        /AIza[0-9A-Za-z-_]{35}/, // Google API keys
        /AKIA[0-9A-Z]{16}/, // AWS access keys
        /[a-zA-Z0-9]{32,}/, // Generic long strings that might be secrets
      ];

      const sourceFiles = this.getSourceFiles();
      let secretsFound = false;

      for (const file of sourceFiles) {
        const content = fs.readFileSync(file, 'utf8');
        for (const pattern of sensitivePatterns) {
          if (pattern.test(content)) {
            console.warn(`⚠️  Potential secret found in ${file}`);
            secretsFound = true;
          }
        }
      }

      this.addResult(
        category,
        'No hardcoded secrets in source code',
        secretsFound ? 'FAIL' : 'PASS',
        secretsFound ? 'Critical' : 'Info'
      );

    } catch (error) {
      this.addResult(category, `Secret scanning failed: ${error.message}`, 'FAIL', 'Critical');
    }

    // Test 2: Environment variable validation
    const requiredEnvVars = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_AUTH_DOMAIN',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_FIREBASE_STORAGE_BUCKET',
      'VITE_FIREBASE_MESSAGING_SENDER_ID',
      'VITE_FIREBASE_APP_ID',
      'VITE_STRIPE_PUBLISHABLE_KEY'
    ];

    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
    this.addResult(
      category,
      'All required environment variables present',
      missingEnvVars.length === 0 ? 'PASS' : 'FAIL',
      missingEnvVars.length === 0 ? 'Info' : 'Critical',
      missingEnvVars.length > 0 ? `Missing: ${missingEnvVars.join(', ')}` : undefined
    );

    // Test 3: Check for .env files in repository
    const envFiles = ['.env', '.env.local', '.env.production'];
    const trackedEnvFiles = envFiles.filter(file => fs.existsSync(file));
    
    this.addResult(
      category,
      'No .env files committed to repository',
      trackedEnvFiles.length === 0 ? 'PASS' : 'FAIL',
      trackedEnvFiles.length === 0 ? 'Info' : 'High',
      trackedEnvFiles.length > 0 ? `Found: ${trackedEnvFiles.join(', ')}` : undefined
    );
  }

  async auditDependencies() {
    console.log('\n📦 Auditing Dependencies & Vulnerabilities...');
    const category = 'dependencies';
    this.results.categories[category] = { tests: [], score: 0 };

    try {
      // Run npm audit
      const auditResult = execSync('npm audit --json', { encoding: 'utf8' });
      const audit = JSON.parse(auditResult);
      
      const criticalVulns = audit.metadata?.vulnerabilities?.critical || 0;
      const highVulns = audit.metadata?.vulnerabilities?.high || 0;
      const moderateVulns = audit.metadata?.vulnerabilities?.moderate || 0;

      this.addResult(
        category,
        'No critical vulnerabilities',
        criticalVulns === 0 ? 'PASS' : 'FAIL',
        criticalVulns === 0 ? 'Info' : 'Critical',
        criticalVulns > 0 ? `${criticalVulns} critical vulnerabilities found` : undefined
      );

      this.addResult(
        category,
        'No high-severity vulnerabilities',
        highVulns === 0 ? 'PASS' : 'FAIL',
        highVulns === 0 ? 'Info' : 'High',
        highVulns > 0 ? `${highVulns} high-severity vulnerabilities found` : undefined
      );

      this.addResult(
        category,
        'Moderate vulnerabilities under control',
        moderateVulns < 5 ? 'PASS' : 'WARN',
        moderateVulns < 5 ? 'Info' : 'Medium',
        moderateVulns >= 5 ? `${moderateVulns} moderate vulnerabilities found` : undefined
      );

    } catch (error) {
      this.addResult(category, `Dependency audit failed: ${error.message}`, 'FAIL', 'Critical');
    }

    // Check for outdated dependencies
    try {
      const outdatedResult = execSync('npm outdated --json', { encoding: 'utf8' });
      const outdated = JSON.parse(outdatedResult);
      const outdatedCount = Object.keys(outdated).length;

      this.addResult(
        category,
        'Dependencies are up to date',
        outdatedCount < 10 ? 'PASS' : 'WARN',
        outdatedCount < 10 ? 'Info' : 'Medium',
        outdatedCount >= 10 ? `${outdatedCount} outdated packages` : undefined
      );

    } catch (error) {
      // npm outdated returns non-zero exit code when packages are outdated
      // This is expected behavior, so we don't treat it as a failure
      this.addResult(category, 'Some dependencies may be outdated', 'WARN', 'Low');
    }
  }

  async auditClientSecurity() {
    console.log('\n🌐 Auditing Client-side Security...');
    const category = 'client-security';
    this.results.categories[category] = { tests: [], score: 0 };

    // Check for security utility implementation
    const securityUtilsPath = path.join(process.cwd(), 'src/utils/security.ts');
    const hasSecurityUtils = fs.existsSync(securityUtilsPath);
    
    this.addResult(
      category,
      'Security utilities implemented',
      hasSecurityUtils ? 'PASS' : 'FAIL',
      hasSecurityUtils ? 'Info' : 'High'
    );

    if (hasSecurityUtils) {
      const securityUtils = fs.readFileSync(securityUtilsPath, 'utf8');
      
      // Check for input sanitization
      const hasInputSanitization = /InputSanitizer/.test(securityUtils);
      this.addResult(
        category,
        'Input sanitization implemented',
        hasInputSanitization ? 'PASS' : 'FAIL',
        hasInputSanitization ? 'Info' : 'High'
      );

      // Check for HTTPS enforcement
      const hasHTTPSEnforcement = /enforceHTTPS/.test(securityUtils);
      this.addResult(
        category,
        'HTTPS enforcement implemented',
        hasHTTPSEnforcement ? 'PASS' : 'FAIL',
        hasHTTPSEnforcement ? 'Info' : 'High'
      );

      // Check for rate limiting
      const hasRateLimit = /RateLimit/.test(securityUtils);
      this.addResult(
        category,
        'Client-side rate limiting implemented',
        hasRateLimit ? 'PASS' : 'WARN',
        hasRateLimit ? 'Info' : 'Medium'
      );
    }

    // Check main.tsx for security initialization
    const mainTsxPath = path.join(process.cwd(), 'src/main.tsx');
    if (fs.existsSync(mainTsxPath)) {
      const mainContent = fs.readFileSync(mainTsxPath, 'utf8');
      const hasSecurityInit = /initializeSecurity/.test(mainContent);
      
      this.addResult(
        category,
        'Security initialization in main app',
        hasSecurityInit ? 'PASS' : 'WARN',
        hasSecurityInit ? 'Info' : 'Medium'
      );
    }
  }

  async auditNetworkSecurity() {
    console.log('\n🌍 Auditing Network & HTTPS Configuration...');
    const category = 'network-security';
    this.results.categories[category] = { tests: [], score: 0 };

    // This would typically test the actual deployed URL
    // For now, we'll check configuration files
    
    // Check vite.config.ts for HTTPS configuration
    const viteConfigPath = path.join(process.cwd(), 'vite.config.ts');
    if (fs.existsSync(viteConfigPath)) {
      const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
      
      // Check for security headers configuration
      const hasSecurityHeaders = /headers/.test(viteConfig);
      this.addResult(
        category,
        'Security headers configured',
        hasSecurityHeaders ? 'PASS' : 'WARN',
        hasSecurityHeaders ? 'Info' : 'Medium'
      );

      // Check for HTTPS configuration
      const hasHTTPSConfig = /https/.test(viteConfig);
      this.addResult(
        category,
        'HTTPS configuration present',
        hasHTTPSConfig ? 'PASS' : 'WARN',
        hasHTTPSConfig ? 'Info' : 'Medium'
      );
    }

    // Check firebase.json for hosting security
    const firebaseConfigPath = path.join(process.cwd(), 'firebase.json');
    if (fs.existsSync(firebaseConfigPath)) {
      const firebaseConfig = JSON.parse(fs.readFileSync(firebaseConfigPath, 'utf8'));
      
      if (firebaseConfig.hosting) {
        // Check for security headers
        const hasHeaders = firebaseConfig.hosting.headers && firebaseConfig.hosting.headers.length > 0;
        this.addResult(
          category,
          'Firebase hosting security headers configured',
          hasHeaders ? 'PASS' : 'WARN',
          hasHeaders ? 'Info' : 'Medium'
        );

        // Check for HTTPS redirect
        const hasHTTPSRedirect = firebaseConfig.hosting.headers?.some(header => 
          header.key === 'Strict-Transport-Security'
        );
        this.addResult(
          category,
          'HSTS header configured',
          hasHTTPSRedirect ? 'PASS' : 'WARN',
          hasHTTPSRedirect ? 'Info' : 'Medium'
        );
      }
    }
  }

  async auditDataPrivacy() {
    console.log('\n🔒 Auditing Data Privacy & GDPR Compliance...');
    const category = 'data-privacy';
    this.results.categories[category] = { tests: [], score: 0 };

    // Check for privacy policy page
    const privacyPolicyPath = path.join(process.cwd(), 'src/pages/PrivacyPolicy.tsx');
    const hasPrivacyPolicy = fs.existsSync(privacyPolicyPath);
    
    this.addResult(
      category,
      'Privacy policy page exists',
      hasPrivacyPolicy ? 'PASS' : 'FAIL',
      hasPrivacyPolicy ? 'Info' : 'High'
    );

    // Check for terms and conditions
    const termsPath = path.join(process.cwd(), 'src/pages/TermsAndConditions.tsx');
    const hasTerms = fs.existsSync(termsPath);
    
    this.addResult(
      category,
      'Terms and conditions page exists',
      hasTerms ? 'PASS' : 'FAIL',
      hasTerms ? 'Info' : 'High'
    );

    // Check for consent management
    const sourceFiles = this.getSourceFiles();
    let hasConsentManagement = false;
    
    for (const file of sourceFiles) {
      const content = fs.readFileSync(file, 'utf8');
      if (/consent|cookie.*policy|gdpr/i.test(content)) {
        hasConsentManagement = true;
        break;
      }
    }

    this.addResult(
      category,
      'Consent management implemented',
      hasConsentManagement ? 'PASS' : 'WARN',
      hasConsentManagement ? 'Info' : 'Medium'
    );

    // Check for data retention policies in Firebase rules
    const rulesPath = path.join(process.cwd(), 'firestore.rules');
    if (fs.existsSync(rulesPath)) {
      const rules = fs.readFileSync(rulesPath, 'utf8');
      const hasDataRetention = /delete.*if/.test(rules);
      
      this.addResult(
        category,
        'Data deletion policies in Firebase rules',
        hasDataRetention ? 'PASS' : 'WARN',
        hasDataRetention ? 'Info' : 'Medium'
      );
    }
  }

  async auditCSP() {
    console.log('\n🛡️ Auditing Content Security Policy...');
    const category = 'csp';
    this.results.categories[category] = { tests: [], score: 0 };

    // Check for CSP implementation in security utils
    const securityUtilsPath = path.join(process.cwd(), 'src/utils/security.ts');
    if (fs.existsSync(securityUtilsPath)) {
      const securityUtils = fs.readFileSync(securityUtilsPath, 'utf8');
      
      const hasCSPManager = /CSPManager/.test(securityUtils);
      this.addResult(
        category,
        'CSP manager implemented',
        hasCSPManager ? 'PASS' : 'WARN',
        hasCSPManager ? 'Info' : 'Medium'
      );

      const hasNonceGeneration = /generateNonce/.test(securityUtils);
      this.addResult(
        category,
        'Nonce generation for inline scripts',
        hasNonceGeneration ? 'PASS' : 'WARN',
        hasNonceGeneration ? 'Info' : 'Medium'
      );
    }

    // Check index.html for CSP meta tag
    const indexPath = path.join(process.cwd(), 'index.html');
    if (fs.existsSync(indexPath)) {
      const indexContent = fs.readFileSync(indexPath, 'utf8');
      const hasCSPMeta = /content-security-policy/i.test(indexContent);
      
      this.addResult(
        category,
        'CSP meta tag in HTML',
        hasCSPMeta ? 'PASS' : 'WARN',
        hasCSPMeta ? 'Info' : 'Medium'
      );
    }
  }

  async auditAuthentication() {
    console.log('\n🔐 Auditing Authentication & Authorization...');
    const category = 'authentication';
    this.results.categories[category] = { tests: [], score: 0 };

    // Check for auth context implementation
    const authContextPath = path.join(process.cwd(), 'src/contexts/AuthContext.tsx');
    const hasAuthContext = fs.existsSync(authContextPath);
    
    this.addResult(
      category,
      'Authentication context implemented',
      hasAuthContext ? 'PASS' : 'FAIL',
      hasAuthContext ? 'Info' : 'Critical'
    );

    // Check for protected routes
    const protectedRoutePath = path.join(process.cwd(), 'src/components/ProtectedRoute.tsx');
    const hasProtectedRoute = fs.existsSync(protectedRoutePath);
    
    this.addResult(
      category,
      'Protected route component implemented',
      hasProtectedRoute ? 'PASS' : 'FAIL',
      hasProtectedRoute ? 'Info' : 'High'
    );

    // Check for role-based access control
    if (hasAuthContext) {
      const authContent = fs.readFileSync(authContextPath, 'utf8');
      const hasRoleManagement = /role/.test(authContent);
      
      this.addResult(
        category,
        'Role-based access control implemented',
        hasRoleManagement ? 'PASS' : 'WARN',
        hasRoleManagement ? 'Info' : 'Medium'
      );
    }

    // Check Firebase Auth configuration
    const firebaseConfigPath = path.join(process.cwd(), 'src/firebase/config.ts');
    if (fs.existsSync(firebaseConfigPath)) {
      const firebaseConfig = fs.readFileSync(firebaseConfigPath, 'utf8');
      const hasAuthDomain = /authDomain/.test(firebaseConfig);
      
      this.addResult(
        category,
        'Firebase Auth domain configured',
        hasAuthDomain ? 'PASS' : 'FAIL',
        hasAuthDomain ? 'Info' : 'Critical'
      );
    }
  }

  getSourceFiles() {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    const excludeDirs = ['node_modules', 'dist', 'build', '.git'];
    
    const files = [];
    
    const scanDir = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !excludeDirs.includes(item)) {
          scanDir(fullPath);
        } else if (stat.isFile() && extensions.includes(path.extname(item))) {
          files.push(fullPath);
        }
      }
    };
    
    scanDir(path.join(process.cwd(), 'src'));
    return files;
  }

  addResult(category, test, status, severity, details = null) {
    const result = {
      test,
      status,
      severity,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.results.categories[category].tests.push(result);
    
    // Update counters
    switch (status) {
      case 'PASS':
        this.results.passed++;
        console.log(`✅ ${test}`);
        break;
      case 'FAIL':
        this.results.failed++;
        console.log(`❌ ${test}${details ? ' - ' + details : ''}`);
        break;
      case 'WARN':
        this.results.warnings++;
        console.log(`⚠️  ${test}${details ? ' - ' + details : ''}`);
        break;
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 SECURITY AUDIT SUMMARY');
    console.log('='.repeat(60));

    const total = this.results.passed + this.results.failed + this.results.warnings;
    this.results.overallScore = Math.round((this.results.passed / total) * 100);

    console.log(`Overall Score: ${this.results.overallScore}%`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings}`);

    // Category breakdown
    console.log('\n📋 Category Breakdown:');
    Object.entries(this.results.categories).forEach(([category, data]) => {
      const categoryPassed = data.tests.filter(t => t.status === 'PASS').length;
      const categoryTotal = data.tests.length;
      const categoryScore = Math.round((categoryPassed / categoryTotal) * 100);
      
      console.log(`${category}: ${categoryScore}% (${categoryPassed}/${categoryTotal})`);
      this.results.categories[category].score = categoryScore;
    });

    // Critical issues
    const criticalIssues = [];
    Object.values(this.results.categories).forEach(category => {
      category.tests.forEach(test => {
        if (test.status === 'FAIL' && test.severity === 'Critical') {
          criticalIssues.push(test);
        }
      });
    });

    if (criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES TO ADDRESS:');
      criticalIssues.forEach(issue => {
        console.log(`❌ ${issue.test}${issue.details ? ' - ' + issue.details : ''}`);
      });
    }

    // Save report to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(process.cwd(), `security-audit-${timestamp}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);

    // Determine if deployment should proceed
    const shouldProceed = this.results.failed === 0 && criticalIssues.length === 0;
    console.log(`\n🚀 Production Deployment: ${shouldProceed ? '✅ APPROVED' : '❌ BLOCKED'}`);

    if (!shouldProceed) {
      console.log('\n⛔ Please fix all failed tests and critical issues before deploying to production.');
      process.exit(1);
    }

    console.log('\n🎉 Security audit passed! Ready for production deployment.');
  }
}

// Run the audit
const audit = new SecurityAudit();
audit.runAudit().catch(error => {
  console.error('Security audit failed:', error);
  process.exit(1);
});