import React, { useState } from 'react';
import { MessageCircle, Star, Send, Smile, Meh, Frown } from 'lucide-react';


const MerchantFeedback: React.FC = () => {
  const [selectedRating, setSelectedRating] = useState(0);
  const [selectedEmoji, setSelectedEmoji] = useState('');
  const [feedback, setFeedback] = useState('');
  const [category, setCategory] = useState('');

  const emojiOptions = [
    { id: 'happy', icon: Smile, label: 'Great!', color: 'text-green-500' },
    { id: 'neutral', icon: Meh, label: 'Okay', color: 'text-yellow-500' },
    { id: 'sad', icon: Frown, label: 'Poor', color: 'text-red-500' }
  ];

  const categories = [
    'Partner Dashboard',
    'Product Management',
    'Analytics & Reporting',
    'Payment System',
    'Customer Support',
    'Marketing Tools',
    'General Feedback'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Feedback submitted:', {
      rating: selectedRating,
      emoji: selectedEmoji,
      category,
      feedback
    });
    // Handle feedback submission
    alert('Thank you for your feedback!');
  };

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="w-16 h-16 bg-gradient-to-r from-accent-500 to-orange-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
              <MessageCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Partner Feedback</h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Help us improve our partner experience by sharing your thoughts and suggestions
            </p>
          </div>

          {/* Feedback Form */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Emoji Rating */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  How was your partner experience?
                </label>
                <div className="flex justify-center space-x-8">
                  {emojiOptions.map((emoji) => {
                    const IconComponent = emoji.icon;
                    return (
                      <button
                        key={emoji.id}
                        type="button"
                        onClick={() => setSelectedEmoji(emoji.id)}
                        className={`flex flex-col items-center p-6 rounded-2xl transition-all transform hover:scale-110 ${
                          selectedEmoji === emoji.id
                            ? 'bg-accent-50 dark:bg-accent-900/20 ring-2 ring-accent-500'
                            : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                        }`}
                      >
                        <IconComponent className={`w-12 h-12 mb-2 ${emoji.color}`} />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {emoji.label}
                        </span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Star Rating */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Rate your overall partner experience
                </label>
                <div className="flex justify-center space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setSelectedRating(star)}
                      className="transition-all transform hover:scale-110"
                    >
                      <Star
                        className={`w-10 h-10 ${
                          star <= selectedRating
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300 dark:text-gray-600'
                        }`}
                      />
                    </button>
                  ))}
                </div>
                {selectedRating > 0 && (
                  <p className="text-center text-sm text-gray-600 dark:text-gray-400 mt-2">
                    {selectedRating} out of 5 stars
                  </p>
                )}
              </div>

              {/* Category Selection */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  What area would you like to provide feedback on?
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {categories.map((cat) => (
                    <button
                      key={cat}
                      type="button"
                      onClick={() => setCategory(cat)}
                      className={`p-3 text-sm font-medium rounded-xl transition-all ${
                        category === cat
                          ? 'bg-accent-600 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {cat}
                    </button>
                  ))}
                </div>
              </div>

              {/* Feedback Text */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Tell us more about your experience
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                  placeholder="Share your thoughts, suggestions, or any issues you've encountered as a partner..."
                  required
                />
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  {feedback.length}/500 characters
                </p>
              </div>

              {/* Submit Button */}
              <div className="text-center">
                <button
                  type="submit"
                  className="bg-gradient-to-r from-accent-600 to-accent-700 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-accent-700 hover:to-accent-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg flex items-center space-x-2 mx-auto"
                >
                  <Send className="w-5 h-5" />
                  <span>Submit Feedback</span>
                </button>
              </div>
            </form>
          </div>

          {/* Additional Info */}
          <div className="mt-8 text-center">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your feedback helps us create a better partner experience for all merchants
            </p>
            <div className="flex justify-center space-x-8 text-sm text-gray-500 dark:text-gray-400">
              <span>✓ Confidential feedback</span>
              <span>✓ Response within 24 hours</span>
              <span>✓ Direct impact on improvements</span>
            </div>
          </div>
        </div>
      </div>
  );
};

export default MerchantFeedback;