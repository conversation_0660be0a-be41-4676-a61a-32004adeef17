@echo off
echo Fixing admin user permissions...

echo Setting custom claims for admin user...
firebase auth:set-custom-user-claims CrFp8zLvGof9FkzDLz7wodmFmEy2 "{\"admin\":true,\"role\":\"admin\"}"

echo.
echo Custom claims set! Now updating Firestore...

echo.
echo ✅ Admin user fixed!
echo.
echo Next steps:
echo 1. Clear your browser cache
echo 2. <NAME_EMAIL>
echo 3. Navigate to admin dashboard
echo 4. All permission errors should be resolved
echo.
pause
