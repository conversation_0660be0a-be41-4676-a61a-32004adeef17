import {
  collection,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';

export interface StripeTransaction {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  description?: string;
  customer?: string;
  paymentMethod?: string;
  metadata?: Record<string, string>;
}

export interface StripeAccount {
  id: string;
  email: string;
  country: string;
  defaultCurrency: string;
  detailsSubmitted: boolean;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
  created: number;
}

export interface StripePayout {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  arrivalDate: number;
  description?: string;
  destination?: string;
}

export interface StripeMetrics {
  totalRevenue: number;
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  pendingPayouts: number;
  totalPayouts: number;
  platformFees: number;
  escrowBalance: number;
}

/**
 * Admin Stripe Service for transaction and payout management
 */
export class AdminStripeService {
  
  /**
   * Get Stripe metrics for admin dashboard
   */
  static async getStripeMetrics(): Promise<StripeMetrics> {
    try {
      // Get orders from Firestore (which contain Stripe transaction data)
      const ordersSnapshot = await getDocs(collection(firestore, 'orders'));
      const orders = ordersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      let totalRevenue = 0;
      let successfulTransactions = 0;
      let failedTransactions = 0;
      let platformFees = 0;
      let escrowBalance = 0;

      orders.forEach((order: any) => {
        if (order.status === 'payment_succeeded' || order.status === 'completed') {
          totalRevenue += order.totalAmount || 0;
          successfulTransactions++;
          
          // Calculate platform fee (8% for textbooks, 10% for others)
          const feeRate = order.isTextbook ? 0.08 : 0.10;
          platformFees += (order.totalAmount || 0) * feeRate;

          // Add to escrow if not yet released
          if (order.status === 'payment_succeeded' && !order.fundsReleased) {
            escrowBalance += order.totalAmount || 0;
          }
        } else if (order.status === 'failed' || order.status === 'cancelled') {
          failedTransactions++;
        }
      });

      // Get pending payouts
      const pendingPayoutsSnapshot = await getDocs(
        query(
          collection(firestore, 'orders'),
          where('status', '==', 'payment_succeeded'),
          where('fundsReleased', '==', false)
        )
      );

      return {
        totalRevenue,
        totalTransactions: orders.length,
        successfulTransactions,
        failedTransactions,
        pendingPayouts: pendingPayoutsSnapshot.size,
        totalPayouts: 0, // TODO: Calculate from released funds
        platformFees,
        escrowBalance
      };
    } catch (error) {
      console.error('Error fetching Stripe metrics:', error);
      throw error;
    }
  }

  /**
   * Get recent transactions
   */
  static async getRecentTransactions(limitCount: number = 50): Promise<any[]> {
    try {
      const ordersQuery = query(
        collection(firestore, 'orders'),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(ordersQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching recent transactions:', error);
      throw error;
    }
  }

  /**
   * Get pending payouts
   */
  static async getPendingPayouts(): Promise<any[]> {
    try {
      const pendingQuery = query(
        collection(firestore, 'orders'),
        where('status', '==', 'payment_succeeded'),
        where('fundsReleased', '==', false),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(pendingQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching pending payouts:', error);
      throw error;
    }
  }

  /**
   * Get Stripe Connect accounts
   */
  static async getConnectAccounts(): Promise<any[]> {
    try {
      const accountsSnapshot = await getDocs(collection(firestore, 'connectAccounts'));
      return accountsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching Connect accounts:', error);
      throw error;
    }
  }

  /**
   * Force release funds for an order (admin override)
   */
  static async forceReleaseFunds(orderId: string, reason: string): Promise<void> {
    try {
      const releaseFunds = httpsCallable(functions, 'releaseFundsWithCode');
      await releaseFunds({
        orderId,
        secretCode: 'ADMIN_OVERRIDE',
        adminOverride: true,
        reason
      });
    } catch (error) {
      console.error('Error force releasing funds:', error);
      throw error;
    }
  }

  /**
   * Refund a transaction
   */
  static async refundTransaction(orderId: string, amount?: number, reason?: string): Promise<void> {
    try {
      const refundTransaction = httpsCallable(functions, 'refundTransaction');
      await refundTransaction({
        orderId,
        amount,
        reason
      });
    } catch (error) {
      console.error('Error refunding transaction:', error);
      throw error;
    }
  }

  /**
   * Get transaction details from Stripe
   */
  static async getTransactionDetails(transactionId: string): Promise<any> {
    try {
      const getTransactionDetails = httpsCallable(functions, 'getTransactionDetails');
      const result = await getTransactionDetails({ transactionId });
      return result.data;
    } catch (error) {
      console.error('Error fetching transaction details:', error);
      throw error;
    }
  }

  /**
   * Get payout details from Stripe
   */
  static async getPayoutDetails(payoutId: string): Promise<any> {
    try {
      const getPayoutDetails = httpsCallable(functions, 'getPayoutDetails');
      const result = await getPayoutDetails({ payoutId });
      return result.data;
    } catch (error) {
      console.error('Error fetching payout details:', error);
      throw error;
    }
  }

  /**
   * Get Stripe webhook events
   */
  static async getWebhookEvents(limitCount: number = 100): Promise<any[]> {
    try {
      const getWebhookEvents = httpsCallable(functions, 'getWebhookEvents');
      const result = await getWebhookEvents({ limit: limitCount });
      return result.data as any[];
    } catch (error) {
      console.error('Error fetching webhook events:', error);
      throw error;
    }
  }

  /**
   * Test webhook endpoint
   */
  static async testWebhook(): Promise<boolean> {
    try {
      const testWebhook = httpsCallable(functions, 'testWebhook');
      const result = await testWebhook({});
      return result.data as boolean;
    } catch (error) {
      console.error('Error testing webhook:', error);
      return false;
    }
  }

  /**
   * Get revenue analytics by time period
   */
  static async getRevenueAnalytics(
    startDate: Date,
    endDate: Date
  ): Promise<{
    daily: Array<{ date: string; revenue: number; transactions: number }>;
    total: number;
    growth: number;
  }> {
    try {
      const ordersQuery = query(
        collection(firestore, 'orders'),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        where('createdAt', '<=', Timestamp.fromDate(endDate)),
        where('status', 'in', ['payment_succeeded', 'completed'])
      );
      
      const snapshot = await getDocs(ordersQuery);
      const orders = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Group by day
      const dailyData = new Map<string, { revenue: number; transactions: number }>();
      let totalRevenue = 0;

      orders.forEach((order: any) => {
        const date = order.createdAt.toDate().toISOString().split('T')[0];
        const revenue = order.totalAmount || 0;
        
        if (!dailyData.has(date)) {
          dailyData.set(date, { revenue: 0, transactions: 0 });
        }
        
        const dayData = dailyData.get(date)!;
        dayData.revenue += revenue;
        dayData.transactions += 1;
        
        totalRevenue += revenue;
      });

      const daily = Array.from(dailyData.entries()).map(([date, data]) => ({
        date,
        ...data
      })).sort((a, b) => a.date.localeCompare(b.date));

      // Calculate growth (simplified - compare with previous period)
      const growth = 0; // TODO: Implement growth calculation

      return {
        daily,
        total: totalRevenue,
        growth
      };
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      throw error;
    }
  }
}
