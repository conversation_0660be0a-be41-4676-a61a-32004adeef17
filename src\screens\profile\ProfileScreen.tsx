import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  User,
  Edit,
  Settings,
  ShoppingBag,
  Heart,
  Star,
  MessageCircle,
  Shield,
  ChevronRight,
  Camera,
  Package,
  DollarSign,
  Clock,
  Eye,
  BarChart3,
  TrendingUp,
  Award,
  Bookmark,
  Share2,
  LogOut
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useListings } from '../../hooks/useListings';
import { formatTimestamp } from '../../utils/timestamp';

// Keep only mock reviews for now - TODO: Replace with real reviews data

// Mock reviews data
const mockReviews = [
  {
    id: 'rev1',
    reviewer: {
      id: 'user456',
      name: '<PERSON>',
      avatar: '/placeholder-avatar.svg',
      university: 'Stanford University'
    },
    rating: 5,
    comment: 'Great seller! The item was exactly as described and shipping was fast. Would definitely buy from again.',
    createdAt: '2023-09-10T14:30:00Z',
    productId: '4',
    productTitle: 'Gaming Setup - Monitor & Keyboard'
  },
  {
    id: 'rev2',
    reviewer: {
      id: 'user789',
      name: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=200',
      university: 'UC Berkeley'
    },
    rating: 5,
    comment: 'Smooth transaction. John was very responsive and the textbook was in better condition than I expected!',
    createdAt: '2023-08-25T09:15:00Z',
    productId: '3',
    productTitle: 'Calculus Textbook - 11th Edition'
  },
  {
    id: 'rev3',
    reviewer: {
      id: 'user101',
      name: 'Emma Wilson',
      avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=200',
      university: 'Stanford University'
    },
    rating: 4,
    comment: 'Good experience overall. The item had a small scratch that was not mentioned, but otherwise as described.',
    createdAt: '2023-08-15T16:45:00Z',
    productId: '2',
    productTitle: 'MacBook Air M2 - Space Gray'
  }
];

const ProfileScreen: React.FC = () => {
  const { currentUser, userProfile } = useAuth();
  const { fetchListings, listings, isLoading: _listingsLoading } = useListings();
  const [reviews, _setReviews] = useState(mockReviews); // Keep reviews as mock for now
  const [error, _setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'listings' | 'sold' | 'saved' | 'reviews'>('listings');
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileForm, setProfileForm] = useState({
    name: userProfile?.name || '',
    bio: userProfile?.bio || '',
    major: userProfile?.major || '',
    graduationYear: userProfile?.graduationYear?.toString() || ''
  });

  // Initialize form with user data
  useEffect(() => {
    if (userProfile) {
      setProfileForm({
        name: userProfile.name || '',
        bio: userProfile.bio || '',
        major: userProfile.major || '',
        graduationYear: userProfile.graduationYear?.toString() || ''
      });
    }
  }, [userProfile]);

  // Fetch user's listings
  useEffect(() => {
    if (currentUser) {
      fetchListings({ ownerId: currentUser.uid });
    }
  }, [currentUser, fetchListings]);

  // Create user object from real data
  const user = {
    id: currentUser?.uid || '',
    name: userProfile?.name || 'User',
    email: userProfile?.email || '',
    avatar: userProfile?.profilePictureURL || '/placeholder-avatar.svg',
    university: userProfile?.university || 'University',
    major: userProfile?.major || '',
    graduationYear: userProfile?.graduationYear?.toString() || '',
    bio: userProfile?.bio || 'No bio available',
    verified: true,
    memberSince: formatTimestamp(userProfile?.createdAt, { month: 'long', year: 'numeric' }) || 'Recently',
    rating: 0, // Will be calculated from real reviews when implemented
    reviews: 0, // Will count real reviews when implemented
    listings: {
      active: listings.filter(l => l.status === 'active').length,
      sold: 0, // TODO: Count sold listings
      saved: 0 // TODO: Count saved listings
    },
    stats: {
      views: 0, // TODO: Add views tracking to listings
      likes: 0, // TODO: Add likes tracking to listings
      messages: 0, // TODO: Count messages
      sales: 0, // TODO: Count sales
      revenue: 0 // TODO: Calculate revenue
    },
    badges: [
      { id: 1, name: 'Verified Student', icon: Shield, color: 'bg-blue-100 text-blue-700' },
      { id: 2, name: 'Top Seller', icon: Award, color: 'bg-yellow-100 text-yellow-700' }
    ]
  };

  const handleProfileFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveProfile = () => {
    // TODO: Implement real profile update API call
    // For now, just close the editing mode
    setIsEditingProfile(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getActiveListings = () => {
    return listings.filter(listing => listing.status === 'active');
  };

  const getSoldListings = () => {
    return listings.filter(listing => listing.status === 'sold');
  };

  const getSavedListings = () => {
    // In a real app, you would fetch saved listings from an API
    // For now, we'll just return the first 2 listings as "saved"
    return listings.slice(0, 2);
  };

  if (!currentUser || !userProfile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 max-w-md w-full">
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 dark:bg-red-900/30 rounded-full">
              <User className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Error</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-xl font-semibold transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 pb-16 md:pb-0">
      {/* Profile Header */}
      <div className="relative">
        {/* Cover Image */}
        <div className="h-48 bg-gradient-to-r from-primary-600 via-primary-500 to-accent-500"></div>
        
        {/* Profile Info Card */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative -mt-24">
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-2xl shadow-xl p-6 sm:p-8">
              <div className="flex flex-col sm:flex-row items-center sm:items-start">
                {/* Avatar */}
                <div className="relative mb-4 sm:mb-0 sm:mr-6">
                  <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white dark:border-gray-800 shadow-lg">
                    <img 
                      src={user.avatar} 
                      alt={user.name} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {!isEditingProfile && (
                    <button className="absolute bottom-0 right-0 bg-primary-600 hover:bg-primary-700 text-white p-2 rounded-full shadow-md transition-colors">
                      <Camera className="w-5 h-5" />
                    </button>
                  )}
                </div>
                
                {/* User Info */}
                <div className="flex-1 text-center sm:text-left">
                  {isEditingProfile ? (
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Name
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={profileForm.name}
                          onChange={handleProfileFormChange}
                          className="w-full px-4 py-2 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="major" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Major
                          </label>
                          <input
                            type="text"
                            id="major"
                            name="major"
                            value={profileForm.major}
                            onChange={handleProfileFormChange}
                            className="w-full px-4 py-2 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                          />
                        </div>
                        
                        <div>
                          <label htmlFor="graduationYear" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Graduation Year
                          </label>
                          <input
                            type="text"
                            id="graduationYear"
                            name="graduationYear"
                            value={profileForm.graduationYear}
                            onChange={handleProfileFormChange}
                            className="w-full px-4 py-2 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Bio
                        </label>
                        <textarea
                          id="bio"
                          name="bio"
                          value={profileForm.bio}
                          onChange={handleProfileFormChange}
                          rows={3}
                          className="w-full px-4 py-2 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                        />
                      </div>
                      
                      <div className="flex space-x-3">
                        <button
                          onClick={handleSaveProfile}
                          className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-xl font-medium transition-colors"
                        >
                          Save Changes
                        </button>
                        
                        <button
                          onClick={() => setIsEditingProfile(false)}
                          className="flex-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 rounded-xl font-medium transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                        <div className="flex items-center justify-center sm:justify-start mb-2 sm:mb-0">
                          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mr-2">
                            {user.name}
                          </h1>
                          {user.verified && (
                            <div className="bg-primary-500 text-white p-1 rounded-full">
                              <Shield className="w-4 h-4 fill-current" />
                            </div>
                          )}
                        </div>
                        
                        <button
                          onClick={() => setIsEditingProfile(true)}
                          className="inline-flex items-center text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                        >
                          <Edit className="w-4 h-4 mr-1" />
                          Edit Profile
                        </button>
                      </div>
                      
                      <div className="flex flex-wrap justify-center sm:justify-start gap-2 mb-3">
                        {user.badges.map(badge => {
                          const BadgeIcon = badge.icon;
                          return (
                            <span 
                              key={badge.id}
                              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300"
                            >
                              <BadgeIcon className="w-3 h-3 mr-1" />
                              {badge.name}
                            </span>
                          );
                        })}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {user.bio}
                      </p>
                      
                      <div className="flex flex-wrap justify-center sm:justify-start gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" />
                          <span>{user.university}</span>
                        </div>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
                          <span>{user.rating} ({user.reviews} reviews)</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" />
                          <span>Member since {user.memberSince}</span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <Eye className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-xs text-success-600 dark:text-success-400 font-medium">
                +12%
              </span>
            </div>
            <div className="text-xl font-bold text-gray-900 dark:text-white mb-1">
              {user.stats.views}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Profile Views</p>
          </div>
          
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                <Heart className="w-5 h-5 text-red-600 dark:text-red-400" />
              </div>
              <span className="text-xs text-success-600 dark:text-success-400 font-medium">
                +8%
              </span>
            </div>
            <div className="text-xl font-bold text-gray-900 dark:text-white mb-1">
              {user.stats.likes}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Total Likes</p>
          </div>
          
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-xs text-success-600 dark:text-success-400 font-medium">
                +15%
              </span>
            </div>
            <div className="text-xl font-bold text-gray-900 dark:text-white mb-1">
              {user.stats.messages}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Messages</p>
          </div>
          
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <span className="text-xs text-success-600 dark:text-success-400 font-medium">
                +22%
              </span>
            </div>
            <div className="text-xl font-bold text-gray-900 dark:text-white mb-1">
              ${user.stats.revenue}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</p>
          </div>
        </div>
      </div>
      
      {/* Performance Chart */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <BarChart3 className="w-6 h-6 text-primary-600 dark:text-primary-400 mr-2" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Performance Overview</h2>
            </div>
            <select className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg px-3 py-1.5 text-sm">
              <option value="week">Last 7 days</option>
              <option value="month">Last 30 days</option>
              <option value="year">Last 12 months</option>
            </select>
          </div>
          
          <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700/50 rounded-xl">
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Performance chart would render here with actual data
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Sales Performance</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">This month</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                {user.stats.sales} items sold
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-success-500"
                  style={{ width: '75%' }}
                ></div>
              </div>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-xl flex items-center justify-center">
                  <Star className="w-5 h-5 text-yellow-500" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Rating</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Overall</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                {user.rating} / 5.0
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-yellow-500"
                  style={{ width: `${(user.rating / 5) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-xl flex items-center justify-center">
                  <Clock className="w-5 h-5 text-accent-500" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Response Rate</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Message replies</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                98%
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-accent-500"
                  style={{ width: '98%' }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Tabs and Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Tabs */}
        <div className="flex overflow-x-auto scrollbar-hide border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            onClick={() => setActiveTab('listings')}
            className={`pb-4 px-6 text-sm font-medium whitespace-nowrap ${
              activeTab === 'listings'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Active Listings ({user.listings.active})
          </button>
          <button
            onClick={() => setActiveTab('sold')}
            className={`pb-4 px-6 text-sm font-medium whitespace-nowrap ${
              activeTab === 'sold'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Sold Items ({user.listings.sold})
          </button>
          <button
            onClick={() => setActiveTab('saved')}
            className={`pb-4 px-6 text-sm font-medium whitespace-nowrap ${
              activeTab === 'saved'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Saved Items ({user.listings.saved})
          </button>
          <button
            onClick={() => setActiveTab('reviews')}
            className={`pb-4 px-6 text-sm font-medium whitespace-nowrap ${
              activeTab === 'reviews'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Reviews ({user.reviews})
          </button>
        </div>
        
        {/* Tab Content */}
        <div className="mb-12">
          {activeTab === 'listings' && (
            <>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">Active Listings</h2>
                <Link
                  to="/add-listing"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl font-medium transition-colors inline-flex items-center"
                >
                  <ShoppingBag className="w-4 h-4 mr-2" />
                  Add New Listing
                </Link>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {getActiveListings().map(listing => (
                  <div 
                    key={listing.id}
                    className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group"
                  >
                    <Link to={`/listing/${listing.id}`}>
                      <div className="relative overflow-hidden">
                        <img
                          src={listing.imageURLs?.[0] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400'}
                          alt={listing.title}
                          className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                        <div className="absolute top-4 left-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            listing.status === 'active'
                              ? 'bg-success-100 text-success-700'
                              : 'bg-gray-100 text-gray-700'
                          }`}>
                            {listing.status}
                          </span>
                        </div>
                      </div>
                    </Link>
                    
                    <div className="p-4">
                      <Link to={`/listing/${listing.id}`}>
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                          {listing.title}
                        </h3>
                      </Link>
                      
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                          ${listing.price}
                        </span>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
                        <span>{listing.condition}</span>
                        <span>{formatTimestamp(listing.createdAt, { month: 'short', day: 'numeric' })}</span>
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span className="flex items-center">
                          <Eye className="w-3 h-3 mr-1" />
                          0 views
                        </span>
                        <span className="flex items-center">
                          <Heart className="w-3 h-3 mr-1" />
                          0 likes
                        </span>
                        <Link 
                          to={`/edit-listing/${listing.id}`}
                          className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors flex items-center"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          Edit
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {getActiveListings().length === 0 && (
                <div className="text-center py-12 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl">
                  <ShoppingBag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No active listings</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">You don't have any active listings at the moment.</p>
                  <Link
                    to="/add-listing"
                    className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-medium transition-colors inline-flex items-center"
                  >
                    <ShoppingBag className="w-5 h-5 mr-2" />
                    Create Your First Listing
                  </Link>
                </div>
              )}
            </>
          )}
          
          {activeTab === 'sold' && (
            <>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Sold Items</h2>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {getSoldListings().map(listing => (
                  <div 
                    key={listing.id}
                    className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md overflow-hidden group relative"
                  >
                    <div className="absolute inset-0 bg-black/50 z-10 flex items-center justify-center">
                      <span className="bg-success-600 text-white px-4 py-2 rounded-lg font-medium">
                        SOLD
                      </span>
                    </div>
                    
                    <Link to={`/listing/${listing.id}`}>
                      <div className="relative overflow-hidden">
                        <img
                          src={listing.imageURLs?.[0] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400'}
                          alt={listing.title}
                          className="w-full h-48 object-cover filter grayscale"
                        />
                      </div>
                    </Link>
                    
                    <div className="p-4">
                      <Link to={`/listing/${listing.id}`}>
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                          {listing.title}
                        </h3>
                      </Link>
                      
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                          ${listing.price}
                        </span>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                        <span>{listing.condition}</span>
                        <span>Sold recently</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {getSoldListings().length === 0 && (
                <div className="text-center py-12 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No sold items</h3>
                  <p className="text-gray-600 dark:text-gray-400">You haven't sold any items yet.</p>
                </div>
              )}
            </>
          )}
          
          {activeTab === 'saved' && (
            <>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Saved Items</h2>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {getSavedListings().map(listing => (
                  <div 
                    key={listing.id}
                    className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group"
                  >
                    <Link to={`/listing/${listing.id}`}>
                      <div className="relative overflow-hidden">
                        <img
                          src={listing.imageURLs?.[0] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400'}
                          alt={listing.title}
                          className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                        <div className="absolute top-4 right-4">
                          <button className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-2 rounded-full shadow-md">
                            <Heart className="w-5 h-5 text-red-500 fill-current" />
                          </button>
                        </div>
                      </div>
                    </Link>
                    
                    <div className="p-4">
                      <Link to={`/listing/${listing.id}`}>
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                          {listing.title}
                        </h3>
                      </Link>
                      
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                          ${listing.price}
                        </span>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                        <span>{listing.condition}</span>
                        <span>Saved recently</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {getSavedListings().length === 0 && (
                <div className="text-center py-12 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl">
                  <Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No saved items</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">You haven't saved any items yet.</p>
                  <Link
                    to="/home"
                    className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-medium transition-colors inline-flex items-center"
                  >
                    Browse Listings
                  </Link>
                </div>
              )}
            </>
          )}
          
          {activeTab === 'reviews' && (
            <>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Reviews</h2>
              
              <div className="space-y-6">
                {reviews.map(review => (
                  <div 
                    key={review.id}
                    className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-md p-6"
                  >
                    <div className="flex items-start">
                      <img 
                        src={review.reviewer.avatar} 
                        alt={review.reviewer.name} 
                        className="w-12 h-12 rounded-full object-cover mr-4"
                      />
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {review.reviewer.name}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {review.reviewer.university}
                            </p>
                          </div>
                          <div className="flex items-center mt-2 sm:mt-0">
                            {[...Array(5)].map((_, i) => (
                              <Star 
                                key={i}
                                className={`w-5 h-5 ${
                                  i < review.rating 
                                    ? 'text-yellow-400 fill-current' 
                                    : 'text-gray-300 dark:text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        
                        <p className="text-gray-700 dark:text-gray-300 mb-3">
                          {review.comment}
                        </p>
                        
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-sm">
                          <Link 
                            to={`/listing/${review.productId}`}
                            className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                          >
                            Product: {review.productTitle}
                          </Link>
                          <span className="text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">
                            {formatDate(review.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {reviews.length === 0 && (
                <div className="text-center py-12 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl">
                  <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No reviews yet</h3>
                  <p className="text-gray-600 dark:text-gray-400">You haven't received any reviews yet.</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
      
      {/* Quick Actions */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
        <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
          <Link to="/settings" className="bg-gradient-to-r from-accent-500 to-accent-600 text-white p-6 rounded-2xl text-center hover:from-accent-600 hover:to-accent-700 transition-all transform hover:scale-105">
            <div className="text-3xl mb-2">⚙️</div>
            <p className="font-semibold">Settings</p>
          </Link>
          <Link to="/messages" className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-2xl text-center hover:from-purple-600 hover:to-purple-700 transition-all transform hover:scale-105">
            <div className="text-3xl mb-2">💬</div>
            <p className="font-semibold">Messages</p>
          </Link>
        </div>
      </div>
      
      {/* Account Actions */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Account Actions</h2>
          
          <div className="space-y-4">
            <Link 
              to="/settings"
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-4">
                  <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">Account Settings</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Manage your account preferences</p>
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </Link>
            
            <Link 
              to="/settings/payment"
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-4">
                  <DollarSign className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">Payment Methods</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Manage your payment options</p>
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </Link>
            
            <Link 
              to="/profile/share"
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-4">
                  <Share2 className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">Share Profile</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Share your profile with others</p>
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </Link>
            
            <button 
              className="w-full flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-xl hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors text-left"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mr-4">
                  <LogOut className="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 className="font-medium text-red-600 dark:text-red-400">Sign Out</h3>
                  <p className="text-sm text-red-500 dark:text-red-300">Log out of your account</p>
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-red-400" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileScreen;