import React from 'react';
import { Download, Truck, Package, ExternalLink } from 'lucide-react';
import { Order } from '../firebase/types';
import { useStripeCheckout } from '../hooks';
import { captureTypedEvent, SentryEventType, captureException } from '../utils/sentry';

interface ShippingLabelCardProps {
  order: Order;
  isSeller: boolean;
}

const ShippingLabelCard: React.FC<ShippingLabelCardProps> = ({ order, isSeller }) => {
  const { generateShippingLabel, isLoading } = useStripeCheckout();

  const handleGenerateLabel = async () => {
    // Track shipping label generation attempt
    captureTypedEvent(SentryEventType.FEATURE_USED, {
      feature: 'shipping_label_generation_started',
      order_id: order.id,
      is_seller: isSeller,
      order_status: order.status
    });
    
    try {
      await generateShippingLabel(order.id);
      
      // Track successful shipping label generation
      captureTypedEvent(SentryEventType.SHIPPING_LABEL_GENERATED, {
        order_id: order.id,
        success: true
      });
      
      // Refresh the page or order data after generating the label
      window.location.reload();
    } catch (error: unknown) {
      console.error('Error generating shipping label:', error);
      
      // Track shipping label generation failure
      captureException(error, {
        feature: 'shipping_label_generation',
        order_id: order.id,
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <Truck className="w-5 h-5 mr-2 text-primary-600 dark:text-primary-400" />
        Shipping Information
      </h3>

      {order.trackingInfo ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Carrier</p>
              <p className="font-medium text-gray-900 dark:text-white">{order.trackingInfo.carrier}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Tracking Number</p>
              <p className="font-medium text-gray-900 dark:text-white">{order.trackingInfo.trackingNumber}</p>
            </div>
          </div>

          <div className="flex flex-col space-y-3">
            {isSeller && order.trackingInfo.labelUrl && (
              <a 
                href={order.trackingInfo.labelUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                onClick={() => {
                  // Track when sellers download the shipping label
                  captureTypedEvent(SentryEventType.FEATURE_USED, {
                    feature: 'shipping_label_downloaded',
                    order_id: order.id,
                    carrier: order.trackingInfo?.carrier
                  });
                }}
                className="flex items-center justify-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-xl transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Download Shipping Label</span>
              </a>
            )}

            {order.trackingInfo.trackingUrl && (
              <a 
                href={order.trackingInfo.trackingUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                onClick={() => {
                  // Track when users click on the tracking link
                  captureTypedEvent(SentryEventType.FEATURE_USED, {
                    feature: 'package_tracking_clicked',
                    order_id: order.id,
                    is_seller: isSeller,
                    carrier: order.trackingInfo?.carrier
                  });
                }}
                className="flex items-center justify-center space-x-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-xl transition-colors"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Track Package</span>
              </a>
            )}
          </div>

          {isSeller && !order.trackingInfo.labelUrl && (
            <div className="mt-4">
              <p className="text-sm text-amber-600 dark:text-amber-400 mb-2">
                Shipping label not found. Generate a new one:
              </p>
              <button
                onClick={handleGenerateLabel}
                disabled={isLoading}
                className="w-full flex items-center justify-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-xl transition-colors disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <span className="animate-spin">⏳</span>
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <Package className="w-4 h-4" />
                    <span>Generate Shipping Label</span>
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      ) : (
        <div>
          {isSeller ? (
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Generate a shipping label to send this item to the buyer.
              </p>
              <button
                onClick={handleGenerateLabel}
                disabled={isLoading}
                className="w-full flex items-center justify-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-xl transition-colors disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <span className="animate-spin">⏳</span>
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <Package className="w-4 h-4" />
                    <span>Generate Shipping Label</span>
                  </>
                )}
              </button>
            </div>
          ) : (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              The seller has not generated a shipping label yet. You'll receive tracking information once the item is shipped.
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default ShippingLabelCard;