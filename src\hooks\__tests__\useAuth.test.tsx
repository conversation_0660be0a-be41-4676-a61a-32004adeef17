import { describe, it, expect, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { AuthProvider } from '../../contexts/AuthContext'
import { useAuth } from '../useAuth'

describe('useAuth', () => {
  it('should throw error when used outside AuthProvider', () => {
    // Suppress console.error for this test since we expect an error
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    expect(() => {
      renderHook(() => useAuth())
    }).toThrow('useAuth must be used within an AuthProvider')
    
    consoleSpy.mockRestore()
  })

  it('should return auth context when used within AuthProvider', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    )

    const { result } = renderHook(() => useAuth(), { wrapper })

    expect(result.current).toHaveProperty('currentUser')
    expect(result.current).toHaveProperty('userProfile')
    expect(result.current).toHaveProperty('userRole')
    expect(result.current).toHaveProperty('isLoading')
    expect(result.current).toHaveProperty('isAdmin')
    expect(result.current).toHaveProperty('isMerchant')
    expect(result.current).toHaveProperty('isStudent')
  })
})