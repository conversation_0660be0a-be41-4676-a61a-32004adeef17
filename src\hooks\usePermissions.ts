import { useAuth } from './useAuth';
import { 
  canEditProfile, 
  canEditListing, 
  canDeleteListing, 
  canViewOrder,
  canUpdateOrder,
  canViewChat,
  canSendMessage,
  canCreateListing,
  canViewListing,
  canPurchaseListing,
  canViewProfile,
  canReportUser,
  canReportListing,
  canAccessAdminPanel,
  canManageUsers,
  canManageListings,
  canViewAnalytics,
  canManagePayments,
  canManageDisputes,
  canViewAuditLogs,
  canManageSettings,
  canViewSystemHealth,
  canManageNotifications,
  canExportData,
  canManageIntegrations,
  canViewFinancialReports,
  canManageCategories,
  canManageTags,
  canManagePromotions,
  canViewUserActivity,
  canManageContent,
  canManageSupport,
  canViewMetrics,
  canManageBackups,
  canManageSecurity,
  canViewLogs,
  canManageAPI,
  canViewDashboard,
  canManageRoles,
  canManagePermissions
} from '../utils/permissions';
import { User, Listing, Order, Chat } from '../firebase/types';

// Resource data types for permission checking
type ResourceData = Listing | Order | Chat | User | {
  listingOwner?: User;
  visibility?: 'university' | 'public';
  buyerId?: string;
  sellerId?: string;
  participants?: string[];
};

// Hook for checking permissions in components
export const usePermissions = () => {
  const { currentUser } = useAuth();

  const checkPermission = (
    permission: string,
    resourceOwnerId?: string,
    resourceData?: ResourceData
  ): boolean => {
    if (!currentUser) return false;

    switch (permission) {
      // Profile permissions
      case 'edit_profile':
        return canEditProfile(currentUser, resourceOwnerId);
      case 'view_profile':
        return canViewProfile(currentUser, resourceOwnerId);

      // Listing permissions
      case 'create_listing':
        return canCreateListing(currentUser);
      case 'edit_listing':
        return canEditListing(currentUser, resourceOwnerId);
      case 'delete_listing':
        return canDeleteListing(currentUser, resourceOwnerId);
      case 'view_listing':
        return canViewListing(currentUser, resourceData);
      case 'purchase_listing':
        return canPurchaseListing(currentUser, resourceData);

      // Order permissions
      case 'view_order':
        return canViewOrder(currentUser, resourceData);
      case 'update_order':
        return canUpdateOrder(currentUser, resourceData);

      // Chat permissions
      case 'view_chat':
        return canViewChat(currentUser, resourceData);
      case 'send_message':
        return canSendMessage(currentUser, resourceData);

      // Reporting permissions
      case 'report_user':
        return canReportUser(currentUser);
      case 'report_listing':
        return canReportListing(currentUser);

      // Admin permissions
      case 'access_admin_panel':
        return canAccessAdminPanel(currentUser);
      case 'manage_users':
        return canManageUsers(currentUser);
      case 'manage_listings':
        return canManageListings(currentUser);
      case 'view_analytics':
        return canViewAnalytics(currentUser);
      case 'manage_payments':
        return canManagePayments(currentUser);
      case 'manage_disputes':
        return canManageDisputes(currentUser);
      case 'view_audit_logs':
        return canViewAuditLogs(currentUser);
      case 'manage_settings':
        return canManageSettings(currentUser);
      case 'view_system_health':
        return canViewSystemHealth(currentUser);
      case 'manage_notifications':
        return canManageNotifications(currentUser);
      case 'export_data':
        return canExportData(currentUser);
      case 'manage_integrations':
        return canManageIntegrations(currentUser);
      case 'view_financial_reports':
        return canViewFinancialReports(currentUser);
      case 'manage_categories':
        return canManageCategories(currentUser);
      case 'manage_tags':
        return canManageTags(currentUser);
      case 'manage_promotions':
        return canManagePromotions(currentUser);
      case 'view_user_activity':
        return canViewUserActivity(currentUser);
      case 'manage_content':
        return canManageContent(currentUser);
      case 'manage_support':
        return canManageSupport(currentUser);
      case 'view_metrics':
        return canViewMetrics(currentUser);
      case 'manage_backups':
        return canManageBackups(currentUser);
      case 'manage_security':
        return canManageSecurity(currentUser);
      case 'view_logs':
        return canViewLogs(currentUser);
      case 'manage_api':
        return canManageAPI(currentUser);
      case 'view_dashboard':
        return canViewDashboard(currentUser);
      case 'manage_roles':
        return canManageRoles(currentUser);
      case 'manage_permissions':
        return canManagePermissions(currentUser);

      default:
        return false;
    }
  };

  return {
    checkPermission,
    canEdit: (resourceOwnerId?: string) => checkPermission('edit_profile', resourceOwnerId),
    canDelete: (resourceOwnerId?: string) => checkPermission('delete_listing', resourceOwnerId),
    canView: (resourceData?: ResourceData) => checkPermission('view_listing', undefined, resourceData),
    canPurchase: (resourceData?: ResourceData) => checkPermission('purchase_listing', undefined, resourceData),
    canReport: () => checkPermission('report_user'),
    isAdmin: () => checkPermission('access_admin_panel'),
    canManage: (resource: string) => checkPermission(`manage_${resource}`),
  };
};
