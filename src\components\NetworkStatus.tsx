import { useState, useEffect } from 'react';
import { WifiOff } from 'lucide-react';

const NetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 pointer-events-none">
      <div className="mx-auto max-w-md bg-red-50 backdrop-blur-lg rounded-xl shadow-lg p-4 border border-red-200 pointer-events-auto">
        <div className="flex items-center">
          <div className="flex-shrink-0 text-red-500">
            <WifiOff size={20} />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">You're offline</h3>
            <div className="mt-1 text-xs text-red-700">
              <p>Some features may be unavailable until you reconnect.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkStatus;