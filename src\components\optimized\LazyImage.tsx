import React, { useState, useRef, useEffect } from 'react';
import { useIntersectionObserver, useImagePreload } from '../../hooks/usePerformance';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  blurDataURL?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
  loading?: 'lazy' | 'eager';
  decoding?: 'sync' | 'async' | 'auto';
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
  blurDataURL,
  width,
  height,
  onLoad,
  onError,
  loading = 'lazy',
  decoding = 'async'
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(loading === 'eager');
  const imgRef = useRef<HTMLImageElement>(null);
  
  const { ref: intersectionRef, isIntersecting } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px'
  });
  
  // Combine refs
  const setRefs = useRef((element: HTMLImageElement | null) => {
    imgRef.current = element;
    intersectionRef.current = element;
  });

  // Start loading when image enters viewport
  useEffect(() => {
    if (isIntersecting && !shouldLoad) {
      setShouldLoad(true);
    }
  }, [isIntersecting, shouldLoad]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Preload image when should load
  const { loaded: preloaded, error: preloadError } = useImagePreload(shouldLoad ? src : '');

  useEffect(() => {
    if (preloaded) {
      setIsLoaded(true);
    }
    if (preloadError) {
      setHasError(true);
    }
  }, [preloaded, preloadError]);

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#f3f4f6',
    ...(width && height && { aspectRatio: `${width} / ${height}` })
  };

  const imageStyle: React.CSSProperties = {
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 1 : 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover'
  };

  const placeholderStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    opacity: isLoaded ? 0 : 1,
    transition: 'opacity 0.3s ease-in-out',
    filter: blurDataURL ? 'blur(10px)' : 'none',
    transform: blurDataURL ? 'scale(1.1)' : 'none' // Slightly scale to hide blur edges
  };

  return (
    <div className={`lazy-image-container ${className}`} style={containerStyle}>
      {/* Placeholder image */}
      <img
        src={blurDataURL || placeholder}
        alt=""
        style={placeholderStyle}
        aria-hidden="true"
      />
      
      {/* Main image */}
      {shouldLoad && !hasError && (
        <img
          ref={setRefs.current}
          src={src}
          alt={alt}
          style={imageStyle}
          onLoad={handleLoad}
          onError={handleError}
          loading={loading}
          decoding={decoding}
          width={width}
          height={height}
        />
      )}
      
      {/* Error state */}
      {hasError && (
        <div 
          className="flex items-center justify-center bg-gray-200 text-gray-500"
          style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
        >
          <div className="text-center">
            <svg className="mx-auto w-12 h-12 mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <p className="text-sm">Failed to load image</p>
          </div>
        </div>
      )}
      
      {/* Loading indicator */}
      {shouldLoad && !isLoaded && !hasError && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-gray-100"
          style={{ opacity: 0.8 }}
        >
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      )}
    </div>
  );
};

export default React.memo(LazyImage);