/**
 * Utility to collect device and browser information
 * Used for ReeFlex observability to provide context for events
 */

export interface DeviceInfo {
  userAgent: string;
  screenWidth: number;
  screenHeight: number;
  devicePixelRatio: number;
  platform: string;
  language: string;
  connectionType?: string;
  memoryInfo?: {
    jsHeapSizeLimit?: number;
    totalJSHeapSize?: number;
    usedJSHeapSize?: number;
  };
  deviceType: 'mobile' | 'tablet' | 'desktop';
}

/**
 * Get device and browser information
 */
export const getDeviceInfo = (): DeviceInfo => {
  // Detect device type based on user agent and screen size
  const detectDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || 
                     (window.innerWidth >= 768 && window.innerWidth <= 1024);
    
    if (isMobile) return 'mobile';
    if (isTablet) return 'tablet';
    return 'desktop';
  };

  // Get network connection info if available
  const getConnectionType = (): string | undefined => {
    // @ts-expect-error - Navigator connection API not in all TypeScript definitions
    const connection = navigator.connection || 
                      // @ts-expect-error - mozConnection is legacy Firefox API not in TypeScript definitions
                      navigator.mozConnection || 
                      // @ts-expect-error - webkitConnection is legacy WebKit API not in TypeScript definitions
                      navigator.webkitConnection;
    
    if (connection) {
      return connection.effectiveType || connection.type || undefined;
    }
    return undefined;
  };

  // Get memory info if available
  const getMemoryInfo = () => {
    // @ts-expect-error - Performance memory not in all TypeScript definitions
    const memory = performance?.memory;
    if (memory) {
      return {
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        totalJSHeapSize: memory.totalJSHeapSize,
        usedJSHeapSize: memory.usedJSHeapSize
      };
    }
    return undefined;
  };

  return {
    userAgent: navigator.userAgent,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    devicePixelRatio: window.devicePixelRatio || 1,
    platform: navigator.platform,
    language: navigator.language,
    connectionType: getConnectionType(),
    memoryInfo: getMemoryInfo(),
    deviceType: detectDeviceType()
  };
};

/**
 * Get a simplified version of device info for logging
 * Reduces payload size for high-frequency events
 */
export const getSimplifiedDeviceInfo = (): Partial<DeviceInfo> => {
  return {
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    deviceType: getDeviceInfo().deviceType,
    connectionType: getDeviceInfo().connectionType
  };
};