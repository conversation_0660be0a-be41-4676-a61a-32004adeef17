import { Timestamp } from 'firebase-admin/firestore';

// Order status types
export type OrderStatus = 
  | 'pending_payment' 
  | 'payment_processing' 
  | 'payment_succeeded' 
  | 'shipped_pending_code' 
  | 'delivered' 
  | 'completed' 
  | 'cancelled' 
  | 'refunded';

// Order interface
export interface Order {
  id: string;
  buyerId: string;
  sellerId: string;
  listingId: string;
  amount: number;
  commission: number;
  sellerAmount: number;
  status: OrderStatus;
  paymentIntentId?: string;
  stripeSessionId?: string;
  category: string;
  title: string;
  shippingAddress?: ShippingAddress;
  trackingInfo?: TrackingInfo;
  secretCode?: string;
  walletAmountUsed?: number;
  cashbackAmount?: number;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
  autoReleaseDate?: Timestamp;
}

// Shipping address interface
export interface ShippingAddress {
  name: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

// Tracking information interface
export interface TrackingInfo {
  carrier: string;
  trackingNumber: string;
  labelUrl?: string;
  shippedDate?: Timestamp;
  estimatedDeliveryDate?: Timestamp;
}

// Wallet interface
export interface Wallet {
  userId: string;
  balance: number;
  lastUpdated: Timestamp;
}

// Shipping label interface
export interface ShippingLabel {
  orderId: string;
  carrier: string;
  trackingNumber: string;
  labelUrl: string;
  createdAt: Timestamp;
}

// Secret code interface
export interface SecretCode {
  orderId: string;
  code: string;
  expiresAt: Timestamp;
  isUsed: boolean;
  usedAt?: Timestamp;
}

// Connect account interface
export interface ConnectAccount {
  userId: string;
  stripeAccountId: string;
  isOnboarded: boolean;
  accountType: 'student' | 'merchant';
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}

// Checkout session request interface
export interface CreateCheckoutSessionRequest {
  listingId: string;
  buyerId: string;
  useWalletBalance?: boolean;
}

// Release funds request interface
export interface ReleaseFundsRequest {
  orderId: string;
  secretCode: string;
}