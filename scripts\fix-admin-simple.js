// Simple script to fix admin user using Firebase CLI
// Run with: firebase functions:shell
// Then call: fixAdminUser({email: '<EMAIL>'})

const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const fixAdminUser = functions.https.onCall(async (data, context) => {
  try {
    const { email } = data;
    
    if (!email) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email is required'
      );
    }

    console.log('Fixing admin user:', email);
    
    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);
    console.log('Found user:', userRecord.uid);
    
    // Set custom claims with both admin and role
    await admin.auth().setCustomUserClaims(userRecord.uid, { 
      admin: true,
      role: 'admin'
    });
    console.log('✅ Custom claims set');
    
    // Update or create user profile in Firestore with complete admin setup
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });
    console.log('✅ Firestore document updated');
    
    // Create admin settings document if it doesn't exist
    await admin.firestore().collection('adminSettings').doc('security').set({
      adminPinRequired: true,
      sessionTimeoutMinutes: 240, // 4 hours
      maxLoginAttempts: 5,
      createdAt: admin.firestore.Timestamp.now()
    }, { merge: true });
    console.log('✅ Admin settings created');
    
    console.log('🎉 Admin user fixed successfully!');
    
    return {
      success: true,
      message: `Admin user fixed for ${email}`,
      uid: userRecord.uid
    };

  } catch (error) {
    console.error('❌ Error fixing admin user:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to fix admin user',
      error
    );
  }
});

module.exports = { fixAdminUser };
