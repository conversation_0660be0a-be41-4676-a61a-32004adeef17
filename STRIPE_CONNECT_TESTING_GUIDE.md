# Hive Campus Stripe Connect Testing Guide

## Overview
This guide provides comprehensive testing instructions for the new Stripe Connect onboarding and checkout system with escrow functionality.

## Prerequisites

### 1. Environment Setup
- Ensure Firebase Functions are deployed with the latest changes
- Verify Stripe test API keys are configured in Firebase Functions environment
- Install Stripe CLI for webhook testing: `stripe login`

### 2. Required Test Data
- Test user accounts (both buyers and sellers)
- Test listings for purchase scenarios
- Access to Firebase Console for monitoring
- Access to Stripe Dashboard for payment verification

## Testing Scenarios

### Scenario 1: New Seller Onboarding Flow

#### Test Steps:
1. **Create New Seller Account**
   - Sign up as a new user
   - Navigate to Profile page
   - Verify "Payout Setup Incomplete" badge is displayed

2. **Trigger Onboarding Modal**
   - Click "Set Up Payouts" button on profile badge
   - Verify StripeOnboardingModal opens with correct messaging
   - Test both account types (Student/Merchant)

3. **Complete Stripe Onboarding**
   - Click "Set Up Payouts" → Choose account type
   - Verify redirect to Stripe onboarding
   - Complete Stripe Express onboarding (use test data)
   - Return to Hive Campus

4. **Verify Onboarding Completion**
   - Check profile badge changes to "Payouts Active"
   - Verify Firebase `connectAccounts` collection updated
   - Check Stripe Dashboard for account status

### Scenario 2: Escrow Flow for Non-Onboarded Sellers

#### Test Steps:
1. **Create Listing as Non-Onboarded Seller**
   - Create user account but don't complete Stripe onboarding
   - Create a test listing
   - Verify onboarding warning appears on AddListing page

2. **Buyer Checkout Process**
   - As different user, navigate to listing
   - Proceed to checkout
   - Verify "Payment Protection Active" message displays
   - Complete checkout with test card (4242 4242 4242 4242)

3. **Verify Escrow Behavior**
   - Check Firebase `orders` collection for `pendingPayout: true`
   - Verify no immediate transfer to seller in Stripe Dashboard
   - Confirm payment captured on platform account

4. **Seller Completes Onboarding**
   - As seller, complete Stripe onboarding
   - Verify automatic payout processing triggers
   - Check Firebase for `pendingPayout: false` and transfer ID
   - Verify transfer appears in Stripe Dashboard

### Scenario 3: Direct Payment for Onboarded Sellers

#### Test Steps:
1. **Create Listing as Onboarded Seller**
   - Use seller with completed Stripe onboarding
   - Create test listing

2. **Buyer Checkout Process**
   - As buyer, proceed to checkout
   - Verify "Instant Payment Ready" message displays
   - Complete checkout

3. **Verify Direct Transfer**
   - Check immediate transfer to seller's Stripe account
   - Verify order status and transfer ID in Firebase
   - Confirm commission deduction is correct

### Scenario 4: Email Reminder System

#### Test Steps:
1. **Create Pending Payout Scenario**
   - Follow Scenario 2 to create pending payout
   - Wait for scheduled function or trigger manually

2. **Test Email Delivery**
   - Verify reminder email sent to seller
   - Check email content and onboarding link
   - Test reminder frequency limits (24-hour cooldown)

3. **Manual Email Testing**
   - Use Firebase Functions emulator
   - Call `sendOnboardingReminderEmail` function directly
   - Verify email template rendering

### Scenario 5: Admin Dashboard Testing

#### Test Steps:
1. **Access Admin Dashboard**
   - Navigate to `/admin` (ensure proper authentication)
   - Click "Payments" tab

2. **Verify Payment Monitoring**
   - Check Stripe Connect statistics display
   - Verify pending payouts table
   - Test "Stripe Dashboard" link

3. **Test Admin Actions**
   - Use `processSellerPendingPayouts` function
   - Test manual payout processing
   - Verify admin controls work correctly

## Webhook Testing

### Setup Stripe CLI
```bash
stripe login
stripe listen --forward-to localhost:5001/your-project/us-central1/stripeApi/webhook
```

### Test Webhook Events
```bash
# Test account update (onboarding completion)
stripe trigger account.updated

# Test payment success
stripe trigger payment_intent.succeeded

# Test transfer events
stripe trigger transfer.created
stripe trigger transfer.failed
```

### Verify Webhook Processing
1. Check Firebase Functions logs
2. Verify database updates in Firebase Console
3. Confirm email triggers work correctly

## UI Testing Checklist

### Mobile Responsiveness
- [ ] Onboarding modal works on mobile (360-420px width)
- [ ] Profile badges display correctly on small screens
- [ ] Checkout flow is mobile-friendly
- [ ] Admin dashboard is responsive

### Dark Mode Support
- [ ] All new components support dark mode
- [ ] Color schemes are consistent
- [ ] Icons and badges are visible in both modes

### Accessibility
- [ ] Proper ARIA labels on interactive elements
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast meets standards

## Performance Testing

### Load Testing
- [ ] Test with multiple concurrent onboarding flows
- [ ] Verify webhook processing under load
- [ ] Check database query performance
- [ ] Monitor Firebase Functions execution time

### Error Handling
- [ ] Test network failures during onboarding
- [ ] Verify graceful handling of Stripe API errors
- [ ] Test webhook retry mechanisms
- [ ] Validate error messages are user-friendly

## Security Testing

### Authentication
- [ ] Verify all Firebase Functions require authentication
- [ ] Test admin function access controls
- [ ] Validate user can only access own data

### Data Protection
- [ ] Ensure sensitive data is not logged
- [ ] Verify Stripe data is handled securely
- [ ] Test webhook signature validation
- [ ] Confirm PCI compliance for payment flows

## Deployment Checklist

### Pre-Deployment
- [ ] All tests pass
- [ ] Environment variables configured
- [ ] Stripe webhooks configured for production
- [ ] Email service configured (Resend/SendGrid)
- [ ] Firebase security rules updated

### Post-Deployment
- [ ] Verify production webhooks work
- [ ] Test end-to-end flow in production
- [ ] Monitor error rates and performance
- [ ] Validate email delivery in production

## Monitoring and Alerts

### Key Metrics to Monitor
- Onboarding completion rate
- Pending payout processing time
- Email delivery success rate
- Webhook processing success rate
- User experience metrics

### Recommended Alerts
- Failed webhook processing
- High pending payout amounts
- Onboarding abandonment rate
- Email delivery failures

## Troubleshooting Common Issues

### Onboarding Issues
- **Problem**: Onboarding link expired
- **Solution**: Generate new link via `getStripeConnectOnboardingLink`

### Payout Issues
- **Problem**: Pending payouts not processing
- **Solution**: Check seller onboarding status and manually trigger processing

### Email Issues
- **Problem**: Reminder emails not sending
- **Solution**: Check email service configuration and rate limits

### Webhook Issues
- **Problem**: Webhooks not processing
- **Solution**: Verify endpoint URL and signature validation

## Support Resources
- Stripe Connect Documentation
- Firebase Functions Documentation
- Hive Campus Development Team
- Stripe Support (for payment issues)
