// Run this script with: node set-admin-claims.js
// Make sure you have firebase-admin installed: npm install firebase-admin

const admin = require('firebase-admin');

// Initialize with your service account or use Firebase CLI auth
// If using Firebase CLI, make sure you're logged in: firebase login
admin.initializeApp({
  projectId: 'h1c1-798a8'
});

async function setAdminClaims() {
  try {
    const adminEmail = '<EMAIL>';
    
    console.log('Setting admin claims for:', adminEmail);
    
    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(adminEmail);
    console.log('Found user UID:', userRecord.uid);
    
    // Set custom claims
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin'
    });
    
    console.log('✅ Custom claims set successfully!');
    
    // Verify the claims were set
    const updatedUser = await admin.auth().getUser(userRecord.uid);
    console.log('Current custom claims:', updatedUser.customClaims);
    
    // Update Firestore document
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });
    
    console.log('✅ Firestore document updated!');
    
    // Create missing collections
    const collections = ['reports', 'shippingLabels', 'walletReports', 'universityAnalytics', 'systemMetrics', 'adminLogs'];
    
    for (const collectionName of collections) {
      await admin.firestore().collection(collectionName).doc('default').set({
        created: admin.firestore.Timestamp.now(),
        type: 'system'
      });
    }
    
    console.log('✅ Missing collections created!');
    console.log('🎉 Admin setup complete!');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

setAdminClaims();
