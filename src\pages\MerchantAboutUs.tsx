import React from 'react';
import { Users, Target, Heart, Zap, Shield, Globe, TrendingUp, Store } from 'lucide-react';


const MerchantAboutUs: React.FC = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'CEO & Co-Founder',
      image: 'https://images.pexels.com/photos/1181519/pexels-photo-1181519.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Former Stanford student passionate about connecting campus communities.'
    },
    {
      name: '<PERSON>',
      role: 'CTO & Co-Founder',
      image: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Tech enthusiast with experience building scalable marketplace platforms.'
    },
    {
      name: '<PERSON>',
      role: 'Head of Community',
      image: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Dedicated to creating safe and inclusive campus marketplaces.'
    },
    {
      name: '<PERSON>',
      role: 'Head of Product',
      image: 'https://images.pexels.com/photos/1674752/pexels-photo-1674752.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Product designer focused on creating intuitive user experiences.'
    }
  ];

  const values = [
    {
      icon: Shield,
      title: 'Safety First',
      description: 'We prioritize the safety and security of our student community through verified accounts and secure transactions.'
    },
    {
      icon: Heart,
      title: 'Community Driven',
      description: 'Built by students, for students. We understand the unique needs of campus life and student budgets.'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'Constantly improving our platform with cutting-edge technology to enhance the student marketplace experience.'
    },
    {
      icon: Globe,
      title: 'Accessibility',
      description: 'Making quality items affordable and accessible to students across universities nationwide.'
    }
  ];

  const stats = [
    { number: '50K+', label: 'Active Students', icon: Users },
    { number: '200+', label: 'Universities', icon: Globe },
    { number: '1M+', label: 'Items Sold', icon: TrendingUp },
    { number: '500+', label: 'Partner Merchants', icon: Store }
  ];

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="w-20 h-20 bg-gradient-to-r from-accent-500 to-orange-500 rounded-3xl mx-auto mb-8 flex items-center justify-center">
              <Heart className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">About Hive Campus</h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
              We're on a mission to create the most trusted and vibrant marketplace for university students, 
              connecting campus communities and making student life more affordable and sustainable.
            </p>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-accent-100 dark:bg-accent-900/20 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                    <IconComponent className="w-8 h-8 text-accent-600 dark:text-accent-400" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {stat.number}
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">{stat.label}</p>
                </div>
              );
            })}
          </div>

          {/* Our Story */}
          <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-8 md:p-12 mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Our Story</h2>
                <div className="space-y-4 text-gray-700 dark:text-gray-300">
                  <p>
                    Hive Campus was born from a simple observation: students needed a better way to buy, sell, 
                    and trade items within their campus communities. As college students ourselves, we experienced 
                    firsthand the challenges of finding affordable textbooks, furniture, and electronics.
                  </p>
                  <p>
                    Traditional marketplaces felt impersonal and unsafe for students. We envisioned a platform 
                    that would combine the convenience of modern e-commerce with the trust and community spirit 
                    of campus life.
                  </p>
                  <p>
                    Today, Hive Campus serves over 50,000 students across 200+ universities, facilitating 
                    millions of dollars in transactions while maintaining the safety and community focus 
                    that makes us unique.
                  </p>
                </div>
              </div>
              <div className="relative">
                <img
                  src="https://images.pexels.com/photos/1438081/pexels-photo-1438081.jpeg?auto=compress&cs=tinysrgb&w=600"
                  alt="Students collaborating"
                  className="rounded-2xl shadow-lg"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-accent-500/20 to-transparent rounded-2xl"></div>
              </div>
            </div>
          </div>

          {/* Our Values */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Values</h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                The principles that guide everything we do at Hive Campus
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {values.map((value, index) => {
                const IconComponent = value.icon;
                return (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300">
                    <div className="w-12 h-12 bg-accent-100 dark:bg-accent-900/20 rounded-xl flex items-center justify-center mb-6">
                      <IconComponent className="w-6 h-6 text-accent-600 dark:text-accent-400" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">{value.title}</h3>
                    <p className="text-gray-600 dark:text-gray-400 leading-relaxed">{value.description}</p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Team Section */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Meet Our Team</h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                The passionate individuals working to revolutionize campus marketplaces
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-24 h-24 rounded-full object-cover mx-auto mb-4 border-4 border-accent-100 dark:border-accent-900/20"
                  />
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">{member.name}</h3>
                  <p className="text-accent-600 dark:text-accent-400 font-semibold mb-3">{member.role}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{member.bio}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Mission Statement */}
          <div className="bg-gradient-to-r from-accent-600 to-orange-600 rounded-3xl shadow-lg p-8 md:p-12 text-white text-center">
            <Target className="w-16 h-16 mx-auto mb-6 opacity-90" />
            <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
            <p className="text-xl leading-relaxed max-w-4xl mx-auto">
              To empower university students with a safe, trusted, and vibrant marketplace that fosters 
              community connections, promotes sustainability through reuse, and makes quality items 
              accessible to every student, regardless of their budget.
            </p>
          </div>
        </div>
      </div>
  );
};

export default MerchantAboutUs;