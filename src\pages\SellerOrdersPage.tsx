import React from 'react';
import { Package, TrendingUp, DollarSign, Users } from 'lucide-react';

import SellerOrdersTable from '../components/SellerOrdersTable';
const SellerOrdersPage: React.FC = () => {

  // Mock stats for the dashboard
  const stats = [
    {
      title: 'Total Orders',
      value: '156',
      change: '+12%',
      icon: Package,
      color: 'bg-blue-500'
    },
    {
      title: 'Revenue',
      value: '$12,450',
      change: '+8%',
      icon: DollarSign,
      color: 'bg-green-500'
    },
    {
      title: 'Conversion Rate',
      value: '3.2%',
      change: '+0.5%',
      icon: TrendingUp,
      color: 'bg-purple-500'
    },
    {
      title: 'Customers',
      value: '124',
      change: '+18%',
      icon: Users,
      color: 'bg-amber-500'
    }
  ];

  return (
      <div className="px-6 py-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Orders Management</h1>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div 
              key={index}
              className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 transition-transform hover:scale-105"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 ${stat.color} rounded-xl flex items-center justify-center`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                  {stat.change}
                </span>
              </div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{stat.title}</h3>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
            </div>
          ))}
        </div>

        {/* Orders Table */}
        <div className="mb-8">
          <SellerOrdersTable />
        </div>

        {/* Shipping Tips */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Shipping Tips</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
              <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">Package Securely</h3>
              <p className="text-sm text-blue-700 dark:text-blue-400">
                Use appropriate packaging materials to ensure items arrive safely. Double-box fragile items.
              </p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4">
              <h3 className="font-semibold text-green-800 dark:text-green-300 mb-2">Ship Promptly</h3>
              <p className="text-sm text-green-700 dark:text-green-400">
                Ship within 2 business days of receiving payment to maintain good buyer ratings.
              </p>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-xl p-4">
              <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-2">Include Details</h3>
              <p className="text-sm text-purple-700 dark:text-purple-400">
                Include a packing slip with order details and a thank you note to enhance the buyer experience.
              </p>
            </div>
          </div>
        </div>
      </div>
  );
};

export default SellerOrdersPage;