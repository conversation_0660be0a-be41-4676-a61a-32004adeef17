import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff, User, ArrowRight, CheckCircle, GraduationCap, ChevronDown, Info } from 'lucide-react';
import { UniversityService, University } from '../../services/universityService';

const SignupScreen: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    university: '',
    studentId: '',
    agreeToTerms: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showUniversityDropdown, setShowUniversityDropdown] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [verificationSent, setVerificationSent] = useState(false);
  const [universities, setUniversities] = useState<University[]>([]);
  const [universitiesLoading, setUniversitiesLoading] = useState(true);

  // Load universities from real data
  useEffect(() => {
    const loadUniversities = async () => {
      try {
        setUniversitiesLoading(true);
        const data = await UniversityService.getUniversities();
        // Filter only active universities and sort by name
        const activeUniversities = data
          .filter(uni => uni.isActive)
          .sort((a, b) => a.name.localeCompare(b.name));
        setUniversities(activeUniversities);
      } catch (err) {
        console.error('Error loading universities:', err);
        // Fallback to a comprehensive set if loading fails
        setUniversities([
          {
            id: 'harvard',
            name: 'Harvard University',
            domain: 'harvard.edu',
            shortName: 'Harvard',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['harvard.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'stanford',
            name: 'Stanford University',
            domain: 'stanford.edu',
            shortName: 'Stanford',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['stanford.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'mit',
            name: 'Massachusetts Institute of Technology',
            domain: 'mit.edu',
            shortName: 'MIT',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['mit.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'berkeley',
            name: 'University of California, Berkeley',
            domain: 'berkeley.edu',
            shortName: 'UC Berkeley',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['berkeley.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'ucla',
            name: 'University of California, Los Angeles',
            domain: 'ucla.edu',
            shortName: 'UCLA',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['ucla.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'msstate',
            name: 'Mississippi State University',
            domain: 'msstate.edu',
            shortName: 'MSU',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['msstate.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          }
        ]);
      } finally {
        setUniversitiesLoading(false);
      }
    };

    loadUniversities();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleUniversitySelect = (university: University) => {
    setFormData(prev => ({ ...prev, university: university.name }));
    setShowUniversityDropdown(false);
  };

  const validateStep1 = () => {
    if (!formData.fullName.trim()) {
      setError('Please enter your full name');
      return false;
    }
    if (!formData.email.trim()) {
      setError('Please enter your email address');
      return false;
    }
    if (!formData.email.includes('@') || !formData.email.includes('.')) {
      setError('Please enter a valid email address');
      return false;
    }

    // Validate .edu domain
    if (!formData.email.endsWith('.edu')) {
      setError('Please use a valid university .edu email address');
      return false;
    }

    if (!formData.password) {
      setError('Please enter a password');
      return false;
    }
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    return true;
  };

  const validateStep2 = () => {
    if (!formData.university) {
      setError('Please select your university');
      return false;
    }

    // Validate email domain matches selected university
    const selectedUniversity = universities.find(uni => uni.name === formData.university);
    if (selectedUniversity && formData.email) {
      const emailDomain = formData.email.split('@')[1];
      if (!selectedUniversity.allowedEmailDomains.includes(emailDomain)) {
        setError(`Email domain must be one of: ${selectedUniversity.allowedEmailDomains.map(d => '@' + d).join(', ')} for ${selectedUniversity.name}`);
        return false;
      }
    }

    if (!formData.studentId.trim()) {
      setError('Please enter your student ID');
      return false;
    }
    if (!formData.agreeToTerms) {
      setError('You must agree to the terms and conditions');
      return false;
    }
    return true;
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      if (validateStep1()) {
        setError(null);
        setCurrentStep(2);
      }
    }
  };

  const handlePrevStep = () => {
    setError(null);
    setCurrentStep(1);
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep2()) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Show verification sent state
      setVerificationSent(true);
    } catch {
      setError('An error occurred during signup. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationComplete = () => {
    navigate('/home');
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-[10%] right-[15%] w-64 h-64 bg-primary-400/20 dark:bg-primary-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-[20%] left-[10%] w-72 h-72 bg-accent-400/20 dark:bg-accent-600/10 rounded-full blur-3xl"></div>
      </div>
      
      {/* Header */}
      <header className="relative z-10 pt-6 px-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <img
              src="/hive-campus-logo.svg"
              alt="Hive Campus Logo"
              className="w-10 h-10"
            />
            <span className="text-xl font-bold text-gray-900 dark:text-white">Hive Campus</span>
          </div>
          <Link 
            to="/login" 
            className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
          >
            Sign In
          </Link>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="flex-1 flex flex-col justify-center px-6 py-8 sm:px-12 relative z-10">
        <div className="max-w-md w-full mx-auto">
          {/* Glass Card */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-2xl shadow-xl overflow-hidden">
            <div className="p-8">
              {!verificationSent ? (
                <>
                  <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Create Account</h1>
                    <p className="text-gray-600 dark:text-gray-400">
                      Join the student marketplace
                    </p>
                  </div>
                  
                  {/* Progress Steps */}
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex-1">
                      <div className={`h-1 ${currentStep >= 1 ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-700'} rounded-full`}></div>
                    </div>
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary-500 text-white font-medium text-sm mx-2">
                      1
                    </div>
                    <div className="flex-1">
                      <div className={`h-1 ${currentStep >= 2 ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-700'} rounded-full`}></div>
                    </div>
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full font-medium text-sm mx-2 ${
                      currentStep >= 2 
                        ? 'bg-primary-500 text-white' 
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                    }`}>
                      2
                    </div>
                    <div className="flex-1">
                      <div className={`h-1 ${currentStep >= 3 ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-700'} rounded-full`}></div>
                    </div>
                  </div>
                  
                  {error && (
                    <div className="mb-6 p-4 bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl text-red-600 dark:text-red-400 text-sm">
                      {error}
                    </div>
                  )}
                  
                  <form onSubmit={handleSignup} className="space-y-6">
                    {currentStep === 1 && (
                      <>
                        {/* Full Name Input */}
                        <div>
                          <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Full Name
                          </label>
                          <div className="relative">
                            <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                              id="fullName"
                              name="fullName"
                              type="text"
                              value={formData.fullName}
                              onChange={handleChange}
                              placeholder="John Doe"
                              className="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                              required
                            />
                          </div>
                        </div>
                        
                        {/* Email Input */}
                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Email Address
                          </label>
                          <div className="relative">
                            <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                              id="email"
                              name="email"
                              type="email"
                              value={formData.email}
                              onChange={handleChange}
                              placeholder="<EMAIL>"
                              className="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                              required
                            />
                          </div>
                          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            We recommend using your university email for verification
                          </p>
                        </div>
                        
                        {/* Password Input */}
                        <div>
                          <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Password
                          </label>
                          <div className="relative">
                            <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                              id="password"
                              name="password"
                              type={showPassword ? "text" : "password"}
                              value={formData.password}
                              onChange={handleChange}
                              placeholder="••••••••"
                              className="w-full pl-12 pr-12 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                              required
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute right-4 top-1/2 transform -translate-y-1/2"
                            >
                              {showPassword ? (
                                <EyeOff className="w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                              ) : (
                                <Eye className="w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                              )}
                            </button>
                          </div>
                          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Must be at least 8 characters
                          </p>
                        </div>
                        
                        {/* Confirm Password Input */}
                        <div>
                          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Confirm Password
                          </label>
                          <div className="relative">
                            <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                              id="confirmPassword"
                              name="confirmPassword"
                              type={showConfirmPassword ? "text" : "password"}
                              value={formData.confirmPassword}
                              onChange={handleChange}
                              placeholder="••••••••"
                              className="w-full pl-12 pr-12 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                              required
                            />
                            <button
                              type="button"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              className="absolute right-4 top-1/2 transform -translate-y-1/2"
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                              ) : (
                                <Eye className="w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                              )}
                            </button>
                          </div>
                        </div>
                        
                        {/* Next Button */}
                        <button
                          type="button"
                          onClick={handleNextStep}
                          className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg transform hover:translate-y-[-2px]"
                        >
                          <span>Continue</span>
                          <ArrowRight className="w-5 h-5" />
                        </button>
                      </>
                    )}
                    
                    {currentStep === 2 && (
                      <>
                        {/* University Selection */}
                        <div>
                          <label htmlFor="university" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            University
                          </label>
                          <div className="relative">
                            <GraduationCap className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <button
                              type="button"
                              onClick={() => setShowUniversityDropdown(!showUniversityDropdown)}
                              className="w-full pl-12 pr-12 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 text-left flex items-center justify-between"
                            >
                              <span className={formData.university ? "text-gray-900 dark:text-white" : "text-gray-400"}>
                                {formData.university || "Select your university"}
                              </span>
                              <ChevronDown className="w-5 h-5 text-gray-400" />
                            </button>
                            
                            {showUniversityDropdown && (
                              <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-10 max-h-60 overflow-y-auto">
                                {universitiesLoading ? (
                                  <div className="px-4 py-3 text-gray-500 dark:text-gray-400">Loading universities...</div>
                                ) : universities.length === 0 ? (
                                  <div className="px-4 py-3 text-gray-500 dark:text-gray-400">No universities available</div>
                                ) : (
                                  universities.map((uni) => (
                                    <button
                                      key={uni.id}
                                      type="button"
                                      onClick={() => handleUniversitySelect(uni)}
                                      className="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-900 dark:text-white"
                                    >
                                      {uni.name}
                                    </button>
                                  ))
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* Student ID Input */}
                        <div>
                          <label htmlFor="studentId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Student ID
                          </label>
                          <div className="relative">
                            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
                                <rect x="3" y="4" width="18" height="16" rx="2" />
                                <circle cx="9" cy="10" r="2" />
                                <path d="M15 8h2" />
                                <path d="M15 12h2" />
                                <path d="M7 16h10" />
                              </svg>
                            </div>
                            <input
                              id="studentId"
                              name="studentId"
                              type="text"
                              value={formData.studentId}
                              onChange={handleChange}
                              placeholder="Enter your student ID"
                              className="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                              required
                            />
                          </div>
                          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <Info className="w-3 h-3 mr-1" />
                            This will be used to verify your student status
                          </p>
                        </div>
                        
                        {/* Terms and Conditions */}
                        <div className="flex items-start">
                          <div className="flex items-center h-5">
                            <input
                              id="agreeToTerms"
                              name="agreeToTerms"
                              type="checkbox"
                              checked={formData.agreeToTerms}
                              onChange={handleChange}
                              className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                            />
                          </div>
                          <div className="ml-3 text-sm">
                            <label htmlFor="agreeToTerms" className="text-gray-700 dark:text-gray-300">
                              I agree to the{' '}
                              <Link to="/terms" className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                                Terms of Service
                              </Link>{' '}
                              and{' '}
                              <Link to="/privacy" className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                                Privacy Policy
                              </Link>
                            </label>
                          </div>
                        </div>
                        
                        {/* Action Buttons */}
                        <div className="flex space-x-4">
                          <button
                            type="button"
                            onClick={handlePrevStep}
                            className="flex-1 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
                          >
                            <ArrowRight className="w-5 h-5 transform rotate-180" />
                            <span>Back</span>
                          </button>
                          
                          <button
                            type="submit"
                            disabled={isLoading}
                            className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg transform hover:translate-y-[-2px] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
                          >
                            {isLoading ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>Creating...</span>
                              </>
                            ) : (
                              <>
                                <span>Create Account</span>
                                <ArrowRight className="w-5 h-5" />
                              </>
                            )}
                          </button>
                        </div>
                      </>
                    )}
                  </form>
                </>
              ) : (
                // Verification Sent State
                <div className="text-center py-6">
                  <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="w-10 h-10 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Verification Email Sent</h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    We've sent a verification link to <strong>{formData.email}</strong>. Please check your inbox and click the link to verify your account.
                  </p>
                  
                  <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-100 dark:border-primary-800 rounded-xl p-4 mb-6 text-left">
                    <p className="text-sm text-primary-800 dark:text-primary-300 flex items-start">
                      <Info className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
                      <span>
                        If you don't see the email in your inbox, please check your spam folder. The email should arrive within a few minutes.
                      </span>
                    </p>
                  </div>
                  
                  <button
                    onClick={handleVerificationComplete}
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg transform hover:translate-y-[-2px]"
                  >
                    <span>Continue to Dashboard</span>
                    <ArrowRight className="w-5 h-5" />
                  </button>
                  
                  <div className="mt-4">
                    <button
                      type="button"
                      className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                    >
                      Resend verification email
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Sign In Link */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Already have an account?{' '}
              <Link 
                to="/login" 
                className="font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
              >
                Sign in
              </Link>
            </p>
          </div>
          
          {/* Merchant Signup */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Are you a business?{' '}
              <Link 
                to="/merchant-signup" 
                className="font-medium text-accent-600 dark:text-accent-400 hover:text-accent-700 dark:hover:text-accent-300 transition-colors"
              >
                Create a merchant account
              </Link>
            </p>
          </div>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="relative z-10 py-6 px-6 text-center">
        <p className="text-xs text-gray-600 dark:text-gray-400">
          &copy; {new Date().getFullYear()} Hive Campus. All rights reserved.
        </p>
        <div className="flex justify-center space-x-4 mt-2">
          <Link to="/terms" className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            Terms
          </Link>
          <Link to="/privacy" className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            Privacy
          </Link>
          <Link to="/help" className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            Help
          </Link>
        </div>
      </footer>
    </div>
  );
};

export default SignupScreen;