import React, { useState, useEffect } from 'react';
import { X, Package, Lock, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { Order } from '../firebase/types';

interface DeliveryNotification {
  id: string;
  orderId: string;
  title: string;
  message: string;
  actionRequired: boolean;
  expiresAt: Date;
  createdAt: Date;
}

interface DeliveryConfirmationPopupProps {
  notification: DeliveryNotification;
  onClose: () => void;
  onCodeSubmitted: () => void;
}

const DeliveryConfirmationPopup: React.FC<DeliveryConfirmationPopupProps> = ({
  notification,
  onClose,
  onCodeSubmitted
}) => {
  const { user: _user } = useAuth();
  const { releaseFundsWithCode, getOrderById } = useStripeCheckout();
  
  const [secretCode, setSecretCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingNewCode, setIsGeneratingNewCode] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [order, setOrder] = useState<Order | null>(null);

  // Load order details
  useEffect(() => {
    const loadOrder = async () => {
      try {
        const orderData = await getOrderById(notification.orderId);
        setOrder(orderData);
      } catch (error) {
        console.error('Error loading order:', error);
      }
    };

    loadOrder();
  }, [notification.orderId, getOrderById]);

  // Handle secret code submission
  const handleSubmitCode = async () => {
    if (!secretCode || secretCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const success = await releaseFundsWithCode(notification.orderId, secretCode);
      
      if (success) {
        setSuccess(true);
        onCodeSubmitted();
        
        // Auto-close after 3 seconds
        setTimeout(() => {
          onClose();
        }, 3000);
      }
    } catch (error: unknown) {
      console.error('Error submitting code:', error);
      setError(error instanceof Error ? error.message : 'Failed to verify code');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle generating new secret code
  const handleGenerateNewCode = async () => {
    setIsGeneratingNewCode(true);
    setError(null);

    try {
      const generateNewCodeFn = httpsCallable(functions, 'generateNewSecretCode');
      const result = await generateNewCodeFn({ orderId: notification.orderId });
      
      const data = result.data as { success: boolean; newSecretCode: string; message: string };
      
      if (data.success) {
        // Show the new code briefly
        alert(`New secret code generated: ${data.newSecretCode}`);
        
        // Reload order to get updated code
        const updatedOrder = await getOrderById(notification.orderId);
        setOrder(updatedOrder);
      }
    } catch (error: unknown) {
      console.error('Error generating new code:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate new code');
    } finally {
      setIsGeneratingNewCode(false);
    }
  };

  // Calculate time remaining
  const timeRemaining = Math.max(0, notification.expiresAt.getTime() - Date.now());
  const hoursRemaining = Math.floor(timeRemaining / (1000 * 60 * 60));

  if (success) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full p-6 animate-in fade-in duration-300">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Payment Released! 🎉
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Thank you for confirming delivery. The payment has been released to the seller.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              This popup will close automatically...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full p-6 animate-in fade-in duration-300">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
              <Package className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                Order Delivered!
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {hoursRemaining}h remaining to confirm
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Order Info */}
        {order && (
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 mb-6">
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">
              {order.title}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Order #{notification.orderId.slice(-8)}
            </p>
          </div>
        )}

        {/* Message */}
        <div className="mb-6">
          <p className="text-gray-700 dark:text-gray-300 text-center">
            {notification.message}
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
              <div>
                <p className="font-medium text-red-900 dark:text-red-200">Error</p>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Code Entry */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Enter 6-digit secret code
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={secretCode}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                  setSecretCode(value);
                  setError(null);
                }}
                placeholder="000000"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-center text-lg font-mono tracking-widest"
                maxLength={6}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleSubmitCode}
              disabled={isSubmitting || secretCode.length !== 6}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Confirming...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4" />
                  <span>Confirm Delivery & Release Payment</span>
                </>
              )}
            </button>

            <button
              onClick={handleGenerateNewCode}
              disabled={isGeneratingNewCode}
              className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2"
            >
              {isGeneratingNewCode ? (
                <>
                  <div className="w-4 h-4 border-2 border-gray-400 border-t-gray-600 rounded-full animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4" />
                  <span>Generate New Code</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Auto-release Warning */}
        <div className="mt-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl">
          <p className="text-sm text-yellow-800 dark:text-yellow-200 text-center">
            ⏰ If you don't confirm within {hoursRemaining} hours, payment will be automatically released to the seller.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DeliveryConfirmationPopup;
