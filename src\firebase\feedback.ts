import { httpsCallable } from 'firebase/functions';
import { functions } from './config';

// Submit feedback
export const submitFeedback = async (rating: number, message: string) => {
  try {
    const submitFeedbackFn = httpsCallable(functions, 'submitFeedback');
    const result = await submitFeedbackFn({ rating, message });
    return result.data;
  } catch (error: unknown) {
    console.error('Error submitting feedback:', error);
    throw error;
  }
};

// Report an issue
export const reportIssue = async (
  category: string,
  title: string,
  description: string,
  screenshotURL?: string
) => {
  try {
    const reportIssueFn = httpsCallable(functions, 'reportIssue');
    const result = await reportIssueFn({
      category,
      title,
      description,
      screenshotURL
    });
    return result.data;
  } catch (error: unknown) {
    console.error('Error reporting issue:', error);
    throw error;
  }
};

// Get feedback (admin only)
export const getFeedback = async (limit: number = 50, lastVisible?: string) => {
  try {
    const getFeedbackFn = httpsCallable(functions, 'getFeedback');
    const result = await getFeedbackFn({ limit, lastVisible });
    return result.data;
  } catch (error: unknown) {
    console.error('Error getting feedback:', error);
    throw error;
  }
};

// Get issue reports (admin only)
export const getIssueReports = async (
  status?: 'open' | 'in_progress' | 'resolved' | 'closed',
  limit: number = 50,
  lastVisible?: string
) => {
  try {
    const getIssueReportsFn = httpsCallable(functions, 'getIssueReports');
    const result = await getIssueReportsFn({ status, limit, lastVisible });
    return result.data;
  } catch (error: unknown) {
    console.error('Error getting issue reports:', error);
    throw error;
  }
};

// Update issue status (admin only)
export const updateIssueStatus = async (
  issueId: string,
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
) => {
  try {
    const updateIssueStatusFn = httpsCallable(functions, 'updateIssueStatus');
    const result = await updateIssueStatusFn({ issueId, status });
    return result.data;
  } catch (error: unknown) {
    console.error('Error updating issue status:', error);
    throw error;
  }
};

// Get user's own issue reports
export const getUserIssueReports = async () => {
  try {
    const getUserIssueReportsFn = httpsCallable(functions, 'getUserIssueReports');
    const result = await getUserIssueReportsFn();
    return result.data;
  } catch (error: unknown) {
    console.error('Error getting user issue reports:', error);
    throw error;
  }
};