module.exports = {
  root: true,
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react-hooks'],
  rules: {
    // Disable the no-explicit-any rule
    '@typescript-eslint/no-explicit-any': 'off',
    // Disable the no-unused-vars rule
    '@typescript-eslint/no-unused-vars': 'off',
    // Disable the no-require-imports rule
    '@typescript-eslint/no-require-imports': 'off',
    // Disable the no-useless-escape rule
    'no-useless-escape': 'off',
    // Disable the react-hooks/exhaustive-deps rule
    'react-hooks/exhaustive-deps': 'off',
    // Disable the react-hooks/rules-of-hooks rule
    'react-hooks/rules-of-hooks': 'off',
    // Disable the react-refresh/only-export-components rule
    'react-refresh/only-export-components': 'off'
  }
};