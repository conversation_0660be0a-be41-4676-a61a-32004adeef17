import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { generateAISummary } from './aiSummary';
import { sendReportEmail } from '../utils/email';

// Export alert functions
export * from './alerts';

// Collection references
const REEFLEX_ACTIVITY_COLLECTION = 'reeflex_activity';
const REEFLEX_FEEDBACK_COLLECTION = 'reeflex_feedback';
const REEFLEX_REPORTS_COLLECTION = 'reeflex_reports';

/**
 * Scheduled function to generate and send daily ReeFlex report
 * Runs once per day at 5:00 AM
 */
export const sendReeFlexReport = functions.pubsub
  .schedule('0 5 * * *')
  .timeZone('America/New_York')
  .onRun(async () => {
    const db = admin.firestore();
    
    try {
      // Calculate time range for the report (last 24 hours)
      const endTime = admin.firestore.Timestamp.now();
      const startTime = new admin.firestore.Timestamp(
        endTime.seconds - 24 * 60 * 60,
        endTime.seconds
      );
      
      // Fetch activity data from the last 24 hours
      const activitySnapshot = await db
        .collection(REEFLEX_ACTIVITY_COLLECTION)
        .where('timestamp', '>=', startTime)
        .where('timestamp', '<=', endTime)
        .get();
      
      // Fetch feedback data from the last 24 hours
      const feedbackSnapshot = await db
        .collection(REEFLEX_FEEDBACK_COLLECTION)
        .where('timestamp', '>=', startTime)
        .where('timestamp', '<=', endTime)
        .get();
      
      // Convert to arrays
      const activities = activitySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      const feedback = feedbackSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // Log the data volume
      functions.logger.info(`Generating report with ${activities.length} activities and ${feedback.length} feedback items`);
      
      // Aggregate data for the report
      const report = await generateReport(activities, feedback);
      
      // Generate AI summary using OpenAI
      const aiSummary = await generateAISummary(report);
      
      // Store the report in Firestore for historical access and dashboard
      const reportId = await storeReport(report, aiSummary);
      
      // Send email with the report
      const emailSent = await sendReportEmail(report, aiSummary);
      
      // Log the result
      if (emailSent) {
        functions.logger.info(`Daily ReeFlex report sent successfully. Report ID: ${reportId}`);
      } else {
        functions.logger.warn(`Failed to send email, but report was generated and stored. Report ID: ${reportId}`);
      }
      
      return null;
    } catch (error) {
      functions.logger.error('Error generating ReeFlex report:', error);
      throw error;
    }
  });

/**
 * Generate a structured report from the activity and feedback data
 */
async function generateReport(activities: FirebaseFirestore.DocumentData[], feedback: FirebaseFirestore.DocumentData[]) {
  // Error and crash statistics
  const errors = activities.filter(a => 
    ['javascript_error', 'promise_rejection', 'api_error', 'api_failed'].includes(a.eventType)
  );
  
  // Performance issues
  const performanceIssues = activities.filter(a => 
    ['slow_navigation', 'slow_resource', 'long_task', 'api_slow'].includes(a.eventType)
  );
  
  // User behavior
  const pageViews = activities.filter(a => a.eventType === 'page_viewed');
  const userInteractions = activities.filter(a => a.eventType === 'user_interaction');
  const checkoutEvents = activities.filter(a => 
    ['checkout_started', 'checkout_completed', 'checkout_failed'].includes(a.eventType)
  );
  
  // Most visited pages
  const pageViewCounts = pageViews.reduce((acc: Record<string, number>, curr) => {
    const route = curr.route;
    acc[route] = (acc[route] || 0) + 1;
    return acc;
  }, {});
  
  // Sort pages by view count
  const topPages = Object.entries(pageViewCounts)
    .sort((a, b) => (b[1] as number) - (a[1] as number))
    .slice(0, 10)
    .map(([route, count]) => ({ route, count }));
  
  // Checkout funnel analysis
  const checkoutStarted = checkoutEvents.filter(e => e.eventType === 'checkout_started').length;
  const checkoutCompleted = checkoutEvents.filter(e => e.eventType === 'checkout_completed').length;
  const checkoutFailed = checkoutEvents.filter(e => e.eventType === 'checkout_failed').length;
  const checkoutDropOffRate = checkoutStarted > 0 
    ? ((checkoutStarted - checkoutCompleted) / checkoutStarted) * 100 
    : 0;
  
  // Error frequency by type
  const errorTypes = errors.reduce((acc: Record<string, number>, curr) => {
    const type = curr.eventType;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});
  
  // Group feedback by category
  const feedbackByCategory = feedback.reduce((acc: Record<string, FirebaseFirestore.DocumentData[]>, curr) => {
    const category = curr.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(curr);
    return acc;
  }, {});
  
  // Extract user quotes from feedback
  const userQuotes = feedback
    .filter(f => f.message && f.message.trim().length > 0)
    .map(f => ({
      message: f.message,
      type: f.feedbackType,
      category: f.category,
      route: f.route
    }))
    .slice(0, 10); // Limit to 10 quotes
  
  // Compile the report
  return {
    timeRange: {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    },
    summary: {
      totalEvents: activities.length,
      totalErrors: errors.length,
      totalFeedback: feedback.length,
      totalPageViews: pageViews.length,
      totalInteractions: userInteractions.length
    },
    errors: {
      count: errors.length,
      byType: errorTypes,
      details: errors.slice(0, 20).map(e => ({
        type: e.eventType,
        message: e.data.message || e.data.error || 'Unknown error',
        route: e.route,
        userId: e.userId,
        timestamp: e.timestamp.toDate().toISOString(),
        sentryEventId: e.sentryEventId
      }))
    },
    performance: {
      issues: performanceIssues.length,
      slowestPages: performanceIssues
        .filter(p => p.eventType === 'slow_navigation')
        .sort((a, b) => b.data.duration_ms - a.data.duration_ms)
        .slice(0, 5)
        .map(p => ({
          route: p.route,
          duration: p.data.duration_ms,
          timestamp: p.timestamp.toDate().toISOString()
        })),
      slowestResources: performanceIssues
        .filter(p => p.eventType === 'slow_resource')
        .sort((a, b) => b.data.duration_ms - a.data.duration_ms)
        .slice(0, 5)
        .map(p => ({
          url: p.data.resource_url,
          type: p.data.resource_type,
          duration: p.data.duration_ms
        }))
    },
    userBehavior: {
      topPages,
      checkoutFunnel: {
        started: checkoutStarted,
        completed: checkoutCompleted,
        failed: checkoutFailed,
        dropOffRate: checkoutDropOffRate.toFixed(2) + '%'
      }
    },
    feedback: {
      positive: feedback.filter(f => f.feedbackType === 'positive').length,
      negative: feedback.filter(f => f.feedbackType === 'negative').length,
      neutral: feedback.filter(f => f.feedbackType === 'neutral').length,
      byCategory: feedbackByCategory,
      userQuotes
    }
  };
}

/**
 * Store the report in Firestore for historical access and dashboard
 */
async function storeReport(report: Record<string, unknown>, aiSummary: string): Promise<string> {
  try {
    const reportRef = await admin.firestore().collection(REEFLEX_REPORTS_COLLECTION).add({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      report,
      aiSummary,
      emailSent: true
    });
    
    return reportRef.id;
  } catch (error) {
    functions.logger.error('Error storing ReeFlex report:', error);
    throw error;
  }
}