import React from 'react';
import { Home, Plus, User, Wallet, MessageCircle, Shield, BarChart3, Users, Package, Flag, Activity, Database, Settings, Search } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';
import UserProfileDropdown from './UserProfileDropdown';
import MerchantProfileDropdown from './MerchantProfileDropdown';
import FloatingChat from './FloatingChat';
import { useAuth } from '../hooks/useAuth';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const { currentUser, userProfile, isLoading, isAdmin, userRole } = useAuth();
  
  const isMerchant = userRole === 'merchant';

  // Create user object for dropdown (fallback to defaults if not available)
  const user = {
    name: userProfile?.name || currentUser?.displayName || (isAdmin ? 'Admin User' : 'User'),
    email: userProfile?.email || currentUser?.email || '',
    avatar: userProfile?.profilePictureURL || '/placeholder-avatar.svg',
    university: isAdmin ? 'System Administrator' : (isMerchant ? 'Merchant Partner' : (userProfile?.university || 'University')),
    verified: isAdmin || false
  };

  // Different navigation items for admin vs merchant vs regular users
  let navItems;
  if (isAdmin) {
    navItems = [
      { icon: BarChart3, label: 'Dashboard', path: '/admin/dashboard-new' },
      { icon: Users, label: 'Users', path: '/admin/users' },
      { icon: Package, label: 'Listings', path: '/admin/listings' },
      { icon: Flag, label: 'Reports', path: '/admin/reports' },
      { icon: Shield, label: 'Security', path: '/admin/security' },
      { icon: Activity, label: 'ReeFlex', path: '/admin/reeflex' },
      { icon: Database, label: 'Analytics', path: '/admin/analytics' },
      { icon: Settings, label: 'Settings', path: '/admin/settings' },
    ];
  } else if (isMerchant) {
    navItems = [
      { icon: BarChart3, label: 'Dashboard', path: '/merchant/dashboard' },
      { icon: Package, label: 'Products', path: '/merchant/products' },
      { icon: MessageCircle, label: 'Messages', path: '/merchant/messages' },
      { icon: User, label: 'Profile', path: '/merchant/profile' },
    ];
  } else {
    navItems = [
      { icon: Home, label: 'Home', path: '/home' },
      { icon: Search, label: 'Search', path: '/search' },
      { icon: Plus, label: 'Sell', path: '/add-listing' },
      { icon: Wallet, label: 'Wallet', path: '/wallet' },
      { icon: User, label: 'Profile', path: '/profile' },
    ];
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to={isAdmin ? "/admin/dashboard-new" : (isMerchant ? "/merchant/dashboard" : "/home")} className="flex items-center space-x-2">
              <img
                src="/hive-campus-logo.svg"
                alt="Hive Campus Logo"
                className="w-8 h-8"
              />
              <span className="text-xl font-bold text-gray-900 dark:text-white">Hive Campus</span>
              {isAdmin && (
                <span className="text-sm text-red-600 dark:text-red-400 font-medium">Admin</span>
              )}
              {isMerchant && (
                <span className="text-sm text-accent-600 dark:text-accent-400 font-medium">Partner</span>
              )}
            </Link>
            
            {/* Header Actions - User Profile Dropdown */}
            <div className="flex items-center">
              {isLoading ? (
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
              ) : currentUser ? (
                isMerchant ? (
                  <MerchantProfileDropdown merchant={{
                    name: user.name,
                    email: user.email,
                    avatar: user.avatar,
                    businessName: user.university,
                    verified: user.verified,
                    businessType: 'Retail Store'
                  }} />
                ) : (
                  <UserProfileDropdown user={user} />
                )
              ) : (
                <div className="flex items-center space-x-2">
                  <Link 
                    to="/login" 
                    className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                  >
                    Sign In
                  </Link>
                  <Link 
                    to="/signup" 
                    className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 pb-20 md:pb-0 md:ml-64">
        {children}
      </main>

      {/* Bottom Navigation (Mobile) */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 md:hidden">
        <div className="flex justify-around py-2">
          {navItems.slice(0, 5).map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            const hasNotification = false; // No notifications for current nav items
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex flex-col items-center p-2 relative ${
                  isActive 
                    ? (isAdmin ? 'text-red-600 dark:text-red-400' : (isMerchant ? 'text-accent-600 dark:text-accent-400' : 'text-primary-600 dark:text-primary-400'))
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                <Icon className="w-6 h-6" />
                <span className="text-xs mt-1">{item.label}</span>
                {hasNotification && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                )}
              </Link>
            );
          })}
        </div>
      </nav>

      {/* Desktop Sidebar Navigation */}
      <aside className="hidden md:fixed md:left-0 md:top-16 md:bottom-0 md:w-64 md:bg-white md:dark:bg-gray-800 md:border-r md:border-gray-200 md:dark:border-gray-700 md:flex md:flex-col">
        <nav className="flex-1 p-4 space-y-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            const hasNotification = false; // No notifications for current nav items
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 relative ${
                  isActive
                    ? (isAdmin ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400' : (isMerchant ? 'bg-accent-50 dark:bg-accent-900/20 text-accent-600 dark:text-accent-400' : 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'))
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
                {hasNotification && (
                  <span className="absolute top-2 left-8 w-3 h-3 bg-red-500 rounded-full"></span>
                )}
              </Link>
            );
          })}
        </nav>
      </aside>

      {/* Floating Chat Button - Only for students */}
      {!isAdmin && !isMerchant && currentUser && (
        <FloatingChat position="bottom-right" />
      )}
    </div>
  );
};

export default Layout;