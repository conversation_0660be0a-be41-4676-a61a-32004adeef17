# Hive Campus Stripe Connect Deployment Checklist

## Pre-Deployment Setup

### 1. Environment Configuration
- [ ] **Firebase Functions Environment Variables**
  ```bash
  firebase functions:config:set stripe.secret_key="sk_live_..."
  firebase functions:config:set stripe.webhook_secret="whsec_..."
  firebase functions:config:set email.api_key="your_email_api_key"
  ```

- [ ] **Stripe Configuration**
  - [ ] Switch to live API keys in production
  - [ ] Configure production webhook endpoints
  - [ ] Set up Stripe Connect application settings
  - [ ] Configure payout schedules

### 2. Database Migration
- [ ] **Add New Fields to Existing Collections**
  - [ ] Update orders collection with escrow fields
  - [ ] Create connectAccounts collection
  - [ ] Add proper indexes for efficient querying

### 3. Deploy Firebase Functions
```bash
firebase deploy --only functions
```

### 4. Configure Stripe Webhooks
- [ ] **Production Webhook Endpoints**
  ```
  https://your-project.cloudfunctions.net/stripeApi/webhook
  ```

- [ ] **Required Webhook Events**
  - [ ] `account.updated`
  - [ ] `payment_intent.succeeded`
  - [ ] `transfer.created`
  - [ ] `transfer.failed`

## Post-Deployment Verification

### 1. Smoke Tests
- [ ] User can create Stripe Connect account
- [ ] Onboarding flow works end-to-end
- [ ] Checkout handles both onboarded and non-onboarded sellers
- [ ] Email reminders are sent correctly
- [ ] Admin dashboard displays payment data

### 2. Integration Tests
- [ ] Test webhooks with real Stripe events
- [ ] Verify email delivery in production
- [ ] Confirm payout processing works
- [ ] Test admin functions

## Success Criteria
- [ ] 95%+ onboarding completion rate
- [ ] <24 hour average payout processing time
- [ ] 99%+ webhook processing success rate
- [ ] Positive user feedback on new flow
