"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateProfile = void 0;
// Profile management functions
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const cors_1 = __importDefault(require("cors"));
const corsHandler = (0, cors_1.default)({ origin: true });
// Function to update user profile with proper validation
exports.updateProfile = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onRequest(async (req, res) => {
    corsHandler(req, res, async () => {
        try {
            if (req.method !== 'POST') {
                res.status(405).json({ error: 'Method not allowed' });
                return;
            }
            // Get the ID token from the Authorization header
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const idToken = authHeader.split('Bearer ')[1];
            const decodedToken = await admin.auth().verifyIdToken(idToken);
            const uid = decodedToken.uid;
            const { name, bio, graduationYear, major, university } = req.body;
            // Validate required fields
            if (!name || !name.trim()) {
                res.status(400).json({ error: 'Name is required' });
                return;
            }
            // Prepare update data
            const updateData = {
                name: name.trim(),
                updatedAt: admin.firestore.Timestamp.now()
            };
            if (bio !== undefined)
                updateData.bio = bio.trim();
            if (major !== undefined)
                updateData.major = major.trim();
            if (university !== undefined)
                updateData.university = university.trim();
            if (graduationYear !== undefined) {
                const year = parseInt(graduationYear);
                if (year >= 2020 && year <= 2035) {
                    updateData.graduationYear = year;
                }
            }
            // Update user document
            await admin.firestore().collection('users').doc(uid).update(updateData);
            // Update Auth displayName
            await admin.auth().updateUser(uid, {
                displayName: name.trim()
            });
            res.json({
                success: true,
                message: 'Profile updated successfully',
                data: updateData
            });
        }
        catch (error) {
            console.error('Error updating profile:', error);
            res.status(500).json({ error: 'Failed to update profile' });
        }
    });
});
