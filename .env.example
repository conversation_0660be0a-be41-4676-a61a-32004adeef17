# Firebase Configuration (Required)
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Stripe Configuration (Required)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Optional: Analytics and Monitoring
VITE_REEFLEX_PROJECT_ID=your_reeflex_project_id
VITE_SENTRY_DSN=your_sentry_dsn

# Production Environment Variables
# Note: Use actual production keys when deploying to production
# Never commit actual keys to your repository
# For production, use pk_live_ for Stripe keys and production Firebase config