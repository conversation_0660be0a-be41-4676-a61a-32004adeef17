<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Stripe Connect</title>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-functions-compat.js"></script>
</head>
<body>
    <h1>Test Stripe Connect Account Creation</h1>
    <div id="status">Not logged in</div>
    <button id="login" onclick="login()">Login</button>
    <button id="createAccount" onclick="createStripeAccount()" disabled>Create Stripe Account</button>
    <div id="result"></div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "*********",
            appId: "1:*********:web:abcdefghijklmnop"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const functions = firebase.functions();

        // Auth state listener
        auth.onAuthStateChanged((user) => {
            const statusDiv = document.getElementById('status');
            const createButton = document.getElementById('createAccount');
            
            if (user) {
                statusDiv.textContent = `Logged in as: ${user.email}`;
                createButton.disabled = false;
            } else {
                statusDiv.textContent = 'Not logged in';
                createButton.disabled = true;
            }
        });

        async function login() {
            try {
                // For testing, use a test email
                const email = prompt('Enter your email:');
                const password = prompt('Enter your password:');
                
                if (email && password) {
                    await auth.signInWithEmailAndPassword(email, password);
                }
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('result').innerHTML = `<p style="color: red;">Login error: ${error.message}</p>`;
            }
        }

        async function createStripeAccount() {
            try {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<p>Creating Stripe Connect account...</p>';

                const createStripeConnectAccount = functions.httpsCallable('createStripeConnectAccount');
                const result = await createStripeConnectAccount({ accountType: 'student' });

                console.log('Stripe account created:', result.data);
                resultDiv.innerHTML = `
                    <p style="color: green;">Success!</p>
                    <p>Account ID: ${result.data.accountId}</p>
                    <p>Onboarding URL: <a href="${result.data.onboardingUrl}" target="_blank">Complete Setup</a></p>
                `;
            } catch (error) {
                console.error('Error creating Stripe account:', error);
                document.getElementById('result').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
