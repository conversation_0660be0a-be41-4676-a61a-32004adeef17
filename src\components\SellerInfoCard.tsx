import React from 'react';
import { Star, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

interface SellerInfoCardProps {
  seller: {
    id: string;
    name: string;
    avatar: string;
    rating: number;
    reviewCount: number;
    verified: boolean;
    university: string;
    totalSales: number;
    responseRate: number;
    responseTime: string;
  };
  className?: string;
}

const SellerInfoCard: React.FC<SellerInfoCardProps> = ({ seller, className = "" }) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 ${className}`}>
      <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Seller Information</h3>
      
      <Link
        to={`/user/${seller.id}`}
        className="block hover:bg-gray-50 dark:hover:bg-gray-700 rounded-xl p-4 transition-colors group"
      >
        <div className="flex items-center space-x-4 mb-4">
          <img
            src={seller.avatar}
            alt={seller.name}
            className="w-16 h-16 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600 group-hover:ring-primary-300 dark:group-hover:ring-primary-500 transition-all"
          />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                {seller.name}
              </h4>
              {seller.verified && (
                <div className="bg-primary-500 text-white p-1 rounded-full">
                  <Shield className="w-3 h-3" />
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <div className="flex items-center">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="ml-1 text-sm text-gray-600 dark:text-gray-400">
                  {seller.rating} ({seller.reviewCount} reviews)
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{seller.university}</p>
          </div>
        </div>
        
        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 text-center text-sm border-t border-gray-200 dark:border-gray-700 pt-4">
          <div>
            <p className="font-semibold text-gray-900 dark:text-white">{seller.totalSales}</p>
            <p className="text-gray-500 dark:text-gray-400">Sales</p>
          </div>
          <div>
            <p className="font-semibold text-gray-900 dark:text-white">{seller.responseRate}%</p>
            <p className="text-gray-500 dark:text-gray-400">Response Rate</p>
          </div>
        </div>
        
        <div className="mt-3 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Typically responds in {seller.responseTime}
          </p>
          <p className="text-xs text-primary-600 dark:text-primary-400 font-medium mt-1 group-hover:underline">
            View full profile →
          </p>
        </div>
      </Link>
    </div>
  );
};

export default SellerInfoCard;