{"timestamp": "2025-07-02T16:43:21.802Z", "overallScore": 77, "passed": 23, "failed": 3, "warnings": 4, "categories": {"firebase-rules": {"tests": [{"test": "No unsafe global read/write rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:21.807Z"}, {"test": "Authentication checks implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:21.807Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:21.807Z"}, {"test": "User ownership validation present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:21.807Z"}, {"test": "Input validation in security rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:21.807Z"}], "score": 100}, "secrets": {"tests": [{"test": "No hardcoded secrets in source code", "status": "FAIL", "severity": "Critical", "details": null, "timestamp": "2025-07-02T16:43:21.858Z"}, {"test": "All required environment variables present", "status": "FAIL", "severity": "Critical", "details": "Missing: VITE_FIREBASE_API_KEY, VITE_FIREBASE_AUTH_DOMAIN, VITE_FIREBASE_PROJECT_ID, VITE_FIREBASE_STORAGE_BUCKET, VITE_FIREBASE_MESSAGING_SENDER_ID, VITE_FIREBASE_APP_ID, VITE_STRIPE_PUBLISHABLE_KEY", "timestamp": "2025-07-02T16:43:21.858Z"}, {"test": "No .env files committed to repository", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:21.859Z"}], "score": 33}, "dependencies": {"tests": [{"test": "Dependency audit failed: Command failed: npm audit --json", "status": "FAIL", "severity": "Critical", "details": null, "timestamp": "2025-07-02T16:43:27.247Z"}, {"test": "Some dependencies may be outdated", "status": "WARN", "severity": "Low", "details": null, "timestamp": "2025-07-02T16:43:36.166Z"}], "score": 0}, "client-security": {"tests": [{"test": "Security utilities implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.168Z"}, {"test": "Input sanitization implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.168Z"}, {"test": "HTTPS enforcement implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.168Z"}, {"test": "Client-side rate limiting implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.168Z"}, {"test": "Security initialization in main app", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.169Z"}], "score": 100}, "network-security": {"tests": [{"test": "Security headers configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-02T16:43:36.169Z"}, {"test": "HTTPS configuration present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.169Z"}, {"test": "Firebase hosting security headers configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.170Z"}, {"test": "HSTS header configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-02T16:43:36.170Z"}], "score": 50}, "data-privacy": {"tests": [{"test": "Privacy policy page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.170Z"}, {"test": "Terms and conditions page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.171Z"}, {"test": "Consent management implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.191Z"}, {"test": "Data deletion policies in Firebase rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.192Z"}], "score": 100}, "csp": {"tests": [{"test": "CSP manager implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.193Z"}, {"test": "Nonce generation for inline scripts", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.193Z"}, {"test": "CSP meta tag in HTML", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-02T16:43:36.193Z"}], "score": 67}, "authentication": {"tests": [{"test": "Authentication context implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.193Z"}, {"test": "Protected route component implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.194Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.194Z"}, {"test": "Firebase Auth domain configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-02T16:43:36.194Z"}], "score": 100}}}