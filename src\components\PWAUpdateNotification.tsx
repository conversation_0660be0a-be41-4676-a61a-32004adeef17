import { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';

export default function PWAUpdateNotification() {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      // Check if there's a waiting service worker when the component mounts
      navigator.serviceWorker.getRegistration().then((reg) => {
        if (reg?.waiting) {
          setRegistration(reg);
          setShowUpdatePrompt(true);
        }
      });

      // Listen for new service worker updates
      const handleUpdate = (reg: ServiceWorkerRegistration) => {
        if (reg.waiting) {
          setRegistration(reg);
          setShowUpdatePrompt(true);
        }
      };

      // Listen for the controllerchange event
      const handleControllerChange = () => {
        window.location.reload();
      };

      navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);

      // Set up an interval to check for updates
      const interval = setInterval(() => {
        navigator.serviceWorker.getRegistration().then((reg) => {
          if (reg) {
            reg.update().then(() => {
              if (reg.waiting) {
                handleUpdate(reg);
              }
            });
          }
        });
      }, 60 * 60 * 1000); // Check every hour

      return () => {
        clearInterval(interval);
        navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
      };
    }
  }, []);

  const handleUpdate = () => {
    if (registration?.waiting) {
      // Send a message to the waiting service worker to skip waiting
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
    setShowUpdatePrompt(false);
  };

  if (!showUpdatePrompt) return null;

  return (
    <div className="fixed bottom-24 left-0 right-0 z-50 p-4 pointer-events-none">
      <div className="mx-auto max-w-md bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-4 border border-gray-200 pointer-events-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-[#f9a826]">
              <RefreshCw size={20} />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Update available</h3>
              <div className="mt-1 text-xs text-gray-600">
                <p>A new version of Hive Campus is available.</p>
              </div>
            </div>
          </div>
          <button
            onClick={handleUpdate}
            className="ml-4 px-3 py-1.5 bg-[#f9a826] text-white text-xs font-medium rounded-lg hover:bg-[#e89417] transition-colors"
          >
            Update now
          </button>
        </div>
      </div>
    </div>
  );
}