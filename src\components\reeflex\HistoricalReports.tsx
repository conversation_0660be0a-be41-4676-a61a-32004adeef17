import React, { useState, useEffect } from 'react';
import { collection, query, orderBy, limit, getDocs, Timestamp } from 'firebase/firestore';
import { firestore } from '../../firebase/config';
import { Calendar, ChevronDown, ChevronUp, Download, ExternalLink, Loader } from 'lucide-react';

// Types
interface ReeFlexReport {
  id: string;
  timestamp: Timestamp;
  aiSummary: string;
  report: {
    timeRange: {
      start: string;
      end: string;
    };
    summary: {
      totalEvents: number;
      totalErrors: number;
      totalFeedback: number;
      totalPageViews: number;
      totalInteractions: number;
    };
  };
}

interface HistoricalReportsProps {
  limit?: number;
  className?: string;
}

const HistoricalReports: React.FC<HistoricalReportsProps> = ({ limit: reportLimit = 7, className = '' }) => {
  const [reports, setReports] = useState<ReeFlexReport[]>([]);
  const [expandedReportId, setExpandedReportId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReports = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const reportsQuery = query(
          collection(firestore, 'reeflex_reports'),
          orderBy('timestamp', 'desc'),
          limit(reportLimit)
        );
        
        const reportsSnapshot = await getDocs(reportsQuery);
        
        if (reportsSnapshot.empty) {
          setReports([]);
          setIsLoading(false);
          return;
        }
        
        const fetchedReports = reportsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as ReeFlexReport[];
        
        setReports(fetchedReports);
        
        // Expand the most recent report by default
        if (fetchedReports.length > 0) {
          setExpandedReportId(fetchedReports[0].id);
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching historical reports:', err);
        setError('Failed to load historical reports. Please try again later.');
        setIsLoading(false);
      }
    };
    
    fetchReports();
  }, [reportLimit]);
  
  const toggleReportExpansion = (reportId: string) => {
    setExpandedReportId(expandedReportId === reportId ? null : reportId);
  };
  
  const formatTimestamp = (timestamp: any) => {
    try {
      let date: Date;

      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp.seconds && typeof timestamp.seconds === 'number') {
        date = new Date(timestamp.seconds * 1000);
      } else if (timestamp._seconds && typeof timestamp._seconds === 'number') {
        date = new Date(timestamp._seconds * 1000);
      } else {
        date = new Date(timestamp);
      }

      if (isNaN(date.getTime())) {
        return 'Unknown time';
      }

      return date.toLocaleString();
    } catch (error) {
      console.warn('Error formatting timestamp:', error);
      return 'Unknown time';
    }
  };
  
  if (isLoading) {
    return (
      <div className={`flex justify-center items-center p-8 ${className}`}>
        <Loader className="h-8 w-8 text-primary-500 animate-spin" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading historical reports...</span>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 ${className}`}>
        <p className="text-red-700 dark:text-red-400">{error}</p>
      </div>
    );
  }
  
  if (reports.length === 0) {
    return (
      <div className={`bg-gray-100 dark:bg-gray-800 rounded-lg p-6 text-center ${className}`}>
        <p className="text-gray-600 dark:text-gray-400">No historical reports available yet. Reports are generated daily.</p>
      </div>
    );
  }
  
  return (
    <div className={`space-y-4 ${className}`}>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Historical Reports</h2>
      
      {reports.map(report => (
        <div 
          key={report.id}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
        >
          <div 
            className="p-4 bg-gray-50 dark:bg-gray-750 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center cursor-pointer"
            onClick={() => toggleReportExpansion(report.id)}
          >
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                Report for {new Date(report.report.timeRange.start).toLocaleDateString()} - {new Date(report.report.timeRange.end).toLocaleDateString()}
              </h3>
              <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                <Calendar className="h-3 w-3 mr-1" />
                <span>Generated: {formatTimestamp(report.timestamp)}</span>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex space-x-2 mr-4">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                  {report.report.summary.totalPageViews} views
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                  {report.report.summary.totalErrors} errors
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                  {report.report.summary.totalFeedback} feedback
                </span>
              </div>
              
              {expandedReportId === report.id ? (
                <ChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              )}
            </div>
          </div>
          
          {expandedReportId === report.id && (
            <div className="p-6">
              <div className="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-6 border border-gray-100 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">AI Summary</h4>
                <div className="prose dark:prose-invert max-w-none">
                  {report.aiSummary.split('\n').map((line, i) => (
                    <React.Fragment key={i}>
                      {line}
                      <br />
                    </React.Fragment>
                  ))}
                </div>
              </div>
              
              <div className="mt-4 flex justify-end space-x-2">
                <a 
                  href={`https://console.firebase.google.com/project/hive-campus/firestore/data/~2Freeflex_reports/${report.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View in Firebase
                </a>
                
                <button
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Report
                </button>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default HistoricalReports;