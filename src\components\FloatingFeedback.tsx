import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { MessageSquare, X, ThumbsUp, ThumbsDown, Send } from 'lucide-react';
import { submitReeFlexFeedback } from '../utils/reeflex';
import { Sentry } from '../utils/sentry';

interface FloatingFeedbackProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

/**
 * FloatingFeedback - A floating button that expands to a feedback form
 * Allows users to quickly submit structured or freeform feedback
 */
const FloatingFeedback: React.FC<FloatingFeedbackProps> = ({ 
  position = 'bottom-right' 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<'positive' | 'negative' | null>(null);
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  const location = useLocation();
  
  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };
  
  // Feedback categories
  const feedbackCategories = {
    positive: ['Helpful Feature', 'Good Experience', 'Easy to Use', 'Fast Performance', 'Other'],
    negative: ['Confusing UI', 'Bug/Error', 'Slow Performance', 'Missing Feature', 'Other']
  };
  
  const toggleFeedback = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Reset state when opening
      setFeedbackType(null);
      setMessage('');
      setIsSubmitted(false);
      setSelectedCategory(null);
    }
  };
  
  const handleFeedbackTypeSelect = (type: 'positive' | 'negative') => {
    setFeedbackType(type);
    setSelectedCategory(null);
  };
  
  const handleSubmit = async () => {
    if (!message && !selectedCategory) return;
    
    setIsSubmitting(true);
    
    try {
      await submitReeFlexFeedback({
        feedbackType: feedbackType || 'neutral',
        category: selectedCategory || 'General',
        message: message,
        route: location.pathname,
        sentryEventId: Sentry.lastEventId()
      });
      
      setIsSubmitted(true);
      
      // Auto-close after 3 seconds
      setTimeout(() => {
        setIsOpen(false);
      }, 3000);
    } catch (error) {
      console.error('Error submitting feedback:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className={`fixed z-50 ${positionClasses[position]}`}>
      {/* Feedback Button */}
      <button
        onClick={toggleFeedback}
        className={`rounded-full p-3 shadow-lg transition-all duration-300 ${
          isOpen ? 'bg-gray-700 rotate-45' : 'bg-primary-600 hover:bg-primary-700'
        }`}
        aria-label={isOpen ? 'Close feedback' : 'Give feedback'}
      >
        {isOpen ? (
          <X className="h-6 w-6 text-white" />
        ) : (
          <MessageSquare className="h-6 w-6 text-white" />
        )}
      </button>
      
      {/* Feedback Panel */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {isSubmitted ? 'Thank You!' : 'How is your experience?'}
            </h3>
            
            {isSubmitted ? (
              <p className="text-gray-600 dark:text-gray-300">
                Your feedback helps us improve Hive Campus.
              </p>
            ) : (
              <>
                {!feedbackType ? (
                  <div className="flex space-x-2 mb-4">
                    <button
                      onClick={() => handleFeedbackTypeSelect('positive')}
                      className="flex-1 flex items-center justify-center space-x-2 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
                    >
                      <ThumbsUp className="h-5 w-5" />
                      <span>Positive</span>
                    </button>
                    <button
                      onClick={() => handleFeedbackTypeSelect('negative')}
                      className="flex-1 flex items-center justify-center space-x-2 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
                    >
                      <ThumbsDown className="h-5 w-5" />
                      <span>Negative</span>
                    </button>
                  </div>
                ) : (
                  <>
                    {/* Category Selection */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        What's it about?
                      </label>
                      <div className="flex flex-wrap gap-2">
                        {feedbackCategories[feedbackType].map((category) => (
                          <button
                            key={category}
                            onClick={() => setSelectedCategory(category)}
                            className={`text-xs px-2 py-1 rounded-full ${
                              selectedCategory === category
                                ? feedbackType === 'positive'
                                  ? 'bg-green-600 text-white'
                                  : 'bg-red-600 text-white'
                                : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                            }`}
                          >
                            {category}
                          </button>
                        ))}
                      </div>
                    </div>
                    
                    {/* Feedback Message */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Tell us more (optional)
                      </label>
                      <textarea
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder="Your feedback helps us improve..."
                        className="w-full px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:text-white"
                        rows={3}
                      />
                    </div>
                    
                    {/* Submit Button */}
                    <button
                      onClick={handleSubmit}
                      disabled={isSubmitting || (!message && !selectedCategory)}
                      className={`w-full flex items-center justify-center space-x-2 py-2 rounded-lg transition-colors ${
                        isSubmitting || (!message && !selectedCategory)
                          ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                          : feedbackType === 'positive'
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-red-600 hover:bg-red-700 text-white'
                      }`}
                    >
                      <Send className="h-4 w-4" />
                      <span>{isSubmitting ? 'Sending...' : 'Send Feedback'}</span>
                    </button>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FloatingFeedback;