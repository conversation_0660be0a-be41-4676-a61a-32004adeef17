import axios from 'axios';
import * as functions from 'firebase-functions';

// Define interfaces for Slack message structure
interface SlackAttachment {
  color?: string;
  text?: string;
  title?: string;
  title_link?: string;
  fields?: Array<{
    title: string;
    value: string;
    short?: boolean;
  }>;
}

interface SlackBlock {
  type: string;
  text?: {
    type: string;
    text: string;
  };
  elements?: Array<{
    type: string;
    text?: string;
  }>;
  [key: string]: unknown; // Allow additional properties
}


interface SlackMessageOptions {
  channel?: string;
  username?: string;
  icon_emoji?: string;
  attachments?: SlackAttachment[];
  blocks?: SlackBlock[];
}

// Slack webhook URL from environment variables
const SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;

/**
 * Send a message to <PERSON>lack using the configured webhook
 * 
 * @param message The message text to send
 * @param options Additional options for the Slack message
 * @returns A promise that resolves when the message is sent
 */
export async function sendSlackMessage(
  message: string,
  options: SlackMessageOptions = {}
): Promise<boolean> {
  // Check if Slack webhook is configured
  if (!SLACK_WEBHOOK_URL) {
    functions.logger.warn('Slack webhook URL is not configured. Set the SLACK_WEBHOOK_URL environment variable.');
    return false;
  }

  try {
    // Prepare the payload
    const payload = {
      text: message,
      ...options
    };

    // Send the message to Slack
    await axios.post(SLACK_WEBHOOK_URL, payload, {
      headers: { 'Content-Type': 'application/json' }
    });

    return true;
  } catch (error) {
    functions.logger.error('Error sending Slack message:', error);
    return false;
  }
}

/**
 * Send a critical error alert to Slack
 * 
 * @param errorType The type of error
 * @param count The number of occurrences
 * @param route The route where the error occurred
 * @param sentryId Optional Sentry event ID
 * @param details Additional details about the error
 */
export async function sendCriticalErrorAlert(
  errorType: string,
  count: number,
  route: string,
  sentryId?: string,
  details?: string
): Promise<boolean> {
  const message = `🔥 *ALERT:* ${count}+ ${errorType} on \`${route}\` in the last hour${sentryId ? ` (Sentry ID: ${sentryId})` : ''}`;
  
  const blocks = [
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: message
      }
    }
  ];
  
  if (details) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Details:* ${details}`
      }
    });
  }
  
  blocks.push({
    type: 'context',
    elements: [
      {
        type: 'mrkdwn',
        text: `🕒 ${new Date().toLocaleString()} | <https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_activity|View in Firebase>`
      }
    ]
  } as any);
  
  return sendSlackMessage(message, {
    username: 'ReeFlex Alert',
    icon_emoji: ':warning:',
    blocks
  });
}

/**
 * Send a performance alert to Slack
 * 
 * @param route The route with performance issues
 * @param duration The duration in milliseconds
 * @param count The number of occurrences
 */
export async function sendPerformanceAlert(
  route: string,
  duration: number,
  count: number
): Promise<boolean> {
  const message = `⏱ *ALERT:* ${count}+ slow page loads on \`${route}\` (${duration}ms) in the last hour`;
  
  return sendSlackMessage(message, {
    username: 'ReeFlex Alert',
    icon_emoji: ':hourglass:',
    blocks: [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: message
        }
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `🕒 ${new Date().toLocaleString()} | <https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_activity|View in Firebase>`
          }
        ]
      }
    ]
  });
}

/**
 * Send a payment failure alert to Slack
 * 
 * @param count The number of payment failures
 * @param details Additional details about the failures
 */
export async function sendPaymentFailureAlert(
  count: number,
  details?: string
): Promise<boolean> {
  const message = `💰 *ALERT:* ${count}+ payment failures in the last hour`;
  
  const blocks = [
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: message
      }
    }
  ];
  
  if (details) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Details:* ${details}`
      }
    });
  }
  
  blocks.push({
    type: 'context',
    elements: [
      {
        type: 'mrkdwn',
        text: `🕒 ${new Date().toLocaleString()} | <https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_activity|View in Firebase>`
      }
    ]
  } as any);
  
  return sendSlackMessage(message, {
    username: 'ReeFlex Alert',
    icon_emoji: ':money_with_wings:',
    blocks
  });
}

/**
 * Send a feedback alert to Slack when multiple users report the same issue
 * 
 * @param route The route with multiple feedback reports
 * @param category The feedback category
 * @param count The number of feedback reports
 * @param quotes Sample user quotes
 */
export async function sendFeedbackAlert(
  route: string,
  category: string,
  count: number,
  quotes: string[] = []
): Promise<boolean> {
  const message = `🧩 *ALERT:* ${count}+ users reported issues with \`${category}\` on \`${route}\``;
  
  const blocks: SlackBlock[] = [
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: message
      }
    }
  ];
  
  if (quotes.length > 0) {
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Sample feedback:*\n${quotes.map(q => `> ${q}`).join('\n')}`
      }
    });
  }
  
  blocks.push({
    type: 'context',
    elements: [
      {
        type: 'mrkdwn',
        text: `🕒 ${new Date().toLocaleString()} | <https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_feedback|View in Firebase>`
      }
    ]
  });
  
  return sendSlackMessage(message, {
    username: 'ReeFlex Alert',
    icon_emoji: ':speech_balloon:',
    blocks
  });
}
