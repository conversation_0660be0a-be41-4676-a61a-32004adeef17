import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should navigate to login page from landing page', async ({ page }) => {
    // Wait for the page to load
    await page.waitForLoadState('networkidle')
    
    // Check if we're on the landing page and click sign in
    if (await page.locator('text=Sign In').isVisible()) {
      await page.click('text=Sign In')
    }
    
    // Should navigate to login type selection or login page
    await expect(page).toHaveURL(/\/(login|login-type-selection)/)
  })

  test('should show validation errors for empty login form', async ({ page }) => {
    await page.goto('/login')
    
    // Try to submit empty form
    const submitButton = page.locator('button[type="submit"]').first()
    await submitButton.click()
    
    // Should show validation errors
    await expect(page.locator('text=Email is required').or(page.locator('text=Please enter your email'))).toBeVisible()
  })

  test('should navigate to signup page from login page', async ({ page }) => {
    await page.goto('/login')
    
    // Click signup link
    await page.click('text=Sign up')
    
    // Should navigate to signup page
    await expect(page).toHaveURL(/\/signup/)
  })

  test('should show validation errors for invalid email in signup', async ({ page }) => {
    await page.goto('/signup')
    
    // Fill form with invalid email
    await page.fill('input[type="email"]', 'invalid-email')
    await page.fill('input[type="password"]', 'password123')
    
    // Try to submit
    const submitButton = page.locator('button[type="submit"]').first()
    await submitButton.click()
    
    // Should show validation error
    await expect(page.locator('text=Please enter a valid email').or(page.locator('text=Invalid email'))).toBeVisible()
  })

  test('should require .edu email for signup', async ({ page }) => {
    await page.goto('/signup')
    
    // Fill form with non-.edu email
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    
    // Try to submit
    const submitButton = page.locator('button[type="submit"]').first()
    await submitButton.click()
    
    // Should show .edu email requirement
    await expect(page.locator('text=Please use a .edu email').or(page.locator('text=university email'))).toBeVisible()
  })

  test('should handle password strength validation', async ({ page }) => {
    await page.goto('/signup')
    
    // Fill form with weak password
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123')
    
    // Should show password strength indicator or validation
    await expect(page.locator('text=Password must be').or(page.locator('[data-testid="password-strength"]'))).toBeVisible()
  })
})

test.describe('Navigation and Layout', () => {
  test('should show different navigation based on user role', async ({ page }) => {
    // Test unauthenticated navigation
    await page.goto('/')
    
    // Should show sign in/up options
    await expect(page.locator('text=Sign In').or(page.locator('text=Login'))).toBeVisible()
  })

  test('should display proper branding and logo', async ({ page }) => {
    await page.goto('/')
    
    // Should show Hive Campus branding
    await expect(page.locator('text=Hive Campus')).toBeVisible()
    
    // Should have proper meta tags for PWA
    await expect(page.locator('meta[name="theme-color"]')).toHaveAttribute('content')
  })

  test('should be responsive on mobile viewports', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // Should adapt to mobile layout
    await expect(page.locator('nav').last()).toBeVisible() // Bottom navigation
  })
})

test.describe('PWA Features', () => {
  test('should have service worker registered', async ({ page }) => {
    await page.goto('/')
    
    // Check if service worker is registered
    const swRegistered = await page.evaluate(() => {
      return 'serviceWorker' in navigator
    })
    
    expect(swRegistered).toBe(true)
  })

  test('should have manifest.json accessible', async ({ page }) => {
    const response = await page.request.get('/manifest.json')
    expect(response.status()).toBe(200)
    
    const manifest = await response.json()
    expect(manifest.name).toBeTruthy()
    expect(manifest.short_name).toBeTruthy()
  })

  test('should show offline page when network is unavailable', async ({ page, context }) => {
    await page.goto('/')
    
    // Simulate offline
    await context.setOffline(true)
    
    // Try to navigate to a new page
    await page.goto('/profile', { waitUntil: 'networkidle' })
    
    // Should show offline indication or cached content
    // Note: This test might need adjustment based on your offline strategy
  })
})

test.describe('Error Handling', () => {
  test('should handle 404 pages gracefully', async ({ page }) => {
    const response = await page.goto('/non-existent-page')
    
    // Should either redirect or show 404 page
    expect(response?.status()).toBeLessThan(500)
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Test error boundary behavior
    await page.goto('/')
    
    // The page should not crash on network errors
    await expect(page.locator('body')).toBeVisible()
  })
})