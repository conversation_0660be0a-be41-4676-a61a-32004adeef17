import React, { useState, useEffect } from 'react';
import { usePopupNotifications } from '../hooks/usePopupNotifications';
import DeliveryConfirmationPopup from './DeliveryConfirmationPopup';

interface PopupNotification {
  id: string;
  userId: string;
  type: 'delivery_confirmation' | 'payment_reminder' | 'order_update';
  orderId: string;
  title: string;
  message: string;
  actionRequired: boolean;
  expiresAt: { toDate: () => Date };
  createdAt: { toDate: () => Date };
  dismissed?: boolean;
}

/**
 * PopupNotificationManager - Manages and displays popup notifications
 * 
 * This component automatically shows important notifications as popups:
 * - Delivery confirmations (highest priority)
 * - Payment reminders
 * - Order updates requiring action
 */
const PopupNotificationManager: React.FC = () => {
  const {
    notifications,
    isLoading,
    dismissNotification,
    getUrgentNotification,
    hasDeliveryConfirmations: _hasDeliveryConfirmations
  } = usePopupNotifications();
  
  const [currentNotification, setCurrentNotification] = useState<PopupNotification | null>(null);
  const [showPopup, setShowPopup] = useState(false);

  // Check for urgent notifications
  useEffect(() => {
    if (isLoading) return;

    const urgentNotification = getUrgentNotification();
    
    if (urgentNotification && !currentNotification) {
      setCurrentNotification(urgentNotification);
      setShowPopup(true);
    }
  }, [notifications, isLoading, currentNotification, getUrgentNotification]);

  // Handle closing popup
  const handleClosePopup = () => {
    setShowPopup(false);
    
    // Dismiss the notification after a short delay
    setTimeout(() => {
      if (currentNotification) {
        dismissNotification(currentNotification.id);
        setCurrentNotification(null);
      }
    }, 300);
  };

  // Handle when code is successfully submitted
  const handleCodeSubmitted = () => {
    // Keep popup open briefly to show success message
    // The DeliveryConfirmationPopup will handle auto-closing
  };

  // Don't render anything if no notifications or loading
  if (isLoading || !showPopup || !currentNotification) {
    return null;
  }

  // Render appropriate popup based on notification type
  switch (currentNotification.type) {
    case 'delivery_confirmation':
      return (
        <DeliveryConfirmationPopup
          notification={{
            id: currentNotification.id,
            orderId: currentNotification.orderId || '',
            title: currentNotification.title,
            message: currentNotification.message,
            actionRequired: currentNotification.actionRequired,
            expiresAt: currentNotification.expiresAt.toDate(),
            createdAt: currentNotification.createdAt.toDate()
          }}
          onClose={handleClosePopup}
          onCodeSubmitted={handleCodeSubmitted}
        />
      );

    case 'new_message':
    case 'admin_warning':
    case 'admin_broadcast':
      // For now, these will be handled by the notification system
      // Could add specific popups for these types later
      return null;

    case 'payment_reminder':
    case 'order_update':
      // Could add other popup types here
      return null;

    default:
      return null;
  }
};

export default PopupNotificationManager;
