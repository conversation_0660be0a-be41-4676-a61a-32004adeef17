import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { verifyAuth, handleError } from "../utils/helpers";
import { Listing, ListingCondition, ListingType, ListingStatus } from "../utils/types";

// Create a new listing
export const createListing = functions.https.onCall(async (data, context) => {
  try {
    console.log('Creating listing with data:', JSON.stringify(data, null, 2));
    const auth = await verifyAuth(context);

    const {
      title,
      description,
      price,
      category,
      condition,
      type,
      imageURLs
    } = data;
    
    // Validate required fields
    if (!title || !description || price === undefined || !category || !condition || !type) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields'
      );
    }
    
    // Validate listing type
    if (!['sell', 'rent', 'auction'].includes(type)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid listing type. Must be one of: sell, rent, auction'
      );
    }
    
    // Validate condition
    if (!['new', 'like_new', 'very_good', 'good', 'fair', 'poor'].includes(condition)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid condition. Must be one of: new, like_new, very_good, good, fair, poor'
      );
    }
    
    // Get user data to include university
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'User not found'
      );
    }

    const userData = userDoc.data();
    console.log('User data:', JSON.stringify(userData, null, 2));

    // Get university from user data or extract from email
    let university = userData?.university;
    console.log('Initial university:', university);

    if (!university && userData?.email) {
      console.log('Extracting university from email:', userData.email);
      // Extract university from email domain as fallback
      const emailParts = userData.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university:', university);

      // Update user profile with university
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        updatedAt: admin.firestore.Timestamp.now()
      });
    } else if (!university && auth.token?.email) {
      console.log('Extracting university from auth token email:', auth.token.email);
      // Try to get email from auth token as fallback
      const emailParts = auth.token.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university from token:', university);

      // Update user profile with university and email
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        email: auth.token.email,
        updatedAt: admin.firestore.Timestamp.now()
      });
    }

    if (!university) {
      console.error('Unable to determine university. User data:', userData, 'Auth token:', auth.token);
      throw new functions.https.HttpsError(
        'failed-precondition',
        'Unable to determine university from user profile or email. Please update your profile.'
      );
    }

    console.log('Final university:', university);

    // Create the listing object
    const listing: any = {
      title,
      description,
      price: Number(price),
      category,
      condition: condition as ListingCondition,
      type: type as ListingType,
      ownerId: auth.uid,
      ownerName: userData?.name || 'Anonymous',
      university: university,
      imageURLs: imageURLs || [],
      status: 'active' as ListingStatus,
      visibility: data.visibility || 'university',
      createdAt: admin.firestore.Timestamp.now(),

      // Add delivery method fields
      deliveryMethod: data.deliveryMethod || 'in_person'
    };

    // Only add shippingOptions if it exists and has valid data
    if (data.shippingOptions && Object.keys(data.shippingOptions).length > 0) {
      listing.shippingOptions = data.shippingOptions;
    }

    // Add type-specific fields
    if (type === 'rent') {
      if (data.rentalPeriod) listing.rentalPeriod = data.rentalPeriod;
      if (data.weeklyPrice) listing.weeklyPrice = Number(data.weeklyPrice);
      if (data.monthlyPrice) listing.monthlyPrice = Number(data.monthlyPrice);
      if (data.startDate) listing.startDate = data.startDate;
      if (data.endDate) listing.endDate = data.endDate;
    }

    if (type === 'auction') {
      if (data.startingBid) listing.startingBid = Number(data.startingBid);
      if (data.auctionStartDate) listing.auctionStartDate = data.auctionStartDate;
      if (data.auctionStartTime) listing.auctionStartTime = data.auctionStartTime;
      if (data.auctionEndDate) listing.auctionEndDate = data.auctionEndDate;
      if (data.auctionEndTime) listing.auctionEndTime = data.auctionEndTime;
      if (data.auctionDuration) listing.auctionDuration = data.auctionDuration;
    }
    
    // Add to Firestore
    console.log('Adding listing to Firestore:', JSON.stringify(listing, null, 2));
    const docRef = await admin.firestore().collection('listings').add(listing);
    console.log('Listing created successfully with ID:', docRef.id);

    return {
      success: true,
      data: {
        id: docRef.id,
        ...listing
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Edit an existing listing
export const editListing = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { 
      listingId,
      title, 
      description, 
      price, 
      category, 
      condition, 
      type, 
      imageURLs,
      status
    } = data;
    
    if (!listingId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Listing ID is required'
      );
    }
    
    // Get the listing
    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
    
    if (!listingDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }
    
    const listing = listingDoc.data() as Listing;
    
    // Check if the user is the owner
    if (listing.ownerId !== auth.uid) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only the owner can edit this listing'
      );
    }
    
    // Create update object
    const updateData: Partial<Listing> = {
      updatedAt: admin.firestore.Timestamp.now()
    };
    
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) updateData.price = Number(price);
    if (category !== undefined) updateData.category = category;
    
    if (condition !== undefined) {
      if (!['new', 'like_new', 'very_good', 'good', 'fair', 'poor'].includes(condition)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Invalid condition. Must be one of: new, like_new, very_good, good, fair, poor'
        );
      }
      updateData.condition = condition as ListingCondition;
    }
    
    if (type !== undefined) {
      if (!['sell', 'rent', 'auction'].includes(type)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Invalid listing type. Must be one of: sell, rent, auction'
        );
      }
      updateData.type = type as ListingType;
    }
    
    if (imageURLs !== undefined) updateData.imageURLs = imageURLs;
    
    if (status !== undefined) {
      if (!['active', 'sold', 'pending', 'deleted'].includes(status)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Invalid status. Must be one of: active, sold, pending, deleted'
        );
      }
      updateData.status = status as ListingStatus;
    }
    
    // Update the listing
    await admin.firestore().collection('listings').doc(listingId).update(updateData);
    
    return { 
      success: true,
      data: {
        id: listingId,
        ...listing,
        ...updateData
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Delete a listing
export const deleteListing = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { listingId } = data;
    
    if (!listingId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Listing ID is required'
      );
    }
    
    // Get the listing
    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
    
    if (!listingDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }
    
    const listing = listingDoc.data() as Listing;
    
    // Check if the user is the owner
    if (listing.ownerId !== auth.uid) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only the owner can delete this listing'
      );
    }
    
    // Option 1: Soft delete by updating status
    await admin.firestore().collection('listings').doc(listingId).update({
      status: 'deleted' as ListingStatus,
      updatedAt: admin.firestore.Timestamp.now()
    });
    
    // Option 2: Hard delete (uncomment if preferred)
    // await admin.firestore().collection('listings').doc(listingId).delete();
    
    return { success: true };
  } catch (error) {
    return handleError(error);
  }
});

// Get a single listing by ID
export const getListingById = functions.https.onCall(async (data, context) => {
  try {
    await verifyAuth(context);
    
    const { listingId } = data;
    
    if (!listingId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Listing ID is required'
      );
    }
    
    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
    
    if (!listingDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }
    
    const listing = listingDoc.data() as Listing;
    
    // Don't return deleted listings unless it's the owner
    if (listing.status === 'deleted' && listing.ownerId !== context.auth?.uid) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }
    
    return {
      success: true,
      data: {
        id: listingId,
        ...listing
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get listings with filtering
export const getListings = functions.https.onCall(async (data, context) => {
  try {
    await verifyAuth(context);

    const {
      university,
      category,
      type,
      condition,
      minPrice,
      maxPrice,
      ownerId,
      status = 'active',
      limit = 20,
      lastVisible
    } = data;

    // Get current user's university for visibility filtering
    const userDoc = await admin.firestore().collection('users').doc(context.auth!.uid).get();
    const currentUserUniversity = userDoc.data()?.university;

    let query = admin.firestore().collection('listings')
      .where('status', '==', status)
      .orderBy('createdAt', 'desc');

    // Apply filters if provided
    if (university) {
      query = query.where('university', '==', university);
    }

    if (category) {
      query = query.where('category', '==', category);
    }

    if (type) {
      query = query.where('type', '==', type);
    }

    if (condition) {
      query = query.where('condition', '==', condition);
    }

    if (ownerId) {
      query = query.where('ownerId', '==', ownerId);
    }
    
    // Handle pagination
    if (lastVisible) {
      const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
      if (lastDoc.exists) {
        query = query.startAfter(lastDoc);
      }
    }
    
    // Apply limit
    query = query.limit(limit);
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const listings: Listing[] = [];
    let lastVisibleId: string | null = null;
    
    snapshot.forEach(doc => {
      const listing = doc.data() as Listing;

      // Apply visibility filtering
      const isVisible = listing.visibility === 'public' ||
                       (listing.visibility === 'university' && listing.university === currentUserUniversity);

      if (!isVisible) {
        return; // Skip this listing
      }

      // Apply price filtering in memory (can't do range queries along with other filters in Firestore)
      const price = listing.price;
      if ((minPrice === undefined || price >= minPrice) &&
          (maxPrice === undefined || price <= maxPrice)) {
        listings.push({
          id: doc.id,
          ...listing
        });
      }

      // Set the last visible document ID for pagination
      lastVisibleId = doc.id;
    });
    
    return {
      success: true,
      data: {
        listings,
        lastVisible: lastVisibleId,
        total: listings.length
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Search listings by title or description
export const searchListings = functions.https.onCall(async (data, context) => {
  try {
    await verifyAuth(context);
    
    const { query, limit = 20 } = data;
    
    if (!query || typeof query !== 'string') {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Search query is required'
      );
    }
    
    // Normalize the query
    const searchTerms = query.toLowerCase().trim().split(/\s+/);
    
    // Get all active listings
    const snapshot = await admin.firestore()
      .collection('listings')
      .where('status', '==', 'active')
      .get();
    
    const results: Listing[] = [];
    
    // Perform client-side filtering (Firestore doesn't support full-text search)
    snapshot.forEach(doc => {
      const listing = doc.data() as Listing;
      const title = listing.title.toLowerCase();
      const description = listing.description.toLowerCase();
      
      // Check if all search terms are in the title or description
      const matches = searchTerms.every(term => 
        title.includes(term) || description.includes(term)
      );
      
      if (matches) {
        results.push({
          id: doc.id,
          ...listing
        });
      }
    });
    
    // Sort by relevance (simple implementation - could be improved)
    results.sort((a, b) => {
      const aTitle = a.title.toLowerCase();
      const bTitle = b.title.toLowerCase();
      
      // Prioritize title matches
      const aMatchesTitle = searchTerms.every(term => aTitle.includes(term));
      const bMatchesTitle = searchTerms.every(term => bTitle.includes(term));
      
      if (aMatchesTitle && !bMatchesTitle) return -1;
      if (!aMatchesTitle && bMatchesTitle) return 1;
      
      // Then sort by recency
      return b.createdAt.toMillis() - a.createdAt.toMillis();
    });
    
    return {
      success: true,
      data: {
        listings: results.slice(0, limit),
        total: results.length
      }
    };
  } catch (error) {
    return handleError(error);
  }
});