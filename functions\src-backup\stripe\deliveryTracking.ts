import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { Timestamp } from 'firebase-admin/firestore';

const db = admin.firestore();

/**
 * Enhanced delivery tracking with real-time status monitoring
 * Integrates with shipping carriers to detect delivery status
 */

// Simulated carrier tracking API (replace with real carrier APIs)
interface TrackingStatus {
  status: 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception';
  location?: string;
  timestamp: Date;
  description: string;
}

/**
 * Check delivery status for all shipped orders
 * Runs every 2 hours to check for deliveries
 */
export const checkDeliveryStatus = functions.pubsub
  .schedule('every 2 hours')
  .onRun(async () => {
    try {
      // Get all orders that are shipped but not yet delivered
      const ordersSnapshot = await db
        .collection('orders')
        .where('status', '==', 'shipped_pending_code')
        .get();

      const promises = ordersSnapshot.docs.map(async (doc) => {
        const order = doc.data();
        const orderId = doc.id;
        
        if (order.trackingInfo?.trackingNumber) {
          try {
            // Check delivery status with carrier
            const deliveryStatus = await checkCarrierDeliveryStatus(
              order.trackingInfo.trackingNumber,
              order.trackingInfo.carrier
            );
            
            if (deliveryStatus.status === 'delivered') {
              await handleOrderDelivered(orderId, deliveryStatus);
            }
          } catch (error) {
            console.error(`Error checking delivery for order ${orderId}:`, error);
          }
        }
      });

      await Promise.all(promises);
      return null;
    } catch (error) {
      console.error('Error in delivery status check:', error);
      return null;
    }
  });

/**
 * Handle when an order is marked as delivered
 */
async function handleOrderDelivered(orderId: string, deliveryStatus: TrackingStatus): Promise<void> {
  try {
    // Update order status to delivered
    await db.collection('orders').doc(orderId).update({
      status: 'delivered',
      deliveredAt: Timestamp.fromDate(deliveryStatus.timestamp),
      deliveryLocation: deliveryStatus.location,
      autoReleaseDate: Timestamp.fromDate(
        new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from delivery
      ),
      updatedAt: Timestamp.now()
    });

    // Get order details for notification
    const orderDoc = await db.collection('orders').doc(orderId).get();
    const order = orderDoc.data();

    if (!order) return;

    // Create delivery notification for buyer
    await db.collection('notifications').add({
      userId: order.buyerId,
      type: 'order_delivered',
      title: '📦 Order Delivered!',
      message: `Your order "${order.title}" has been delivered. Please enter your secret code to release payment to the seller.`,
      data: {
        orderId,
        secretCode: order.secretCode,
        autoReleaseDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        showCodeEntry: true
      },
      priority: 'high',
      read: false,
      createdAt: Timestamp.now()
    });

    // Create popup notification trigger
    await db.collection('popup_notifications').add({
      userId: order.buyerId,
      type: 'delivery_confirmation',
      orderId,
      title: '🎉 Your order has been delivered!',
      message: `Please confirm receipt of "${order.title}" by entering your 6-digit code.`,
      actionRequired: true,
      expiresAt: Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // 24 hours
      createdAt: Timestamp.now()
    });

    console.log(`Order ${orderId} marked as delivered, notifications sent`);
  } catch (error) {
    console.error(`Error handling delivered order ${orderId}:`, error);
    throw error;
  }
}

/**
 * Check delivery status with carrier API
 * This is a mock implementation - replace with real carrier APIs
 */
async function checkCarrierDeliveryStatus(
  _trackingNumber: string,
  _carrier: string
): Promise<TrackingStatus> {
  // Mock implementation - replace with real carrier API calls
  // For USPS, FedEx, UPS, etc.
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Mock delivery detection (in production, this would call real APIs)
  const mockDelivered = Math.random() > 0.9; // 10% chance of being delivered
  
  if (mockDelivered) {
    return {
      status: 'delivered',
      location: 'Front door',
      timestamp: new Date(),
      description: 'Package delivered to recipient'
    };
  }
  
  return {
    status: 'in_transit',
    timestamp: new Date(),
    description: 'Package in transit'
  };
}

/**
 * Generate new secret code for buyer
 */
export const generateNewSecretCode = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { orderId } = data;
    if (!orderId) {
      throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
    }

    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found');
    }

    const order = orderDoc.data();
    
    // Verify user is the buyer
    if (order?.buyerId !== context.auth.uid) {
      throw new functions.https.HttpsError('permission-denied', 'Only the buyer can request a new code');
    }

    // Check if order is in correct status
    if (!['delivered', 'shipped_pending_code'].includes(order?.status)) {
      throw new functions.https.HttpsError('failed-precondition', 'Order must be delivered or shipped to generate new code');
    }

    // Generate new 6-digit code
    const newSecretCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Update the secret code
    await db.collection('codes').doc(orderId).update({
      code: newSecretCode,
      regeneratedAt: Timestamp.now(),
      regenerationCount: admin.firestore.FieldValue.increment(1)
    });

    // Update order with new code
    await db.collection('orders').doc(orderId).update({
      secretCode: newSecretCode,
      updatedAt: Timestamp.now()
    });

    // Send notification about new code
    await db.collection('notifications').add({
      userId: order.buyerId,
      type: 'new_secret_code',
      title: '🔑 New Secret Code Generated',
      message: `A new secret code has been generated for your order "${order.title}". Use this code to release payment.`,
      data: {
        orderId,
        newSecretCode
      },
      read: false,
      createdAt: Timestamp.now()
    });

    return {
      success: true,
      newSecretCode,
      message: 'New secret code generated successfully'
    };

  } catch (error) {
    console.error('Error generating new secret code:', error);
    throw error;
  }
});

/**
 * Manual delivery confirmation by buyer
 */
export const confirmDelivery = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { orderId } = data;
    if (!orderId) {
      throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
    }

    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found');
    }

    const order = orderDoc.data();
    
    // Verify user is the buyer
    if (order?.buyerId !== context.auth.uid) {
      throw new functions.https.HttpsError('permission-denied', 'Only the buyer can confirm delivery');
    }

    // Check if order is shipped
    if (order?.status !== 'shipped_pending_code') {
      throw new functions.https.HttpsError('failed-precondition', 'Order must be shipped to confirm delivery');
    }

    // Mark as delivered manually
    await handleOrderDelivered(orderId, {
      status: 'delivered',
      timestamp: new Date(),
      description: 'Delivery confirmed by buyer'
    });

    return {
      success: true,
      message: 'Delivery confirmed successfully'
    };

  } catch (error) {
    console.error('Error confirming delivery:', error);
    throw error;
  }
});
