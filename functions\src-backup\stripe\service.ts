import * as admin from 'firebase-admin';
import { stripe, stripeConfig } from './config';
import { 
  Order, 
  Wallet, 
  SecretCode, 
  ConnectAccount 
} from './types';
import { Timestamp, FieldValue } from 'firebase-admin/firestore';

const db = admin.firestore();

/**
 * Creates a Stripe Checkout Session for a listing
 */
export async function createCheckoutSession(
  listingId: string,
  buyerId: string,
  useWalletBalance: boolean = false
): Promise<{ sessionId: string; sessionUrl: string }> {
  try {
    console.log('Creating checkout session for:', { listingId, buyerId, useWalletBalance });

    // Validate inputs
    if (!listingId || listingId.trim() === '') {
      throw new Error('Invalid listing ID');
    }

    if (!buyerId || buyerId.trim() === '') {
      throw new Error('Invalid buyer ID');
    }

    // Get the listing data
    const listingDoc = await db.collection('listings').doc(listingId).get();
    if (!listingDoc.exists) {
      throw new Error('Listing not found');
    }

    const listing = listingDoc.data() as FirebaseFirestore.DocumentData;
    console.log('Listing data:', listing);
    
    // Validate listing has required fields
    if (!listing.title || !listing.price || !listing.ownerId) {
      throw new Error('Listing is missing required fields (title, price, or ownerId)');
    }

    // Verify seller exists
    const sellerDoc = await db.collection('users').doc(listing.ownerId).get();
    if (!sellerDoc.exists) {
      throw new Error('Seller not found');
    }

    const seller = sellerDoc.data();
    console.log('Seller data:', { sellerId: listing.ownerId, sellerName: seller?.name });
    
    // Verify buyer exists
    const buyerDoc = await db.collection('users').doc(buyerId).get();
    if (!buyerDoc.exists) {
      throw new Error('Buyer not found');
    }
    
    // Check if the seller has a Stripe Connect account
    const connectAccountQuery = await db.collection('connectAccounts')
      .where('userId', '==', listing.ownerId)
      .where('isOnboarded', '==', true)
      .limit(1)
      .get();
    
    let connectAccount: ConnectAccount | null = null;

    if (!connectAccountQuery.empty) {
      connectAccount = connectAccountQuery.docs[0].data() as ConnectAccount;
      console.log('Using Connect account for seller:', connectAccount.stripeAccountId);
    } else {
      console.log('No Connect account found - using direct payment (for testing)');
    }
    
    // Determine if this is a textbook or other category for commission calculation
    const isTextbook = listing.category.toLowerCase() === 'textbooks';
    const commissionRate = isTextbook 
      ? stripeConfig.commissionRates.textbooks 
      : stripeConfig.commissionRates.other;
    
    // Calculate the seller's amount after commission
    const sellerAmount = Math.round(listing.price * 100 * (1 - commissionRate));
    
    // Check if the buyer wants to use their wallet balance
    let walletAmountUsed = 0;
    let stripeAmount = Math.round(listing.price * 100); // Amount in cents
    
    if (useWalletBalance) {
      // Get the buyer's wallet
      const walletDoc = await db.collection('wallets').doc(buyerId).get();
      
      if (walletDoc.exists) {
        const wallet = walletDoc.data() as Wallet;
        
        if (wallet.balance > 0) {
          const walletBalanceInCents = Math.round(wallet.balance * 100);
          
          // If wallet balance covers the entire amount, we still need to charge a minimum amount via Stripe
          if (walletBalanceInCents >= stripeAmount) {
            walletAmountUsed = (stripeAmount - stripeConfig.minimumChargeAmount * 100) / 100;
            stripeAmount = stripeConfig.minimumChargeAmount * 100;
          } else {
            // Use the entire wallet balance
            walletAmountUsed = wallet.balance;
            stripeAmount -= walletBalanceInCents;
          }
        }
      }
    }
    
    // Calculate cashback amount (only on the Stripe portion)
    const cashbackAmount = (stripeAmount / 100) * stripeConfig.cashbackRate;
    
    // Create a Checkout Session
    console.log('Creating Stripe session with amount:', stripeAmount, 'cents');

    let session;
    try {
      session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: listing.title,
              description: listing.description.substring(0, 255), // Stripe limits description length
              metadata: {
                category: listing.category,
                condition: listing.condition
              }
            },
            unit_amount: stripeAmount,
            tax_behavior: 'exclusive', // Tax will be calculated separately
          },
          quantity: 1,
        },
      ],
      // Enable automatic tax calculation
      automatic_tax: {
        enabled: true,
      },
      // Collect shipping address for tax calculation
      shipping_address_collection: {
        allowed_countries: ['US'],
      },
      // Collect customer details for tax purposes
      customer_creation: 'always',
      // Collect phone number for tax compliance
      phone_number_collection: {
        enabled: true,
      },
      payment_intent_data: {
        ...(connectAccount && {
          application_fee_amount: Math.round(listing.price * 100 * commissionRate),
          transfer_data: {
            destination: connectAccount.stripeAccountId,
          },
        }),
        metadata: {
          listingId,
          buyerId,
          sellerId: listing.ownerId,
          category: listing.category,
          walletAmountUsed: walletAmountUsed.toString(),
          cashbackAmount: cashbackAmount.toString()
        },
        capture_method: 'manual', // We'll capture the payment after the buyer confirms receipt
      },
      mode: 'payment',
      success_url: `${process.env.APP_URL || 'https://hivecampus.app'}/order/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.APP_URL || 'https://hivecampus.app'}/listing/${listingId}`,
      metadata: {
        listingId,
        buyerId,
        sellerId: listing.ownerId,
        category: listing.category,
        walletAmountUsed: walletAmountUsed.toString(),
        cashbackAmount: cashbackAmount.toString()
      },
    });

    console.log('Stripe session created successfully:', session.id);

    } catch (stripeError) {
      console.error('Error creating Stripe session:', stripeError);
      throw new Error(`Failed to create Stripe session: ${stripeError instanceof Error ? stripeError.message : 'Unknown error'}`);
    }

    // Create a pending order in Firestore
    console.log('Creating order with data:', {
      buyerId,
      sellerId: listing.ownerId,
      listingId,
      amount: listing.price,
      commission: listing.price * commissionRate,
      stripeSessionId: session.id
    });

    const orderRef = db.collection('orders').doc();
    const order: Order = {
      id: orderRef.id,
      buyerId,
      sellerId: listing.ownerId,
      listingId,
      amount: listing.price,
      commission: listing.price * commissionRate,
      sellerAmount: sellerAmount / 100,
      status: 'pending_payment',
      stripeSessionId: session.id,
      category: listing.category,
      title: listing.title,
      walletAmountUsed: walletAmountUsed,
      cashbackAmount,
      createdAt: Timestamp.now(),
    };

    try {
      await orderRef.set(order);
      console.log('Order created successfully:', orderRef.id);
    } catch (orderError) {
      console.error('Error creating order:', orderError);
      throw new Error(`Failed to create order: ${orderError instanceof Error ? orderError.message : 'Unknown error'}`);
    }
    
    // NOTE: We don't deduct wallet balance here anymore
    // Wallet balance will be deducted in the webhook after successful payment
    // This prevents wallet balance loss if payment fails
    
    return {
      sessionId: session.id,
      sessionUrl: session.url || '',
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
}

/**
 * Handles the Stripe webhook event for successful payments
 */
export async function handlePaymentSucceeded(paymentIntent: Record<string, unknown>): Promise<void> {
  try {
    const metadata = paymentIntent.metadata as Record<string, string>;
    const { listingId, buyerId, cashbackAmount } = metadata;
    
    // Find the order by payment intent ID
    const ordersQuery = await db.collection('orders')
      .where('paymentIntentId', '==', paymentIntent.id as string)
      .limit(1)
      .get();
    
    if (ordersQuery.empty) {
      throw new Error('Order not found for payment intent');
    }
    
    const orderDoc = ordersQuery.docs[0];
    const orderRef = orderDoc.ref;
    const order = orderDoc.data() as Order;
    
    // Update the order status
    await orderRef.update({
      status: 'payment_succeeded',
      updatedAt: Timestamp.now()
    });
    
    // Generate a 6-digit secret code for the buyer
    const secretCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Store the secret code
    await db.collection('codes').doc(order.id).set({
      orderId: order.id,
      code: secretCode,
      expiresAt: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30 days expiry
      isUsed: false,
      createdAt: Timestamp.now()
    });
    
    // Update the order with the secret code and auto-release date
    await orderRef.update({
      status: 'payment_succeeded',
      secretCode,
      autoReleaseDate: Timestamp.fromDate(new Date(Date.now() + stripeConfig.autoReleaseEscrowDays * 24 * 60 * 60 * 1000)),
      updatedAt: Timestamp.now()
    });
    
    // Import the Shippo integration
    const { createShippingLabel } = await import('./shippo');
    
    try {
      // We'll call this asynchronously so it doesn't block the payment confirmation
      // In a production environment, you might want to use a queue or a separate function
      createShippingLabel(order.id).then(label => {
        console.log(`Shipping label generated for order ${order.id}: ${label.trackingNumber}`);
        orderRef.update({
          status: 'shipped_pending_code',
          updatedAt: Timestamp.now()
        });
      }).catch(error => {
        console.error(`Error generating shipping label for order ${order.id}:`, error);
        // We don't want to fail the payment process if label generation fails
        // The seller can manually generate the label later
      });
    } catch (error) {
      console.error(`Error initiating shipping label generation for order ${order.id}:`, error);
      // We don't want to fail the payment process if label generation fails
      // The seller can manually generate the label later
    }
    
    // Update the listing status to sold (payment succeeded)
    await db.collection('listings').doc(listingId).update({
      status: 'sold',
      soldAt: Timestamp.now(),
      soldTo: buyerId,
      updatedAt: Timestamp.now()
    });

    // Deduct wallet credit now that payment is successful
    const walletAmountUsed = parseFloat(metadata.walletAmountUsed || '0');
    if (walletAmountUsed > 0) {
      try {
        const walletRef = db.collection('wallets').doc(buyerId);
        const walletDoc = await walletRef.get();

        if (walletDoc.exists) {
          const _currentWallet = walletDoc.data();

          // Create transaction record
          const transaction = {
            id: db.collection('temp').doc().id,
            type: 'debit',
            amount: walletAmountUsed,
            description: `Purchase: ${order.title}`,
            source: 'purchase_deduction',
            orderId: order.id,
            createdAt: Timestamp.now()
          };

          // Update wallet with deduction and transaction history
          await walletRef.update({
            balance: FieldValue.increment(-walletAmountUsed),
            history: FieldValue.arrayUnion(transaction),
            lastUpdated: Timestamp.now()
          });

          console.log(`Deducted $${walletAmountUsed} from wallet for order ${order.id}`);
        } else {
          console.error(`Wallet not found for user ${buyerId} when trying to deduct $${walletAmountUsed}`);
        }
      } catch (walletError) {
        console.error('Error deducting wallet balance:', walletError);
        // Don't fail the entire payment process if wallet deduction fails
        // The payment was successful, so we should continue
      }
    }

    // Add cashback to buyer's wallet if applicable
    if (cashbackAmount && parseFloat(cashbackAmount) > 0) {
      try {
        const walletRef = db.collection('wallets').doc(buyerId);
        const walletDoc = await walletRef.get();

        // Create cashback transaction record
        const cashbackTransaction = {
          id: db.collection('temp').doc().id,
          type: 'credit',
          amount: parseFloat(cashbackAmount),
          description: `Cashback from purchase: ${order.title}`,
          source: 'cashback',
          orderId: order.id,
          createdAt: Timestamp.now()
        };

        if (walletDoc.exists) {
          // Update existing wallet with cashback and transaction history
          await walletRef.update({
            balance: FieldValue.increment(parseFloat(cashbackAmount)),
            history: FieldValue.arrayUnion(cashbackTransaction),
            lastUpdated: Timestamp.now()
          });
        } else {
          // Create new wallet with cashback
          await walletRef.set({
            userId: buyerId,
            balance: parseFloat(cashbackAmount),
            referralCode: `user${buyerId.substring(0, 6)}`, // Generate basic referral code
            usedReferral: false,
            history: [cashbackTransaction],
            grantedBy: 'cashback',
            createdAt: Timestamp.now(),
            lastUpdated: Timestamp.now()
          });
        }

        console.log(`Added $${cashbackAmount} cashback to wallet for order ${order.id}`);
      } catch (cashbackError) {
        console.error('Error adding cashback to wallet:', cashbackError);
        // Don't fail the payment process if cashback fails
      }
    }
    
    // Send payment confirmation emails
    try {
      const buyerDoc = await db.collection('users').doc(order.buyerId).get();
      const sellerDoc = await db.collection('users').doc(order.sellerId).get();

      const buyerData = buyerDoc.data();
      const sellerData = sellerDoc.data();

      if (buyerData?.email) {
        console.log('Sending payment confirmation email to buyer:', buyerData.email);

        // In a real implementation, you would call the email function here
        // For now, we'll just log the email data
        const emailData = {
          orderId: order.id,
          buyerEmail: buyerData.email,
          sellerEmail: sellerData?.email,
          orderDetails: {
            title: order.title,
            amount: order.amount,
            sellerAmount: order.sellerAmount,
            sellerName: sellerData?.name || 'Unknown Seller',
            listingId: order.listingId
          }
        };

        console.log('Payment confirmation email data:', emailData);
      }
    } catch (emailError) {
      console.error('Error preparing payment confirmation email:', emailError);
      // Don't throw error as payment was successful
    }

    console.log(`Order ${order.id} has been processed and is ready for shipping`);
  } catch (error) {
    console.error('Error handling payment succeeded webhook:', error);
    throw error;
  }
}

/**
 * Releases funds to the seller when the buyer enters the secret code
 */
export async function releaseFundsWithCode(orderId: string, code: string): Promise<boolean> {
  try {
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }
    
    const order = orderDoc.data() as Order;
    
    // Check if the order is in the correct status
    if (order.status !== 'shipped_pending_code') {
      throw new Error('Order is not in the correct status for code verification');
    }
    
    // Get the secret code
    const codeDoc = await db.collection('codes').doc(orderId).get();
    if (!codeDoc.exists) {
      throw new Error('Secret code not found');
    }
    
    const secretCode = codeDoc.data() as SecretCode;
    
    // Check if the code is already used
    if (secretCode.isUsed) {
      throw new Error('Secret code has already been used');
    }
    
    // Check if the code is correct
    if (secretCode.code !== code) {
      throw new Error('Invalid secret code');
    }
    
    // Capture the payment intent to release funds to the seller
    if (order.paymentIntentId) {
      await stripe.paymentIntents.capture(order.paymentIntentId);
    }
    
    // Update the order status
    await db.collection('orders').doc(orderId).update({
      status: 'completed',
      updatedAt: Timestamp.now()
    });
    
    // Mark the code as used
    await db.collection('codes').doc(orderId).update({
      isUsed: true,
      usedAt: Timestamp.now()
    });
    
    // Update the listing status to sold
    await db.collection('listings').doc(order.listingId).update({
      status: 'sold',
      updatedAt: Timestamp.now()
    });
    
    return true;
  } catch (error) {
    console.error('Error releasing funds with code:', error);
    throw error;
  }
}

/**
 * Automatically releases funds to the seller after the escrow period
 */
export async function autoReleaseFunds(): Promise<void> {
  try {
    const now = Timestamp.now();
    
    // Find orders that are past their auto-release date
    const ordersQuery = await db.collection('orders')
      .where('status', '==', 'shipped_pending_code')
      .where('autoReleaseDate', '<=', now)
      .limit(50) // Process in batches
      .get();
    
    if (ordersQuery.empty) {
      console.log('No orders to auto-release');
      return;
    }
    
    // Process each order
    const batch = db.batch();
    
    for (const doc of ordersQuery.docs) {
      const order = doc.data() as Order;
      
      // Capture the payment intent to release funds to the seller
      if (order.paymentIntentId) {
        await stripe.paymentIntents.capture(order.paymentIntentId);
      }
      
      // Update the order
      batch.update(doc.ref, {
        status: 'completed',
        updatedAt: now
      });
      
      // Mark the code as used
      batch.update(db.collection('codes').doc(order.id), {
        isUsed: true,
        usedAt: now
      });
      
      // Update the listing
      batch.update(db.collection('listings').doc(order.listingId), {
        status: 'sold',
        updatedAt: now
      });
    }
    
    await batch.commit();
    console.log(`Auto-released funds for ${ordersQuery.size} orders`);
  } catch (error) {
    console.error('Error auto-releasing funds:', error);
    throw error;
  }
}

/**
 * Creates a Stripe Connect account for a seller
 */
export async function createConnectAccount(userId: string, accountType: 'student' | 'merchant'): Promise<{ accountId: string; accountLinkUrl: string }> {
  try {
    // Get the user data
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new Error('User not found');
    }
    
    const user = userDoc.data() as FirebaseFirestore.DocumentData;
    
    // Create a Stripe Connect Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US',
      email: user.email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: accountType === 'merchant' ? 'company' : 'individual',
      metadata: {
        userId,
        accountType
      }
    });
    
    // Store the Connect account in Firestore
    await db.collection('connectAccounts').doc(userId).set({
      userId,
      stripeAccountId: account.id,
      isOnboarded: false,
      accountType,
      createdAt: Timestamp.now()
    });
    
    // Create an account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: stripeConfig.connect.refreshUrl,
      return_url: stripeConfig.connect.returnUrl,
      type: 'account_onboarding',
    });
    
    return {
      accountId: account.id,
      accountLinkUrl: accountLink.url
    };
  } catch (error) {
    console.error('Error creating Connect account:', error);
    throw error;
  }
}

/**
 * Applies wallet balance to a checkout
 */
export async function applyWalletBalance(userId: string, amount: number): Promise<number> {
  try {
    // Get the user's wallet
    const walletDoc = await db.collection('wallets').doc(userId).get();
    
    if (!walletDoc.exists) {
      return 0; // No wallet exists
    }
    
    const wallet = walletDoc.data() as Wallet;
    
    // Calculate how much can be applied
    const amountToApply = Math.min(wallet.balance, amount);
    
    // Ensure we're not reducing the Stripe charge below the minimum
    const remainingAmount = amount - amountToApply;
    
    if (remainingAmount < stripeConfig.minimumChargeAmount) {
      // Adjust the amount to apply to ensure minimum Stripe charge
      const adjustedAmountToApply = amount - stripeConfig.minimumChargeAmount;
      return Math.max(0, adjustedAmountToApply);
    }
    
    return amountToApply;
  } catch (error) {
    console.error('Error applying wallet balance:', error);
    throw error;
  }
}
