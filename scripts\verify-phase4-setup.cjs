#!/usr/bin/env node

/**
 * Phase 4 Setup Verification Script
 * Verifies that all security audit and load testing scripts are properly configured
 */

const fs = require('fs');
const path = require('path');

class Phase4Verifier {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
  }

  verify() {
    console.log('🔍 Verifying Phase 4: Production Deployment Setup');
    console.log('=' .repeat(60));

    this.checkFiles();
    this.checkPackageScripts();
    this.checkSecurityUtils();
    this.checkFirebaseConfig();
    this.checkEnvironmentTemplate();

    this.generateReport();
  }

  checkFiles() {
    console.log('\n📁 Checking Required Files...');
    
    const requiredFiles = [
      'scripts/production-security-audit.cjs',
      'scripts/load-testing.cjs',
      'scripts/production-deployment-checklist.md',
      'scripts/performance-test.js',
      'src/utils/security.ts',
      'firestore.rules',
      'firebase.json',
      'PHASE-4-PRODUCTION-DEPLOYMENT.md'
    ];

    requiredFiles.forEach(file => {
      const fullPath = path.join(process.cwd(), file);
      if (fs.existsSync(fullPath)) {
        this.addResult('PASS', `✅ ${file} exists`);
      } else {
        this.addResult('FAIL', `❌ ${file} missing`);
      }
    });
  }

  checkPackageScripts() {
    console.log('\n📦 Checking Package.json Scripts...');
    
    const packagePath = path.join(process.cwd(), 'package.json');
    if (!fs.existsSync(packagePath)) {
      this.addResult('FAIL', '❌ package.json not found');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const requiredScripts = [
      'security:audit',
      'security:check',
      'security:dependencies',
      'load:test',
      'load:test:artillery',
      'production:audit',
      'production:deploy',
      'production:verify'
    ];

    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        this.addResult('PASS', `✅ Script '${script}' configured`);
      } else {
        this.addResult('FAIL', `❌ Script '${script}' missing`);
      }
    });
  }

  checkSecurityUtils() {
    console.log('\n🔒 Checking Security Utilities...');
    
    const securityUtilsPath = path.join(process.cwd(), 'src/utils/security.ts');
    if (!fs.existsSync(securityUtilsPath)) {
      this.addResult('FAIL', '❌ Security utilities not found');
      return;
    }

    const securityUtils = fs.readFileSync(securityUtilsPath, 'utf8');
    
    const requiredClasses = [
      'InputSanitizer',
      'CSPManager',
      'AuthSecurity',
      'ClientRateLimit',
      'SecureStorage'
    ];

    requiredClasses.forEach(className => {
      if (securityUtils.includes(`class ${className}`)) {
        this.addResult('PASS', `✅ ${className} class implemented`);
      } else {
        this.addResult('WARN', `⚠️  ${className} class not found`);
      }
    });

    const requiredFunctions = [
      'enforceHTTPS',
      'validateSecurityHeaders',
      'initializeSecurity'
    ];

    requiredFunctions.forEach(funcName => {
      if (securityUtils.includes(`${funcName}`)) {
        this.addResult('PASS', `✅ ${funcName} function implemented`);
      } else {
        this.addResult('WARN', `⚠️  ${funcName} function not found`);
      }
    });
  }

  checkFirebaseConfig() {
    console.log('\n🔥 Checking Firebase Configuration...');
    
    const firebaseConfigPath = path.join(process.cwd(), 'firebase.json');
    if (!fs.existsSync(firebaseConfigPath)) {
      this.addResult('FAIL', '❌ firebase.json not found');
      return;
    }

    try {
      const firebaseConfig = JSON.parse(fs.readFileSync(firebaseConfigPath, 'utf8'));
      
      if (firebaseConfig.hosting) {
        this.addResult('PASS', '✅ Firebase hosting configured');
        
        if (firebaseConfig.hosting.headers) {
          this.addResult('PASS', '✅ Security headers configured');
        } else {
          this.addResult('WARN', '⚠️  Security headers not configured');
        }
      } else {
        this.addResult('WARN', '⚠️  Firebase hosting not configured');
      }

      if (firebaseConfig.functions) {
        this.addResult('PASS', '✅ Firebase functions configured');
      } else {
        this.addResult('WARN', '⚠️  Firebase functions not configured');
      }

    } catch (error) {
      this.addResult('FAIL', `❌ Invalid firebase.json: ${error.message}`);
    }
  }

  checkEnvironmentTemplate() {
    console.log('\n🌍 Checking Environment Configuration...');
    
    const envExamplePath = path.join(process.cwd(), '.env.example');
    const hasEnvExample = fs.existsSync(envExamplePath);
    
    if (hasEnvExample) {
      this.addResult('PASS', '✅ .env.example template exists');
      
      const envExample = fs.readFileSync(envExamplePath, 'utf8');
      const requiredVars = [
        'VITE_FIREBASE_API_KEY',
        'VITE_FIREBASE_AUTH_DOMAIN',
        'VITE_FIREBASE_PROJECT_ID',
        'VITE_STRIPE_PUBLISHABLE_KEY'
      ];

      requiredVars.forEach(varName => {
        if (envExample.includes(varName)) {
          this.addResult('PASS', `✅ ${varName} in template`);
        } else {
          this.addResult('WARN', `⚠️  ${varName} missing from template`);
        }
      });
    } else {
      this.addResult('WARN', '⚠️  .env.example template not found');
    }

    // Check for .env files in git
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignore = fs.readFileSync(gitignorePath, 'utf8');
      if (gitignore.includes('.env')) {
        this.addResult('PASS', '✅ .env files properly ignored in git');
      } else {
        this.addResult('WARN', '⚠️  .env files might not be ignored in git');
      }
    }
  }

  addResult(type, message) {
    this.results.details.push({ type, message });
    
    switch (type) {
      case 'PASS':
        this.results.passed++;
        console.log(message);
        break;
      case 'FAIL':
        this.results.failed++;
        console.log(message);
        break;
      case 'WARN':
        this.results.warnings++;
        console.log(message);
        break;
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 PHASE 4 SETUP VERIFICATION SUMMARY');
    console.log('='.repeat(60));

    const total = this.results.passed + this.results.failed + this.results.warnings;
    const score = Math.round((this.results.passed / total) * 100);

    console.log(`Overall Score: ${score}%`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings}`);

    if (this.results.failed > 0) {
      console.log('\n🚨 Critical Issues Found:');
      this.results.details
        .filter(detail => detail.type === 'FAIL')
        .forEach(detail => console.log(`   ${detail.message}`));
    }

    if (this.results.warnings > 0) {
      console.log('\n⚠️  Warnings to Address:');
      this.results.details
        .filter(detail => detail.type === 'WARN')
        .forEach(detail => console.log(`   ${detail.message}`));
    }

    const isReady = this.results.failed === 0;
    console.log(`\n🚀 Phase 4 Setup: ${isReady ? '✅ READY' : '❌ NEEDS ATTENTION'}`);

    if (isReady) {
      console.log('\n🎉 Phase 4 setup is complete! You can now run:');
      console.log('   npm run security:audit     - Run security audit');
      console.log('   npm run load:test          - Run load testing');
      console.log('   npm run production:verify  - Complete verification');
      console.log('   npm run production:deploy  - Deploy to production');
    } else {
      console.log('\n⛔ Please fix the failed checks before proceeding with Phase 4.');
    }

    console.log('\n📚 Documentation:');
    console.log('   - PHASE-4-PRODUCTION-DEPLOYMENT.md');
    console.log('   - scripts/production-deployment-checklist.md');
  }
}

// Run verification
const verifier = new Phase4Verifier();
verifier.verify();