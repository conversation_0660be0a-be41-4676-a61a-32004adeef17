<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache - Hive Campus</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        .button {
            background: #f9a826;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .button:hover {
            background: #e8971f;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧹 Clear Hive Campus Cache</h1>
    <p>If you're experiencing issues with the app, use this tool to clear all cached data.</p>
    
    <div id="status"></div>
    
    <button class="button" onclick="clearAllCache()">Clear All Cache</button>
    <button class="button" onclick="clearServiceWorkers()">Clear Service Workers</button>
    <button class="button" onclick="clearLocalStorage()">Clear Local Storage</button>
    <button class="button" onclick="goToApp()">Go to App</button>
    
    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function clearServiceWorkers() {
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                    showStatus('✅ Service workers cleared successfully!', 'success');
                } catch (error) {
                    showStatus('❌ Error clearing service workers: ' + error.message, 'error');
                }
            } else {
                showStatus('ℹ️ Service workers not supported in this browser', 'info');
            }
        }
        
        function clearLocalStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                showStatus('✅ Local storage cleared successfully!', 'success');
            } catch (error) {
                showStatus('❌ Error clearing local storage: ' + error.message, 'error');
            }
        }
        
        async function clearAllCache() {
            showStatus('🧹 Clearing all cache...', 'info');
            
            // Clear service workers
            await clearServiceWorkers();
            
            // Clear local storage
            clearLocalStorage();
            
            // Clear browser cache if possible
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                    showStatus('✅ All cache cleared! Please refresh the page.', 'success');
                } catch (error) {
                    showStatus('⚠️ Cache partially cleared. Please manually refresh the page.', 'info');
                }
            } else {
                showStatus('✅ Cache cleared! Please manually refresh the page.', 'success');
            }
            
            // Auto-redirect after 3 seconds
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        }
        
        function goToApp() {
            window.location.href = '/';
        }
        
        // Auto-clear on page load if requested
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto') === 'true') {
            clearAllCache();
        }
    </script>
</body>
</html>
