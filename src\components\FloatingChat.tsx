
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { MessageCircle } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';


interface FloatingChatProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

/**
 * FloatingChat - A floating chat button that provides quick access to messaging
 * Shows recent conversations and allows starting new chats
 */
const FloatingChat: React.FC<FloatingChatProps> = ({
  position = 'bottom-right'
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();

  // Don't render if user is not authenticated
  if (!currentUser) {
    return null;
  }

  // Don't render if user is on the messages page
  if (location.pathname === '/messages') {
    return null;
  }

  // Adjust positioning to be higher and avoid mobile navigation
  const positionClasses = {
    'bottom-right': 'bottom-24 right-4 md:bottom-4 md:right-4',  // Higher on mobile, normal on desktop
    'bottom-left': 'bottom-24 left-4 md:bottom-4 md:left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };



  const handleFloatingChatClick = () => {
    navigate('/messages');
  };



  return (
    <div className={`fixed z-30 ${positionClasses[position]}`}>
      {/* Chat Button */}
      <button
        onClick={handleFloatingChatClick}
        className="relative rounded-full p-3 shadow-lg transition-all duration-300 bg-primary-600 hover:bg-primary-700 hover:scale-105"
        aria-label="Open messages"
      >
        <MessageCircle className="h-6 w-6 text-white" />
      </button>
      


    </div>
  );
};

export default FloatingChat;