import * as functions from 'firebase-functions';
import { db } from '../config/firebase';
import { Timestamp } from 'firebase-admin/firestore';
import { ShippingLabel, Order, ShippingAddress } from './types';
import shippoConstructor from 'shippo';

// Define interfaces for user data and shippo objects
interface UserData {
  displayName?: string;
  email: string;
  address?: ShippingAddress;
  shippingAddress: ShippingAddress;
  phone?: string;
}

interface ShippoAddress {
  name: string;
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone?: string;
  email?: string;
}

interface ShippoParcel {
  length: string;
  width: string;
  height: string;
  distance_unit: string;
  weight: string;
  mass_unit: string;
}

interface ShippoRateRequest {
  address_from: ShippoAddress;
  address_to: ShippoAddress;
  parcels: ShippoParcel[];
  async: boolean;
}

interface ShippoRate {
  object_id: string;
  amount: string;
  currency: string;
  provider: string;
  servicelevel: {
    name: string;
    token: string;
  };
  estimated_days: number;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface ShippoTransaction {
  object_id: string;
  status: string;
  label_url: string;
  tracking_number: string;
  tracking_url_provider: string;
  messages?: string;
}

interface ShippoTransactionRequest {
  rate: string;
  label_file_type: string;
  async: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface ShippoShipment {
  rates: ShippoRate[];
}

// Shippo API configuration
const SHIPPO_API_KEY = process.env.SHIPPO_API_KEY || 'shippo_test_placeholder_api_key';
const shippo = shippoConstructor(SHIPPO_API_KEY);

/**
 * Creates a Shippo address object from user data
 */
function createShippoAddress(userData: UserData, isFrom: boolean): ShippoAddress {
  return {
    name: userData.displayName || userData.email,
    street1: isFrom ? (userData.address?.line1 || '123 Campus Drive') : userData.shippingAddress.line1,
    street2: isFrom ? (userData.address?.line2 || '') : (userData.shippingAddress.line2 || ''),
    city: isFrom ? (userData.address?.city || 'Stanford') : userData.shippingAddress.city,
    state: isFrom ? (userData.address?.state || 'CA') : userData.shippingAddress.state,
    zip: isFrom ? (userData.address?.postalCode || '94305') : userData.shippingAddress.postalCode,
    country: isFrom ? (userData.address?.country || 'US') : userData.shippingAddress.country,
    phone: isFrom ? (userData.phone || '******-123-4567') : (userData.shippingAddress.phone || userData.phone || ''),
    email: userData.email
  };
}

/**
 * Creates a Shippo parcel object based on the order
 */
function createShippoParcel(order: Order): ShippoParcel {
  // Default dimensions for a small package
  const defaultParcel: ShippoParcel = {
    length: '8',
    width: '6',
    height: '4',
    distance_unit: 'in',
    weight: '2',
    mass_unit: 'lb'
  };

  // You could customize the parcel based on the order category or other attributes
  switch (order.category?.toLowerCase()) {
    case 'electronics':
      return {
        length: '12',
        width: '8',
        height: '6',
        distance_unit: 'in',
        weight: '3',
        mass_unit: 'lb'
      };
    case 'clothing':
      return {
        length: '10',
        width: '8',
        height: '2',
        distance_unit: 'in',
        weight: '1',
        mass_unit: 'lb'
      };
    case 'books':
      return {
        length: '9',
        width: '6',
        height: '2',
        distance_unit: 'in',
        weight: '2.5',
        mass_unit: 'lb'
      };
    default:
      return defaultParcel;
  }
}

/**
 * Creates a shipping label using Shippo API
 */
export async function generateShippingLabelAsync(orderId: string): Promise<ShippingLabel> {
  try {
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }
    
    const order = orderDoc.data() as Order;
    
    // Get buyer and seller information
    const buyerDoc = await db.collection('users').doc(order.buyerId).get();
    const sellerDoc = await db.collection('users').doc(order.sellerId).get();
    
    if (!buyerDoc.exists || !sellerDoc.exists) {
      throw new Error('Buyer or seller information not found');
    }
    
    const buyer = buyerDoc.data() as UserData;
    const seller = sellerDoc.data() as UserData;
    
    // Ensure we have shipping address
    if (!order.shippingAddress) {
      throw new Error('Shipping address not found for this order');
    }
    
    // Create Shippo addresses
    const fromAddress = createShippoAddress(seller, true);
    const toAddress = createShippoAddress({
      ...buyer,
      shippingAddress: order.shippingAddress
    }, false);
    
    // Create parcel object
    const parcel = createShippoParcel(order);
    
    // Create shipment object
    const shipment: ShippoRateRequest = {
      address_from: fromAddress,
      address_to: toAddress,
      parcels: [parcel],
      async: false
    };
    
    console.log('Creating shipment:', JSON.stringify(shipment, null, 2));
    
    // Create the shipment to get rates
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const shipmentResponse = await shippo.shipment.create(shipment as any);
    
    // Type guard to ensure rates exist and are an array
    if (!shipmentResponse.rates || !Array.isArray(shipmentResponse.rates) || shipmentResponse.rates.length === 0) {
      throw new Error('No shipping rates available');
    }
    
    // Select the cheapest available rate
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const selectedRate = shipmentResponse.rates.reduce((cheapest: any, rate: any) =>
      parseFloat(rate.amount) < parseFloat(cheapest.amount) ? rate : cheapest
    );
    
    console.log('Selected rate:', selectedRate);
    
    // Create the shipping label transaction
    const transactionData: ShippoTransactionRequest = {
      rate: selectedRate.object_id,
      label_file_type: 'PDF',
      async: false
    };
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const transaction = await shippo.transaction.create(transactionData as any);
    
    if (transaction.status !== 'SUCCESS') {
      throw new Error(`Shipping label creation failed: ${transaction.messages}`);
    }
    
    const trackingUrl = `https://tools.usps.com/go/TrackConfirmAction?tLabels=${transaction.tracking_number}`;
    
    // Create the shipping label document
    const shippingLabel: ShippingLabel = {
      orderId,
      carrier: selectedRate.provider,
      trackingNumber: transaction.tracking_number as string,
      labelUrl: transaction.label_url as string,
      createdAt: Timestamp.now()
    };
    
    // Save the shipping label to Firestore
    await db.collection('shippingLabels').doc(orderId).set(shippingLabel);
    
    // Update the order with tracking information
    await db.collection('orders').doc(orderId).update({
      status: 'shipped_pending_code',
      trackingInfo: {
        carrier: selectedRate.provider,
        trackingNumber: transaction.tracking_number,
        labelUrl: transaction.label_url,
        shippedDate: Timestamp.now()
      },
      updatedAt: Timestamp.now()
    });
    
    // Create a notification for the buyer
    await db.collection('notifications').add({
      userId: order.buyerId,
      type: 'order_shipped',
      title: 'Order Shipped',
      message: `Your order #${orderId} has been shipped. You can track your package using the tracking information.`,
      data: {
        orderId,
        trackingUrl
      },
      read: false,
      createdAt: Timestamp.now()
    });
    
    return shippingLabel;
  } catch (error) {
    console.error('Error generating shipping label:', error);
    throw error;
  }
}

/**
 * Cloud Function to generate a shipping label
 */
// Helper function to generate shipping label
export const createShippingLabel = async (orderId: string): Promise<ShippingLabel> => {
  try {
    // Check if order exists
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }
    
    const order = orderDoc.data() as Order;
    
    // Get buyer and seller information
    const buyerDoc = await db.collection('users').doc(order.buyerId).get();
    const sellerDoc = await db.collection('users').doc(order.sellerId).get();
    
    if (!buyerDoc.exists || !sellerDoc.exists) {
      throw new Error('Buyer or seller information not found');
    }
    
    const buyer = buyerDoc.data() as UserData;
    const seller = sellerDoc.data() as UserData;
    
    // Ensure we have shipping address
    if (!order.shippingAddress) {
      throw new Error('Shipping address not found for this order');
    }
    
    // Create Shippo addresses
    const fromAddress = createShippoAddress(seller, true);
    const toAddress = createShippoAddress({
      ...buyer,
      shippingAddress: order.shippingAddress
    }, false);
    
    // Create parcel object
    const parcel = createShippoParcel(order);
    
    // Create shipment object
    const shipment: ShippoRateRequest = {
      address_from: fromAddress,
      address_to: toAddress,
      parcels: [parcel],
      async: false
    };
    
    console.log('Creating shipment:', JSON.stringify(shipment, null, 2));
    
    // Create the shipment to get rates
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const shipmentResponse = await shippo.shipment.create(shipment as any);
    
    // Type guard to ensure rates exist and are an array
    if (!shipmentResponse.rates || !Array.isArray(shipmentResponse.rates) || shipmentResponse.rates.length === 0) {
      throw new Error('No shipping rates available');
    }
    
    // Select the cheapest available rate
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const cheapestRate = shipmentResponse.rates.reduce((prev: any, current: any) =>
      parseFloat(prev.amount) < parseFloat(current.amount) ? prev : current
    );
    
    // Create transaction to purchase the shipping label
    const transactionData: ShippoTransactionRequest = {
      rate: cheapestRate.object_id,
      label_file_type: 'PDF',
      async: false
    };
    
    console.log('Creating transaction:', JSON.stringify(transactionData, null, 2));
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const transaction = await shippo.transaction.create(transactionData as any);
    
    if (transaction.status !== 'SUCCESS') {
      throw new Error(`Transaction failed: ${transaction.messages || 'Unknown error'}`);
    }
    
    // Save the shipping label to Firestore
    const shippingLabel: ShippingLabel = {
      orderId,
      trackingNumber: transaction.tracking_number as string,
      labelUrl: transaction.label_url as string,
      carrier: cheapestRate.provider as string,
      createdAt: Timestamp.now(),
    };
    
    await db.collection('shippingLabels').doc(orderId).set(shippingLabel);
    
    // Update the order with tracking information
    await db.collection('orders').doc(orderId).update({
      status: 'shipped',
      trackingNumber: transaction.tracking_number,
      trackingUrl: transaction.tracking_url_provider,
      shippedAt: Timestamp.now(),
    });
    
    return shippingLabel;
  } catch (error) {
    console.error('Error generating shipping label:', error);
    throw error;
  }
};

export const generateShippingLabel = functions.https.onCall(async (data, context) => {
  try {
    // Ensure user is authenticated
    if (!context.auth) {
      throw new Error('Unauthorized');
    }
    
    const { orderId } = data;
    if (!orderId) {
      throw new Error('Order ID is required');
    }
    
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }
    
    const order = orderDoc.data() as Order;
    
    // Verify that the user is the seller
    if (order.sellerId !== context.auth.uid) {
      throw new Error('Only the seller can generate a shipping label');
    }
    
    // Check if a shipping label already exists
    const labelDoc = await db.collection('shippingLabels').doc(orderId).get();
    if (labelDoc.exists) {
      const existingLabel = labelDoc.data() as ShippingLabel;
      return {
        success: true,
        shippingLabel: existingLabel
      };
    }
    
    // Create the shipping label
    const shippingLabel = await createShippingLabel(orderId);
    
    return {
      success: true,
      shippingLabel
    };
  } catch (error) {
    console.error('Error in generateShippingLabel function:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
});
