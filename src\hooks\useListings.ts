import { useState, useCallback } from 'react';
import { 
  createListing, 
  editListing, 
  deleteListing, 
  getListings, 
  getListingById,
  searchListings
} from '../firebase';
import { Listing, ListingCondition, ListingType, ListingStatus } from '../firebase/types';

interface ListingsFilter {
  university?: string;
  category?: string;
  type?: ListingType;
  condition?: ListingCondition;
  minPrice?: number;
  maxPrice?: number;
  ownerId?: string;
  status?: ListingStatus;
}

interface ListingsResult {
  listings: Listing[];
  lastVisible: string | null;
  total: number;
}

// Firebase function response interfaces
interface FirebaseResponse<T> {
  success: boolean;
  data: T;
}

interface ListingsData {
  listings: Listing[];
  lastVisible: string | null;
  total: number;
}

// Additional data for rent/auction listings
interface AdditionalListingData {
  // Rent-specific fields
  rentalPeriod?: 'weekly' | 'monthly';
  weeklyPrice?: number;
  monthlyPrice?: number;
  startDate?: string;
  endDate?: string;

  // Auction-specific fields
  startingBid?: number;
  currentBid?: number;
  auctionStartDate?: string;
  auctionStartTime?: string;
  auctionEndDate?: string;
  auctionEndTime?: string;
  auctionDuration?: number;
  bidders?: string[];
  highestBidder?: string;
}

export const useListings = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [listingsResult, setListingsResult] = useState<ListingsResult>({
    listings: [],
    lastVisible: null,
    total: 0
  });

  // Create a new listing
  const addListing = useCallback(async (
    title: string,
    description: string,
    price: number,
    category: string,
    condition: ListingCondition,
    type: ListingType,
    imageURLs: string[],
    visibility: 'university' | 'public' = 'university',
    additionalData?: AdditionalListingData
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await createListing({
        title,
        description,
        price,
        category,
        condition,
        type,
        imageURLs,
        visibility,
        ...additionalData
      });

      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to create listing';
      setError(errorMsg);
      throw err;
    }
  }, []);

  // Update an existing listing
  const updateListing = useCallback(async (
    listingId: string,
    updates: Partial<Listing>
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await editListing({
        listingId,
        ...updates
      });
      
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to update listing';
      setError(errorMsg);
      throw err;
    }
  }, []);

  // Delete a listing
  const removeListing = useCallback(async (listingId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await deleteListing(listingId);
      
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to delete listing');
      throw err;
    }
  }, []);

  // Fetch a single listing by ID
  const fetchListing = useCallback(async (listingId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getListingById(listingId);

      setIsLoading(false);

      // Extract the listing data from the response
      if (result && (result as any).success && (result as any).data) {
        return (result as any).data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err: any) {
      setIsLoading(false);
      console.error('Error fetching listing:', err);
      setError(err.message || 'Failed to fetch listing');
      throw err;
    }
  }, []);

  // Fetch listings with filters
  const fetchListings = useCallback(async (
    filters: ListingsFilter = {},
    limit: number = 20,
    loadMore: boolean = false
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      // Get current lastVisible using a ref to avoid dependency issues
      let lastVisible: string | null = null;
      if (loadMore) {
        setListingsResult(prev => {
          lastVisible = prev.lastVisible;
          return prev;
        });
      }

      const result = await getListings({
        ...filters,
        limit,
        lastVisible: lastVisible || undefined
      });

      const typedResult = result as FirebaseResponse<ListingsData>;
      if (result && typedResult.success) {
        const newListings = typedResult.data.listings;

        // Use functional update to avoid dependency on listingsResult
        setListingsResult(prev => {
          if (loadMore) {
            // If loading more, append to existing listings
            return {
              listings: [...prev.listings, ...newListings],
              lastVisible: typedResult.data.lastVisible,
              total: typedResult.data.total
            };
          } else {
            // Otherwise, replace existing listings
            return {
              listings: newListings,
              lastVisible: typedResult.data.lastVisible,
              total: typedResult.data.total
            };
          }
        });
      }

      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to fetch listings');
      throw err;
    }
  }, []); // Removed listingsResult dependency to prevent infinite loop

  // Search listings by query
  const search = useCallback(async (query: string, limit: number = 20) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await searchListings(query, limit);
      
      const typedResult = result as FirebaseResponse<ListingsData>;
      if (result && typedResult.success) {
        setListingsResult({
          listings: typedResult.data.listings,
          lastVisible: null, // Search doesn't support pagination currently
          total: typedResult.data.total
        });
      }
      
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to search listings');
      throw err;
    }
  }, []);

  return {
    listings: listingsResult.listings,
    lastVisible: listingsResult.lastVisible,
    total: listingsResult.total,
    isLoading,
    error,
    addListing,
    updateListing,
    removeListing,
    fetchListing,
    fetchListings,
    search,
    hasMore: listingsResult.lastVisible !== null
  };
};