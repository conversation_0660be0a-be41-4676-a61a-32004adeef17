import { useState, useEffect, useCallback } from 'react';
import { X } from 'lucide-react';

// Define the BeforeInstallPromptEvent interface for TypeScript
interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>;
}

// Extend Window interface for PWA properties
declare global {
  interface Window {
    MSStream?: unknown;
  }
  
  interface Navigator {
    standalone?: boolean;
  }
}

/**
 * InstallPrompt Component
 * 
 * A reusable component that prompts users to install the Hive Campus PWA
 * on both Android (using beforeinstallprompt) and iOS (using a custom banner).
 */
const InstallPrompt: React.FC = () => {
  // State for storing the install prompt event
  const [installPromptEvent, setInstallPromptEvent] = useState<BeforeInstallPromptEvent | null>(null);
  
  // State for controlling visibility of prompts
  const [showAndroidPrompt, setShowAndroidPrompt] = useState<boolean>(false);
  const [showIOSPrompt, setShowIOSPrompt] = useState<boolean>(false);
  
  // State to track if the app is on iOS
  const [isIOS, setIsIOS] = useState<boolean>(false);

  // Handle the beforeinstallprompt event for Android/Chrome
  const handleBeforeInstallPrompt = useCallback((event: Event) => {
    // Prevent Chrome 76+ from automatically showing the prompt
    event.preventDefault();
    
    // Store the event for later use
    setInstallPromptEvent(event as BeforeInstallPromptEvent);
    
    // Show the install button
    setShowAndroidPrompt(true);
  }, []);

  // Handle the install button click for Android
  const handleInstallClick = async () => {
    if (!installPromptEvent) return;
    
    // Show the install prompt
    await installPromptEvent.prompt();
    
    // Wait for the user to respond to the prompt
    const choiceResult = await installPromptEvent.userChoice;
    
    // Hide the install button regardless of outcome
    setShowAndroidPrompt(false);
    
    // Log the outcome (for analytics purposes)
    if (choiceResult.outcome === 'accepted') {
      console.log('User accepted the install prompt');
    } else {
      console.log('User dismissed the install prompt');
    }
    
    // Clear the saved prompt as it can't be used again
    setInstallPromptEvent(null);
  };

  // Dismiss the iOS prompt and store in localStorage to avoid showing it again
  const dismissIOSPrompt = () => {
    setShowIOSPrompt(false);
    localStorage.setItem('iosPromptDismissed', 'true');
  };

  // Dismiss the Android prompt
  const dismissAndroidPrompt = () => {
    setShowAndroidPrompt(false);
  };

  useEffect(() => {
    // Check if the device is iOS
    const iosCheck = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    setIsIOS(iosCheck);
    
    // For iOS devices
    if (iosCheck) {
      // Check if the app is already installed (in standalone mode)
      const isStandalone = 'standalone' in window.navigator && window.navigator.standalone === true;
      
      // Check if we've already dismissed the prompt in this browser
      const hasPromptBeenDismissed = localStorage.getItem('iosPromptDismissed') === 'true';
      
      // Only show the iOS prompt if not in standalone mode and not previously dismissed
      if (!isStandalone && !hasPromptBeenDismissed) {
        // Show the iOS prompt after a short delay for better UX
        const timer = setTimeout(() => {
          setShowIOSPrompt(true);
        }, 2000);
        
        return () => clearTimeout(timer);
      }
    } 
    // For Android/Chrome devices
    else {
      // Listen for the beforeinstallprompt event
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      
      // Cleanup
      return () => {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      };
    }
  }, [handleBeforeInstallPrompt]);

  // Don't render anything if no prompt should be shown
  if (!showAndroidPrompt && !showIOSPrompt) return null;

  return (
    <div 
      className="fixed bottom-4 left-0 right-0 z-50 mx-auto px-4 pointer-events-none"
      role="complementary"
      aria-label="Install app prompt"
    >
      <div className="max-w-md mx-auto bg-white/90 backdrop-blur-lg rounded-xl shadow-lg border border-gray-200 overflow-hidden pointer-events-auto">
        {/* iOS Prompt */}
        {isIOS && showIOSPrompt && (
          <div className="p-4">
            <div className="flex items-start">
              <div className="flex-1">
                <h3 className="text-base font-semibold text-gray-900">Install Hive Campus</h3>
                <p className="mt-1 text-sm text-gray-600">
                  Tap Share → Add to Home Screen to install Hive Campus.
                </p>
                <div className="mt-2 flex items-center text-xs text-gray-500">
                  <span className="inline-flex items-center justify-center w-5 h-5 bg-gray-100 rounded-full mr-1">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                      <path d="M13 4.5a2.5 2.5 0 11.5 0v15a1 1 0 01-1 1h-9a1 1 0 01-1-1v-15a2.5 2.5 0 115 0V18a1 1 0 001 1h9a1 1 0 001-1V4.5z" />
                    </svg>
                  </span>
                  Install for the best experience
                </div>
              </div>
              <button 
                onClick={dismissIOSPrompt}
                className="ml-4 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Dismiss install prompt"
              >
                <X size={20} />
              </button>
            </div>
          </div>
        )}

        {/* Android Prompt */}
        {!isIOS && showAndroidPrompt && (
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="text-base font-semibold text-gray-900">Install Hive Campus</h3>
                <p className="mt-1 text-sm text-gray-600">
                  Add to your home screen for easy access.
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleInstallClick}
                  className="px-4 py-2 bg-[#f9a826] text-white text-sm font-medium rounded-lg hover:bg-[#e89417] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#f9a826] transition-colors"
                  aria-label="Install Hive Campus app"
                >
                  Install
                </button>
                <button
                  onClick={dismissAndroidPrompt}
                  className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  aria-label="Dismiss install prompt"
                >
                  <X size={20} />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InstallPrompt;