# Hive Campus Stripe Integration

This document provides an overview of the Stripe integration for the Hive Campus marketplace app.

## Overview

The Stripe integration enables secure payments between buyers and sellers on the Hive Campus platform. It includes:

1. **Stripe Connect** for onboarding student sellers and merchant partners
2. **Escrow System** to hold funds until buyers confirm receipt
3. **Wallet System** for cashback and balance redemption
4. **Commission Structure** with different rates for textbooks vs. other items

## Key Features

### Stripe Connect Express

- Student sellers and merchant partners can create Stripe Connect accounts
- Funds are transferred directly to their bank accounts
- Platform handles commission calculation and fee deduction

### Escrow Protection

- When a buyer makes a purchase, funds are held in escrow
- A 6-digit secret code is generated for the buyer
- The buyer enters the code upon receiving the item to release funds
- If the buyer doesn't enter the code, funds are auto-released after 3 days

### Wallet & Cashback

- Buyers earn 2% cashback on all purchases
- Wallet balance can be applied to future purchases
- Minimum Stripe charge is $0.50, so wallet balance cannot be used for the entire purchase amount

### Commission Structure

- 8% commission (including Stripe fees) for textbooks and course materials
- 10% commission (including Stripe fees) for all other categories

## Implementation Details

### Firebase Functions

The backend logic is implemented using Firebase Functions:

- `stripeApi`: Express app handling all Stripe-related endpoints
- `checkEscrowPeriods`: Scheduled function to auto-release funds after escrow period

### API Endpoints

- `/checkout-session`: Creates a Stripe Checkout session
- `/connect-account`: Creates a Stripe Connect account
- `/release-funds`: Releases funds to the seller using the secret code
- `/shipping-label`: Generates a shipping label for an order
- `/wallet-balance`: Gets the wallet balance for a user
- `/order/:orderId`: Gets an order by ID
- `/webhook`: Handles Stripe webhook events

### Firestore Collections

- `/orders/{orderId}`: Order information
- `/wallets/{userId}`: User wallet balances
- `/shippingLabels/{orderId}`: Shipping label information
- `/codes/{orderId}`: Secret codes for order verification
- `/connectAccounts/{userId}`: Stripe Connect account information

## Frontend Components

- `Wallet.tsx`: Displays wallet balance and transaction history
- `PaymentSettings.tsx`: Allows sellers to set up their Stripe Connect account
- `Checkout.tsx`: Handles the checkout process
- `OrderTracking.tsx`: Displays order status and allows buyers to enter the secret code
- `CheckoutSuccessPage.tsx`: Displayed after a successful checkout

## Setup Instructions

1. Create a Stripe account and enable Stripe Connect
2. Set up Firebase Functions environment variables:
   ```
   firebase functions:config:set stripe.secret_key="sk_test_your_key" stripe.webhook_secret="whsec_your_secret" app.url="https://your-app-url.com"
   ```
3. Deploy Firebase Functions:
   ```
   cd functions
   npm install
   npm run deploy
   ```
4. Set up Stripe webhook endpoints in the Stripe Dashboard

## Testing

### Test Cards

- **Success**: 4242 4242 4242 4242
- **Requires Authentication**: 4000 0025 0000 3155
- **Declined**: 4000 0000 0000 0002

### Test Flow

1. Create a listing
2. Purchase the listing using a test card
3. Verify the order is created in Firestore
4. Enter the secret code to release funds
5. Verify the funds are released to the seller

## Security Considerations

- All API endpoints require Firebase Authentication
- Buyers can only release funds for their own orders
- Sellers cannot access buyer secret codes
- Stripe handles all sensitive payment information

## Resources

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Connect Documentation](https://stripe.com/docs/connect)
- [Firebase Functions Documentation](https://firebase.google.com/docs/functions)
- [Firestore Documentation](https://firebase.google.com/docs/firestore)