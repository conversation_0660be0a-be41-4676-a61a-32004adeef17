import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Package, 
  Download, 
  ExternalLink,
  Search,
  ChevronDown,
  AlertCircle
} from 'lucide-react';
import { useStripeCheckout } from '../hooks';
import { Order } from '../firebase/types';
import { Timestamp } from 'firebase/firestore';

// Interface for serialized Firestore timestamp
interface SerializedTimestamp {
  seconds: number;
  nanoseconds?: number;
}

const SellerOrdersTable: React.FC = () => {
  const { getOrdersBySeller, generateShippingLabel, isLoading } = useStripeCheckout();
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isLoadingOrders, setIsLoadingOrders] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingOrderId, setProcessingOrderId] = useState<string | null>(null);

  // Fetch orders
  useEffect(() => {
    const fetchOrders = async () => {
      setIsLoadingOrders(true);
      setError(null);
      
      try {
        const sellerOrders = await getOrdersBySeller();
        setOrders(sellerOrders);
        setFilteredOrders(sellerOrders);
      } catch (error: unknown) {
        console.error('Error fetching orders:', error);
        setError(error instanceof Error ? error.message : 'Failed to load orders');
      } finally {
        setIsLoadingOrders(false);
      }
    };
    
    fetchOrders();
  }, [getOrdersBySeller]);

  // Filter orders based on search query and status filter
  useEffect(() => {
    let result = orders;
    
    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(order => order.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(order => 
        order.id.toLowerCase().includes(query) || 
        order.title.toLowerCase().includes(query) ||
        order.buyerId.toLowerCase().includes(query)
      );
    }
    
    setFilteredOrders(result);
  }, [orders, searchQuery, statusFilter]);

  // Handle generating shipping label
  const handleGenerateLabel = async (orderId: string) => {
    setProcessingOrderId(orderId);
    
    try {
      await generateShippingLabel(orderId);
      
      // Refresh orders after generating label
      const updatedOrders = await getOrdersBySeller();
      setOrders(updatedOrders);
    } catch (error: unknown) {
      console.error('Error generating shipping label:', error);
      alert('Failed to generate shipping label: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setProcessingOrderId(null);
    }
  };

  // Format date
  const formatDate = (timestamp: Timestamp | Date | string | null | undefined) => {
    if (!timestamp) return 'N/A';

    try {
      let date: Date;

      if (timestamp instanceof Timestamp) {
        date = timestamp.toDate();
      } else if (timestamp && typeof timestamp === 'object' && 'seconds' in timestamp) {
        // Handle serialized Firestore timestamp
        date = new Date((timestamp as SerializedTimestamp).seconds * 1000);
      } else if (timestamp && typeof timestamp === 'object' && '_seconds' in timestamp) {
        // Handle Firebase client serialized timestamp
        date = new Date((timestamp as any)._seconds * 1000);
      } else {
        date = new Date(timestamp as string | Date);
      }

      if (isNaN(date.getTime())) {
        return 'N/A';
      }

      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'N/A';
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending_payment':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
            Pending Payment
          </span>
        );
      case 'payment_succeeded':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
            Payment Received
          </span>
        );
      case 'shipped_pending_code':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
            Shipped
          </span>
        );
      case 'delivered':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300">
            Delivered
          </span>
        );
      case 'completed':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
            Completed
          </span>
        );
      case 'cancelled':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
            Cancelled
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
            {status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
        );
    }
  };

  if (isLoadingOrders) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">Error Loading Orders</h3>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
      {/* Header and Filters */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Your Orders</h2>
          
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search orders..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-full"
              />
            </div>
            
            {/* Status Filter */}
            <div className="relative">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none w-full"
              >
                <option value="all">All Statuses</option>
                <option value="pending_payment">Pending Payment</option>
                <option value="payment_succeeded">Payment Received</option>
                <option value="shipped_pending_code">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Orders Table */}
      <div className="overflow-x-auto">
        {filteredOrders.length === 0 ? (
          <div className="p-8 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Orders Found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              {orders.length === 0 
                ? "You don't have any orders yet." 
                : "No orders match your current filters."}
            </p>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-750">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Order
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Shipping
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {order.title}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Order #{order.id.substring(0, 8)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{formatDate(order.createdAt)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(order.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">${order.amount.toFixed(2)}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      You receive: ${order.sellerAmount.toFixed(2)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {order.trackingInfo ? (
                      <div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                          {order.trackingInfo.carrier}
                        </div>
                        <div className="text-xs font-mono text-gray-900 dark:text-white">
                          {order.trackingInfo.trackingNumber}
                        </div>
                      </div>
                    ) : (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {order.status === 'payment_succeeded' ? 'Ready to ship' : 'Not shipped yet'}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      {/* View Order Details */}
                      <Link
                        to={`/order/${order.id}`}
                        className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                      >
                        View
                      </Link>
                      
                      {/* Generate/Download Shipping Label */}
                      {(order.status === 'payment_succeeded' || order.status === 'shipped_pending_code') && (
                        <>
                          {order.trackingInfo?.labelUrl ? (
                            <a
                              href={order.trackingInfo.labelUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            >
                              <Download className="w-4 h-4 mr-1" />
                              Label
                            </a>
                          ) : (
                            <button
                              onClick={() => handleGenerateLabel(order.id)}
                              disabled={isLoading || processingOrderId === order.id}
                              className="inline-flex items-center text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 disabled:opacity-50"
                            >
                              {processingOrderId === order.id ? (
                                <>
                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 dark:border-blue-400 mr-1"></div>
                                  Processing...
                                </>
                              ) : (
                                <>
                                  <Package className="w-4 h-4 mr-1" />
                                  Generate Label
                                </>
                              )}
                            </button>
                          )}
                        </>
                      )}
                      
                      {/* Track Package */}
                      {order.trackingInfo?.trackingUrl && (
                        <a
                          href={order.trackingInfo.trackingUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                        >
                          <ExternalLink className="w-4 h-4 mr-1" />
                          Track
                        </a>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default SellerOrdersTable;
