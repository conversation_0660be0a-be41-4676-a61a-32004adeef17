# Apply Ree<PERSON>lex updates script

# Update Slack utility
Write-Host "Updating Slack utility..." -ForegroundColor Cyan
Copy-Item -Path "functions/src/utils/slack.ts.new" -Destination "functions/src/utils/slack.ts" -Force

# Update Email utility
Write-Host "Updating Email utility..." -ForegroundColor Cyan
Copy-Item -Path "functions/src/utils/email.ts.new" -Destination "functions/src/utils/email.ts" -Force

# Update ReeFlexDashboard
Write-Host "Updating ReeFlexDashboard..." -ForegroundColor Cyan
Copy-Item -Path "src/pages/admin/ReeFlexDashboard.tsx.new" -Destination "src/pages/admin/ReeFlexDashboard.tsx" -Force

# Create directories if they don't exist
Write-Host "Creating component directories if needed..." -ForegroundColor Cyan
if (-not (Test-Path "src/components/reeflex")) {
    New-Item -Path "src/components/reeflex" -ItemType Directory -Force
}

Write-Host "ReeFlex updates applied successfully!" -ForegroundColor Green
Write-Host "Remember to install any required dependencies and restart your development server." -ForegroundColor Yellow