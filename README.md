# Hive Campus - Student Marketplace

Hive Campus is a student-exclusive marketplace app built with React, TypeScript, Firebase, and Tailwind CSS. It follows a liquid glass aesthetic with blur effects and modern UI design inspired by popular marketplace apps.

## Features

- **Authentication**: Email/password and Microsoft SSO (for .edu emails)
- **Listings**: Create, edit, delete, and browse listings
- **Messaging**: Real-time chat between users
- **Image Uploads**: Upload images for listings, profiles, and issue reports
- **Feedback and Reporting**: Submit feedback and report issues
- **Role-Based Access**: Student, merchant, and admin roles with appropriate permissions

## Tech Stack

- **Frontend**: React, TypeScript, Tailwind CSS
- **Backend**: Firebase (Authentication, Firestore, Storage, Functions)
- **State Management**: React Context API and custom hooks
- **Routing**: React Router v6
- **UI Components**: Custom components with Tailwind CSS
- **Icons**: Lucide React

## Project Structure

```
project/
├── functions/           # Firebase Cloud Functions
│   ├── src/
│   │   ├── auth/        # Authentication functions
│   │   ├── listings/    # Listing management functions
│   │   ├── messages/    # Messaging functions
│   │   ├── uploads/     # File upload functions
│   │   ├── feedback/    # Feedback and reporting functions
│   │   └── utils/       # Utility functions and types
│   ├── package.json
│   └── tsconfig.json
├── src/
│   ├── components/      # Reusable UI components
│   ├── contexts/        # React Context providers
│   ├── firebase/        # Firebase client SDK integration
│   ├── hooks/           # Custom React hooks
│   ├── pages/           # Application pages
│   ├── App.tsx          # Main application component
│   └── main.tsx         # Application entry point
├── public/              # Static assets
├── firebase.json        # Firebase configuration
├── firestore.rules      # Firestore security rules
└── storage.rules        # Storage security rules
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Firebase account
- Firebase CLI (`npm install -g firebase-tools`)

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   cd functions
   npm install
   ```

3. Configure Firebase:
   - Create a Firebase project in the Firebase Console
   - Enable Authentication, Firestore, Storage, and Functions
   - Update the Firebase configuration in `src/firebase/config.ts`

4. Deploy Firebase Functions and Rules:
   ```
   firebase deploy
   ```

5. Start the development server:
   ```
   npm run dev
   ```

## Firebase Backend

The Firebase backend includes:

### Authentication

- Email/password authentication with .edu email restriction
- Microsoft SSO integration for university accounts
- User profile management

### Firestore Database

- `/users/{uid}`: User profiles
- `/listings/{listingId}`: Marketplace listings
- `/chats/{chatId}/messages/{messageId}`: Chat messages
- `/feedback/{id}`: User feedback
- `/issues/{id}`: Issue reports

### Storage

- `/users/{uid}/profile/{fileName}`: User profile pictures
- `/listings/{uid}/{fileName}`: Listing images
- `/issues/{uid}/{fileName}`: Issue screenshots

### Cloud Functions

- Authentication triggers and user management
- Listing CRUD operations
- Messaging and chat management
- File upload handling
- Feedback and issue reporting

## Frontend Integration

The frontend uses custom hooks to interact with the Firebase backend:

- `useAuth`: Authentication state and user profile management
- `useListings`: Listing management (create, edit, delete, search)
- `useFileUpload`: File upload handling with progress tracking
- `useMessaging`: Real-time messaging between users
- `useFeedback`: Feedback submission and issue reporting

## Deployment

1. Build the application:
   ```
   npm run build
   ```

2. Deploy to Firebase Hosting:
   ```
   firebase deploy --only hosting
   ```

## Security

- Firestore and Storage security rules enforce access control
- All Firebase Functions validate authentication and authorization
- Input validation on both client and server
- File type and size restrictions for uploads

## License

This project is licensed under the MIT License - see the LICENSE file for details.# Hive Campus - Student Marketplace

Hive Campus is a student-exclusive marketplace app built with React, TypeScript, Firebase, and Tailwind CSS. It follows a liquid glass aesthetic with blur effects and modern UI design inspired by popular marketplace apps.

## Features

- **Authentication**: Email/password and Microsoft SSO (for .edu emails)
- **Listings**: Create, edit, delete, and browse listings
- **Messaging**: Real-time chat between users
- **Image Uploads**: Upload images for listings, profiles, and issue reports
- **Feedback and Reporting**: Submit feedback and report issues
- **Role-Based Access**: Student, merchant, and admin roles with appropriate permissions

## Tech Stack

- **Frontend**: React, TypeScript, Tailwind CSS
- **Backend**: Firebase (Authentication, Firestore, Storage, Functions)
- **State Management**: React Context API and custom hooks
- **Routing**: React Router v6
- **UI Components**: Custom components with Tailwind CSS
- **Icons**: Lucide React

## Project Structure

```
project/
├── functions/           # Firebase Cloud Functions
│   ├── src/
│   │   ├── auth/        # Authentication functions
│   │   ├── listings/    # Listing management functions
│   │   ├── messages/    # Messaging functions
│   │   ├── uploads/     # File upload functions
│   │   ├── feedback/    # Feedback and reporting functions
│   │   └── utils/       # Utility functions and types
│   ├── package.json
│   └── tsconfig.json
├── src/
│   ├── components/      # Reusable UI components
│   ├── contexts/        # React Context providers
│   ├── firebase/        # Firebase client SDK integration
│   ├── hooks/           # Custom React hooks
│   ├── pages/           # Application pages
│   ├── App.tsx          # Main application component
│   └── main.tsx         # Application entry point
├── public/              # Static assets
├── firebase.json        # Firebase configuration
├── firestore.rules      # Firestore security rules
└── storage.rules        # Storage security rules
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Firebase account
- Firebase CLI (`npm install -g firebase-tools`)

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   cd functions
   npm install
   ```

3. Configure Firebase:
   - Create a Firebase project in the Firebase Console
   - Enable Authentication, Firestore, Storage, and Functions
   - Update the Firebase configuration in `src/firebase/config.ts`

4. Deploy Firebase Functions and Rules:
   ```
   firebase deploy
   ```

5. Start the development server:
   ```
   npm run dev
   ```

## Firebase Backend

The Firebase backend includes:

### Authentication

- Email/password authentication with .edu email restriction
- Microsoft SSO integration for university accounts
- User profile management

### Firestore Database

- `/users/{uid}`: User profiles
- `/listings/{listingId}`: Marketplace listings
- `/chats/{chatId}/messages/{messageId}`: Chat messages
- `/feedback/{id}`: User feedback
- `/issues/{id}`: Issue reports

### Storage

- `/users/{uid}/profile/{fileName}`: User profile pictures
- `/listings/{uid}/{fileName}`: Listing images
- `/issues/{uid}/{fileName}`: Issue screenshots

### Cloud Functions

- Authentication triggers and user management
- Listing CRUD operations
- Messaging and chat management
- File upload handling
- Feedback and issue reporting

## Frontend Integration

The frontend uses custom hooks to interact with the Firebase backend:

- `useAuth`: Authentication state and user profile management
- `useListings`: Listing management (create, edit, delete, search)
- `useFileUpload`: File upload handling with progress tracking
- `useMessaging`: Real-time messaging between users
- `useFeedback`: Feedback submission and issue reporting

## Deployment

1. Build the application:
   ```
   npm run build
   ```

2. Deploy to Firebase Hosting:
   ```
   firebase deploy --only hosting
   ```

## Security

- Firestore and Storage security rules enforce access control
- All Firebase Functions validate authentication and authorization
- Input validation on both client and server
- File type and size restrictions for uploads

## License

This project is licensed under the MIT License - see the LICENSE file for details.