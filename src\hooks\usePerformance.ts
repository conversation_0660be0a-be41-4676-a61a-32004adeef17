import { useEffect, useCallback, useMemo, useRef, useState } from 'react';
import { debounce, throttle } from '../utils/performance';

// Hook for performance monitoring
export const usePerformance = (componentName: string) => {
  const renderCount = useRef(0);
  const startTime = useRef(performance.now());

  useEffect(() => {
    renderCount.current += 1;
    const renderTime = performance.now() - startTime.current;
    
    if (renderTime > 16) { // More than one frame
      console.warn(`${componentName} render took ${renderTime.toFixed(2)}ms`);
    }
    
    startTime.current = performance.now();
  });

  const logRenderInfo = useCallback(() => {
    console.log(`${componentName} has rendered ${renderCount.current} times`);
  }, [componentName]);

  return { renderCount: renderCount.current, logRenderInfo };
};

// Hook for debounced callbacks
export const useDebounce = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
) => {
  return useMemo(
    () => debounce(callback, delay),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [callback, delay, ...deps]
  );
};

// Hook for throttled callbacks
export const useThrottle = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  limit: number,
  deps: React.DependencyList = []
) => {
  return useMemo(
    () => throttle(callback, limit),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [callback, limit, ...deps]
  );
};

// Hook for intersection observer (lazy loading)
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const ref = useRef<HTMLElement>(null);
  const isIntersecting = useRef(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        isIntersecting.current = entry.isIntersecting;
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options,
      }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [options]);

  return { ref, isIntersecting: isIntersecting.current };
};

// Hook for virtual scrolling
export const useVirtualScroll = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) => {
  const scrollTop = useRef(0);
  const startIndex = Math.floor(scrollTop.current / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );

  const visibleItems = items.slice(startIndex, endIndex);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    scrollTop.current = e.currentTarget.scrollTop;
  }, []);

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
  };
};

// Hook for measuring component performance
export const useMeasureRender = (name: string) => {
  const startTime = useRef<number>();

  useEffect(() => {
    startTime.current = performance.now();
  });

  useEffect(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      if (duration > 16) {
        console.warn(`${name} render duration: ${duration.toFixed(2)}ms`);
      }
    }
  });
};

// Hook for optimized event handlers
export const useOptimizedEventHandler = <T extends Event>(
  handler: (event: T) => void,
  _options: { passive?: boolean; capture?: boolean } = {}
) => {
  const handlerRef = useRef(handler);
  handlerRef.current = handler;

  return useCallback(
    (event: T) => {
      handlerRef.current(event);
    },
    []
  );
};

// Hook for lazy component loading
export const useLazyComponent = <T>(
  importFunc: () => Promise<{ default: T }>,
  fallback: T
) => {
  const [component, setComponent] = useState<T>(fallback);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    importFunc()
      .then((module) => {
        setComponent(module.default);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  }, [importFunc]);

  return { component, loading };
};

// Hook for image preloading
export const useImagePreload = (src: string) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);

  useEffect(() => {
    const img = new Image();
    img.onload = () => setLoaded(true);
    img.onerror = () => setError(true);
    img.src = src;
  }, [src]);

  return { loaded, error };
};