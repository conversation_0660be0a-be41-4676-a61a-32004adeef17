import * as functions from 'firebase-functions';
import dotenv from 'dotenv';

export interface EnvironmentConfig {
  stripe: {
    apiKey: string;
    webhookSecret: string;
    autoReleaseEscrowDays: number;
  };
  app: {
    url: string;
    version: string;
  };
  openai: {
    apiKey: string;
  };
  slack: {
    webhookUrl: string;
  };
  shippo: {
    apiKey: string;
  };
  email: {
    host: string;
    port: number;
    user: string;
    password: string;
    from: string;
  };
  admin: {
    email: string;
  };
  sentry: {
    dsn: string;
  };
  environment: string;
}

// Get environment configuration
export const getEnvironmentConfig = (): EnvironmentConfig => {
  // Try to detect if we're in Firebase Functions environment
  const isFirebaseFunctions = typeof functions.config === 'function';
  const hasFirebaseConfig = isFirebaseFunctions && functions.config().stripe?.api_key;

  // Load dotenv for local development
  dotenv.config();

  if (hasFirebaseConfig) {
    // Production configuration using Firebase Functions config
    console.log('Using Firebase Functions config');
    return {
      stripe: {
        apiKey: functions.config().stripe?.api_key || '',
        webhookSecret: functions.config().stripe?.webhook_secret || '',
        autoReleaseEscrowDays: parseInt(functions.config().stripe?.auto_release_escrow_days || '7'),
      },
      app: {
        url: functions.config().app?.url || 'https://hivecampus.app',
        version: functions.config().app?.version || '1.0.0',
      },
      openai: {
        apiKey: functions.config().openai?.api_key || '',
      },
      slack: {
        webhookUrl: functions.config().slack?.webhook_url || '',
      },
      shippo: {
        apiKey: functions.config().shippo?.api_key || '',
      },
      email: {
        host: functions.config().email?.host || 'smtp.gmail.com',
        port: parseInt(functions.config().email?.port || '587'),
        user: functions.config().email?.user || '',
        password: functions.config().email?.password || '',
        from: functions.config().email?.from || '<EMAIL>',
      },
      environment: 'production',
      admin: {
        email: functions.config().admin?.email || '<EMAIL>',
      },
      sentry: {
        dsn: functions.config().sentry?.dsn || '',
      },
    };
  } else {
    // Development configuration using environment variables
    console.log('Using environment variables config');

    return {
      stripe: {
        apiKey: process.env.STRIPE_API_KEY || '',
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
        autoReleaseEscrowDays: parseInt(process.env.STRIPE_AUTO_RELEASE_ESCROW_DAYS || '7'),
      },
      app: {
        url: process.env.APP_URL || 'http://localhost:5173',
        version: process.env.APP_VERSION || '1.0.0-dev',
      },
      openai: {
        apiKey: process.env.OPENAI_API_KEY || '',
      },
      slack: {
        webhookUrl: process.env.SLACK_WEBHOOK_URL || '',
      },
      shippo: {
        apiKey: process.env.SHIPPO_API_KEY || '',
      },
      email: {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        user: process.env.EMAIL_USER || '',
        password: process.env.EMAIL_PASS || '',
        from: process.env.EMAIL_FROM || '<EMAIL>',
      },
      admin: {
        email: process.env.VITE_APP_OWNER_EMAIL || '<EMAIL>',
      },
      sentry: {
        dsn: process.env.SENTRY_DSN || '',
      },
      environment: 'development',
    };
  }
};

// Validate environment configuration
export const validateEnvironmentConfig = (config: EnvironmentConfig): void => {
  const errors: string[] = [];

  if (!config.stripe.apiKey) {
    errors.push('Stripe API key is required');
  }

  if (!config.stripe.webhookSecret) {
    errors.push('Stripe webhook secret is required');
  }

  if (!config.app.url) {
    errors.push('App URL is required');
  }

  if (errors.length > 0) {
    throw new Error(`Environment configuration errors: ${errors.join(', ')}`);
  }
};

// Lazy initialization to avoid timeout during deployment
let _environmentConfig: EnvironmentConfig | null = null;

export const getEnvironmentConfigSingleton = (): EnvironmentConfig => {
  if (!_environmentConfig) {
    _environmentConfig = getEnvironmentConfig();

    // Validate configuration
    try {
      validateEnvironmentConfig(_environmentConfig);
    } catch (error) {
      console.error('Environment configuration validation failed:', error);
      throw new Error(`Environment configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return _environmentConfig;
};

// Export the config - will be initialized on first access
export const environmentConfig = new Proxy({} as EnvironmentConfig, {
  get(_target, prop) {
    const config = getEnvironmentConfigSingleton();
    return config[prop as keyof EnvironmentConfig];
  }
});