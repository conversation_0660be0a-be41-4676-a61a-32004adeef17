import React, { useState, useRef, useEffect } from 'react';
import { 
  User, 
  Settings, 
  MessageSquare, 
  Phone, 
  Info, 
  AlertTriangle, 
  ChevronDown,
  LogOut,
  Shield,
  Store
} from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { logOut } from '../firebase/auth';

interface MerchantProfileDropdownProps {
  merchant: {
    name: string;
    email: string;
    avatar: string;
    businessName: string;
    verified: boolean;
    businessType: string;
  };
}

const MerchantProfileDropdown: React.FC<MerchantProfileDropdownProps> = ({ merchant }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const menuItems = [
    {
      icon: User,
      label: 'Profile',
      path: '/merchant/profile',
      description: 'Manage your business profile'
    },
    {
      icon: Phone,
      label: 'Contact Us',
      path: '/merchant/contact',
      description: 'Get in touch with our team'
    },
    {
      icon: Info,
      label: 'About Us',
      path: '/merchant/about',
      description: 'Learn more about Hive Campus'
    },
    {
      icon: MessageSquare,
      label: 'Feedback',
      path: '/merchant/feedback',
      description: 'Share your thoughts and suggestions'
    },
    {
      icon: AlertTriangle,
      label: 'Raise an Issue',
      path: '/merchant/report',
      description: 'Report a problem or concern'
    },
    {
      icon: Settings,
      label: 'Settings',
      path: '/merchant/settings',
      description: 'Manage your account preferences'
    }
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = async () => {
    try {
      await logOut();
      setIsOpen(false);
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Profile Button - Optimized for mobile */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 md:space-x-3 p-1.5 md:p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group"
      >
        <div className="relative">
          <img
            src={merchant.avatar}
            alt={merchant.name}
            className="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 group-hover:border-accent-300 dark:group-hover:border-accent-500 transition-colors"
          />
          {merchant.verified && (
            <div className="absolute -bottom-0.5 -right-0.5 md:-bottom-1 md:-right-1 w-3 h-3 md:w-4 md:h-4 bg-accent-500 rounded-full flex items-center justify-center">
              <Shield className="w-1.5 h-1.5 md:w-2.5 md:h-2.5 text-white" />
            </div>
          )}
          {/* Online Status */}
          <div className="absolute bottom-0 right-0 w-2.5 h-2.5 md:w-3 md:h-3 bg-success-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
        </div>
        
        {/* Merchant Info (Hidden on small mobile, visible on larger screens) */}
        <div className="hidden sm:block text-left">
          <p className="text-sm font-semibold text-gray-900 dark:text-white">
            {merchant.name}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 hidden md:block">
            {merchant.businessName}
          </p>
        </div>
        
        <ChevronDown className={`w-3 h-3 md:w-4 md:h-4 text-gray-400 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {/* Dropdown Menu - Mobile optimized */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-72 sm:w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-xl z-50 overflow-hidden animate-scale-in">
          {/* Merchant Header */}
          <div className="p-4 sm:p-6 bg-gradient-to-r from-accent-500 to-orange-500 text-white">
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="relative">
                <img
                  src={merchant.avatar}
                  alt={merchant.name}
                  className="w-12 h-12 sm:w-16 sm:h-16 rounded-full object-cover border-3 border-white/20"
                />
                {merchant.verified && (
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4 text-accent-500" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-base sm:text-lg font-bold truncate">{merchant.name}</h3>
                <p className="text-xs sm:text-sm text-orange-100 truncate">{merchant.email}</p>
                <p className="text-xs text-orange-200 mt-1 truncate">{merchant.businessName}</p>
                {merchant.verified && (
                  <div className="flex items-center space-x-1 mt-2">
                    <Store className="w-3 h-3" />
                    <span className="text-xs">Verified Partner</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2 max-h-64 sm:max-h-80 overflow-y-auto">
            {menuItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <Link
                  key={index}
                  to={item.path}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 sm:space-x-4 px-4 sm:px-6 py-3 sm:py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"
                >
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center group-hover:bg-accent-100 dark:group-hover:bg-accent-900/20 transition-colors flex-shrink-0">
                    <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 dark:text-gray-400 group-hover:text-accent-600 dark:group-hover:text-accent-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors">
                      {item.label}
                    </p>
                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">
                      {item.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>

          {/* Divider */}
          <div className="border-t border-gray-200 dark:border-gray-700"></div>

          {/* Sign Out */}
          <div className="p-2">
            <button
              onClick={handleSignOut}
              className="w-full flex items-center space-x-3 sm:space-x-4 px-4 sm:px-6 py-3 sm:py-4 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors group rounded-xl"
            >
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center group-hover:bg-red-200 dark:group-hover:bg-red-900/40 transition-colors flex-shrink-0">
                <LogOut className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 dark:text-red-400" />
              </div>
              <div className="flex-1 text-left">
                <p className="text-sm sm:text-base font-semibold text-red-600 dark:text-red-400">
                  Sign Out
                </p>
                <p className="text-xs sm:text-sm text-red-500 dark:text-red-500">
                  Sign out of your partner account
                </p>
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MerchantProfileDropdown;