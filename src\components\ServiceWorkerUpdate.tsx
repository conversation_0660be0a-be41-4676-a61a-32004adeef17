import { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';

interface ServiceWorkerUpdateProps {
  onUpdate: () => void;
}

const ServiceWorkerUpdate = ({ onUpdate }: ServiceWorkerUpdateProps) => {
  const [showReload, setShowReload] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);

  useEffect(() => {
    // Add event listener for new service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // The controllerchange event fires when the service worker controlling this page changes
        window.location.reload();
      });
    }

    // Listen for messages from the service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
        setShowReload(true);
        setWaitingWorker(event.data.sw);
      }
    });

    return () => {
      // Clean up event listeners
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('controllerchange', () => {
          window.location.reload();
        });
      }
    };
  }, []);

  const handleUpdate = () => {
    if (waitingWorker) {
      // Send a message to the waiting service worker, instructing it to activate
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
    }
    setShowReload(false);
    onUpdate();
  };

  if (!showReload) return null;

  return (
    <div className="fixed bottom-16 left-0 right-0 z-50 p-4 pointer-events-none">
      <div className="mx-auto max-w-md bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-4 border border-gray-200 pointer-events-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-primary-500">
              <RefreshCw size={20} />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Update available</h3>
              <div className="mt-1 text-xs text-gray-600">
                <p>A new version of Hive Campus is available.</p>
              </div>
            </div>
          </div>
          <button
            onClick={handleUpdate}
            className="ml-4 px-3 py-1.5 bg-[#f9a826] text-white text-xs font-medium rounded-lg hover:bg-[#e89417] transition-colors"
          >
            Update now
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceWorkerUpdate;import { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';

interface ServiceWorkerUpdateProps {
  onUpdate: () => void;
}

const ServiceWorkerUpdate = ({ onUpdate }: ServiceWorkerUpdateProps) => {
  const [showReload, setShowReload] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);

  useEffect(() => {
    // Add event listener for new service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // The controllerchange event fires when the service worker controlling this page changes
        window.location.reload();
      });
    }

    // Listen for messages from the service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
        setShowReload(true);
        setWaitingWorker(event.data.sw);
      }
    });

    return () => {
      // Clean up event listeners
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('controllerchange', () => {
          window.location.reload();
        });
      }
    };
  }, []);

  const handleUpdate = () => {
    if (waitingWorker) {
      // Send a message to the waiting service worker, instructing it to activate
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
    }
    setShowReload(false);
    onUpdate();
  };

  if (!showReload) return null;

  return (
    <div className="fixed bottom-16 left-0 right-0 z-50 p-4 pointer-events-none">
      <div className="mx-auto max-w-md bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-4 border border-gray-200 pointer-events-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-primary-500">
              <RefreshCw size={20} />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Update available</h3>
              <div className="mt-1 text-xs text-gray-600">
                <p>A new version of Hive Campus is available.</p>
              </div>
            </div>
          </div>
          <button
            onClick={handleUpdate}
            className="ml-4 px-3 py-1.5 bg-[#f9a826] text-white text-xs font-medium rounded-lg hover:bg-[#e89417] transition-colors"
          >
            Update now
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceWorkerUpdate;import { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';

interface ServiceWorkerUpdateProps {
  onUpdate: () => void;
}

const ServiceWorkerUpdate = ({ onUpdate }: ServiceWorkerUpdateProps) => {
  const [showReload, setShowReload] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);

  useEffect(() => {
    // Add event listener for new service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // The controllerchange event fires when the service worker controlling this page changes
        window.location.reload();
      });
    }

    // Listen for messages from the service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
        setShowReload(true);
        setWaitingWorker(event.data.sw);
      }
    });

    return () => {
      // Clean up event listeners
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('controllerchange', () => {
          window.location.reload();
        });
      }
    };
  }, []);

  const handleUpdate = () => {
    if (waitingWorker) {
      // Send a message to the waiting service worker, instructing it to activate
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
    }
    setShowReload(false);
    onUpdate();
  };

  if (!showReload) return null;

  return (
    <div className="fixed bottom-16 left-0 right-0 z-50 p-4 pointer-events-none">
      <div className="mx-auto max-w-md bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-4 border border-gray-200 pointer-events-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-primary-500">
              <RefreshCw size={20} />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Update available</h3>
              <div className="mt-1 text-xs text-gray-600">
                <p>A new version of Hive Campus is available.</p>
              </div>
            </div>
          </div>
          <button
            onClick={handleUpdate}
            className="ml-4 px-3 py-1.5 bg-[#f9a826] text-white text-xs font-medium rounded-lg hover:bg-[#e89417] transition-colors"
          >
            Update now
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceWorkerUpdate;import { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';

interface ServiceWorkerUpdateProps {
  onUpdate: () => void;
}

const ServiceWorkerUpdate = ({ onUpdate }: ServiceWorkerUpdateProps) => {
  const [showReload, setShowReload] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);

  useEffect(() => {
    // Add event listener for new service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // The controllerchange event fires when the service worker controlling this page changes
        window.location.reload();
      });
    }

    // Listen for messages from the service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
        setShowReload(true);
        setWaitingWorker(event.data.sw);
      }
    });

    return () => {
      // Clean up event listeners
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('controllerchange', () => {
          window.location.reload();
        });
      }
    };
  }, []);

  const handleUpdate = () => {
    if (waitingWorker) {
      // Send a message to the waiting service worker, instructing it to activate
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
    }
    setShowReload(false);
    onUpdate();
  };

  if (!showReload) return null;

  return (
    <div className="fixed bottom-16 left-0 right-0 z-50 p-4 pointer-events-none">
      <div className="mx-auto max-w-md bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-4 border border-gray-200 pointer-events-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-primary-500">
              <RefreshCw size={20} />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Update available</h3>
              <div className="mt-1 text-xs text-gray-600">
                <p>A new version of Hive Campus is available.</p>
              </div>
            </div>
          </div>
          <button
            onClick={handleUpdate}
            className="ml-4 px-3 py-1.5 bg-[#f9a826] text-white text-xs font-medium rounded-lg hover:bg-[#e89417] transition-colors"
          >
            Update now
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceWorkerUpdate;