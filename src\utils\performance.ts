import * as Sentry from '@sentry/react';

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();
  private observers: Set<PerformanceObserver> = new Set();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    if (typeof window !== 'undefined' && 'performance' in window) {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    // Observe Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as PerformanceEventTiming;
          if (lastEntry) {
            this.recordMetric('LCP', lastEntry.startTime);
            Sentry.addBreadcrumb({
              category: 'performance',
              message: `LCP: ${lastEntry.startTime.toFixed(2)}ms`,
              level: 'info',
            });
          }
        });
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
        this.observers.add(lcpObserver);
      } catch (_e) {
        console.warn('LCP observer not supported');
      }

      // Observe First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'first-input') {
              const fid = (entry as PerformanceEventTiming).processingStart - entry.startTime;
              this.recordMetric('FID', fid);
              Sentry.addBreadcrumb({
                category: 'performance',
                message: `FID: ${fid.toFixed(2)}ms`,
                level: 'info',
              });
            }
          });
        });
        fidObserver.observe({ type: 'first-input', buffered: true });
        this.observers.add(fidObserver);
      } catch (_e) {
        console.warn('FID observer not supported');
      }

      // Observe Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (!(entry as LayoutShiftEntry).hadRecentInput) {
              clsValue += (entry as LayoutShiftEntry).value;
              this.recordMetric('CLS', clsValue);
            }
          });
        });
        clsObserver.observe({ type: 'layout-shift', buffered: true });
        this.observers.add(clsObserver);
      } catch (_e) {
        console.warn('CLS observer not supported');
      }

      // Observe Long Tasks
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.duration > 50) {
              this.recordMetric('LONG_TASK', entry.duration);
              Sentry.addBreadcrumb({
                category: 'performance',
                message: `Long task: ${entry.duration.toFixed(2)}ms`,
                level: 'warning',
              });
            }
          });
        });
        longTaskObserver.observe({ type: 'longtask', buffered: true });
        this.observers.add(longTaskObserver);
      } catch (_e) {
        console.warn('Long task observer not supported');
      }
    }
  }

  recordMetric(name: string, value: number) {
    this.metrics.set(name, value);
    
    // Send to Sentry
    Sentry.setMeasurement(name, value, 'millisecond');
  }

  getMetric(name: string): number | undefined {
    return this.metrics.get(name);
  }

  getAllMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  // Measure custom operations
  measureAsync<T>(name: string, operation: () => Promise<T>): Promise<T> {
    const start = performance.now();
    return operation().finally(() => {
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
    });
  }

  measure<T>(name: string, operation: () => T): T {
    const start = performance.now();
    try {
      return operation();
    } finally {
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
    }
  }

  // Resource loading metrics
  getResourceMetrics() {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return {};
    }

    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    const metrics = {
      totalResources: resources.length,
      totalTransferSize: 0,
      totalDecodedBodySize: 0,
      slowResources: [] as string[],
    };

    resources.forEach((resource) => {
      metrics.totalTransferSize += resource.transferSize || 0;
      metrics.totalDecodedBodySize += resource.decodedBodySize || 0;
      
      if (resource.duration > 1000) {
        metrics.slowResources.push(resource.name);
      }
    });

    return metrics;
  }

  // Navigation timing
  getNavigationMetrics() {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return {};
    }

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (!navigation) return {};

    return {
      TTFB: navigation.responseStart - navigation.requestStart,
      DOMContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
      Load: navigation.loadEventEnd - navigation.navigationStart,
      DNSLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
      TCPConnect: navigation.connectEnd - navigation.connectStart,
    };
  }

  // Report all metrics to monitoring service
  reportMetrics() {
    const allMetrics = {
      ...this.getAllMetrics(),
      ...this.getNavigationMetrics(),
      ...this.getResourceMetrics(),
    };

    // Send to Sentry
    Object.entries(allMetrics).forEach(([key, value]) => {
      if (typeof value === 'number') {
        Sentry.setMeasurement(key, value, 'millisecond');
      }
    });

    return allMetrics;
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.metrics.clear();
  }
}

// Image loading optimization
export const optimizeImage = (src: string, options: {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
} = {}) => {
  const { width, height, quality: _quality = 80, format: _format = 'webp' } = options;
  
  // If using a CDN like Cloudinary or similar, construct optimized URL
  // For now, return original src but add loading optimizations
  const img = new Image();
  img.loading = 'lazy';
  img.decoding = 'async';
  
  if (width) img.width = width;
  if (height) img.height = height;
  
  return src;
};

// Lazy loading utility
export const lazyLoad = <T extends HTMLElement>(
  element: T,
  callback: () => void,
  options: IntersectionObserverInit = {}
) => {
  if (!('IntersectionObserver' in window)) {
    callback();
    return;
  }

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          callback();
          observer.unobserve(element);
        }
      });
    },
    {
      rootMargin: '50px',
      ...options,
    }
  );

  observer.observe(element);
  return observer;
};

// Debounce utility for performance
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number,
  immediate = false
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    
    timeout = setTimeout(() => {
      timeout = null;
      if (!immediate) func(...args);
    }, wait);
    
    if (callNow) func(...args);
  };
};

// Throttle utility for performance
export const throttle = <T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Memory usage monitoring
export const monitorMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as { memory: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
    };
  }
  return null;
};

// Initialize performance monitoring
export const initPerformanceMonitoring = () => {
  const monitor = PerformanceMonitor.getInstance();
  
  // Report metrics after page load
  window.addEventListener('load', () => {
    setTimeout(() => {
      monitor.reportMetrics();
    }, 1000);
  });

  // Report metrics when page becomes hidden (modern alternative to beforeunload)
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      monitor.reportMetrics();
    }
  });

  // Monitor memory usage periodically
  if (typeof window !== 'undefined') {
    setInterval(() => {
      const memoryInfo = monitorMemoryUsage();
      if (memoryInfo && memoryInfo.usagePercentage > 80) {
        Sentry.addBreadcrumb({
          category: 'performance',
          message: `High memory usage: ${memoryInfo.usagePercentage.toFixed(2)}%`,
          level: 'warning',
        });
      }
    }, 30000); // Check every 30 seconds
  }

  return monitor;
};

export default PerformanceMonitor;