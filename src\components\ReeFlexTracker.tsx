import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { logReeFlexEvent } from '../utils/reeflex';
import { Sentry } from '../utils/sentry';
import { getDeviceInfo } from '../utils/deviceInfo';

/**
 * ReeFlexTracker - Intelligent observability agent for Hive Campus
 * 
 * Passively monitors:
 * - Route changes
 * - Performance delays
 * - User interactions
 * - API responses
 * - Errors and crashes
 */
const ReeFlexTracker: React.FC = () => {
  const location = useLocation();
  const previousPath = useRef<string | null>(null);
  const navigationStartTime = useRef<number | null>(null);
  const clickStartTimes = useRef<Map<string, number>>(new Map());
  
  // Track route changes
  useEffect(() => {
    const handleRouteChange = async () => {
      const currentPath = location.pathname;
      
      // Skip if it's the initial load
      if (previousPath.current !== null) {
        // Calculate navigation time if we have a start time
        let navigationTime: number | undefined;
        if (navigationStartTime.current) {
          navigationTime = performance.now() - navigationStartTime.current;
          
          // Log slow navigations (> 2000ms)
          if (navigationTime > 2000) {
            logReeFlexEvent('slow_navigation', {
              from: previousPath.current,
              to: currentPath,
              duration_ms: Math.round(navigationTime),
              device_info: getDeviceInfo()
            });
          }
        }
        
        // Log page view
        logReeFlexEvent('page_viewed', {
          path: currentPath,
          previous_path: previousPath.current,
          navigation_time_ms: navigationTime ? Math.round(navigationTime) : undefined
        });
      }
      
      // Update previous path for next navigation
      previousPath.current = currentPath;
      navigationStartTime.current = null;
    };
    
    handleRouteChange();
  }, [location.pathname]);
  
  // Track performance metrics
  useEffect(() => {
    // Observer for long tasks
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        // Only log tasks that take more than 200ms
        if (entry.duration > 200) {
          logReeFlexEvent('long_task', {
            duration_ms: Math.round(entry.duration),
            path: location.pathname,
            task_name: entry.name || 'unknown',
            device_info: getDeviceInfo()
          });
        }
      }
    });
    
    // Start observing long tasks
    try {
      observer.observe({ entryTypes: ['longtask'] });
    } catch {
      console.warn('LongTask observer not supported in this browser');
    }
    
    // Track resource loading
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const resourceEntry = entry as PerformanceResourceTiming;
        
        // Only log slow resources (> 2000ms)
        if (resourceEntry.duration > 2000) {
          logReeFlexEvent('slow_resource', {
            resource_url: resourceEntry.name,
            duration_ms: Math.round(resourceEntry.duration),
            resource_type: resourceEntry.initiatorType,
            path: location.pathname
          });
        }
      }
    });
    
    // Start observing resource timing
    try {
      resourceObserver.observe({ entryTypes: ['resource'] });
    } catch {
      console.warn('Resource timing observer not supported in this browser');
    }
    
    return () => {
      observer.disconnect();
      resourceObserver.disconnect();
    };
  }, [location.pathname]);
  
  // Track user interactions
  useEffect(() => {
    // Track clicks on buttons and links
    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const button = target.closest('button, a, [role="button"]');
      
      if (button) {
        const buttonId = button.id || '';
        const buttonText = button.textContent?.trim() || '';
        // const buttonType = button.getAttribute('type') || '';
        const href = button.getAttribute('href') || '';
        const role = button.getAttribute('role') || button.tagName.toLowerCase();
        
        // Store start time for this interaction
        const interactionId = `${role}-${buttonId || buttonText}-${Date.now()}`;
        clickStartTimes.current.set(interactionId, performance.now());
        
        // If it's a navigation link, store the start time
        if (href && href.startsWith('/')) {
          navigationStartTime.current = performance.now();
        }
        
        // Log the click event
        logReeFlexEvent('user_interaction', {
          interaction_type: 'click',
          element_type: button.tagName.toLowerCase(),
          element_id: buttonId,
          element_text: buttonText.substring(0, 50), // Limit text length
          path: location.pathname,
          interaction_id: interactionId
        });
      }
    };
    
    // Track form submissions
    const handleSubmit = (event: SubmitEvent) => {
      const form = event.target as HTMLFormElement;
      const formId = form.id || '';
      const formAction = form.action || '';
      const formMethod = form.method || 'get';
      
      // Log form submission
      logReeFlexEvent('form_submitted', {
        form_id: formId,
        form_action: formAction,
        form_method: formMethod,
        path: location.pathname,
        field_count: form.elements.length
      });
    };
    
    // Add event listeners
    document.addEventListener('click', handleClick);
    document.addEventListener('submit', handleSubmit);
    
    return () => {
      document.removeEventListener('click', handleClick);
      document.removeEventListener('submit', handleSubmit);
    };
  }, [location.pathname]);
  
  // Track API calls and errors
  useEffect(() => {
    // Store original fetch
    const originalFetch = window.fetch;
    
    // Override fetch to track API calls
    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
      const startTime = performance.now();
      const url = typeof input === 'string' ? input : input.url;
      
      try {
        const response = await originalFetch(input, init);
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Log slow API calls (> 2000ms)
        if (duration > 2000) {
          logReeFlexEvent('api_slow', {
            url: url,
            method: init?.method || 'GET',
            duration_ms: Math.round(duration),
            status: response.status,
            path: location.pathname
          });
        }
        
        // Log API failures
        if (!response.ok) {
          logReeFlexEvent('api_failed', {
            url: url,
            method: init?.method || 'GET',
            status: response.status,
            path: location.pathname,
            duration_ms: Math.round(duration)
          });
        }
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        
        // Log network errors
        logReeFlexEvent('api_error', {
          url: url,
          method: init?.method || 'GET',
          error: error instanceof Error ? error.message : String(error),
          path: location.pathname,
          duration_ms: Math.round(endTime - startTime)
        });
        
        throw error;
      }
    };
    
    // Track global errors
    const handleError = (event: ErrorEvent) => {
      logReeFlexEvent('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        path: location.pathname,
        sentry_event_id: Sentry.lastEventId()
      });
    };
    
    // Track unhandled promise rejections
    const handleRejection = (event: PromiseRejectionEvent) => {
      logReeFlexEvent('promise_rejection', {
        reason: event.reason instanceof Error ? event.reason.message : String(event.reason),
        path: location.pathname,
        sentry_event_id: Sentry.lastEventId()
      });
    };
    
    // Add global error handlers
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleRejection);
    
    return () => {
      // Restore original fetch
      window.fetch = originalFetch;
      
      // Remove global error handlers
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  }, [location.pathname]);
  
  // Track checkout flow steps
  useEffect(() => {
    if (location.pathname.includes('/checkout/')) {
      logReeFlexEvent('checkout_started', {
        path: location.pathname,
        listing_id: location.pathname.split('/').pop()
      });
    } else if (location.pathname === '/order/success') {
      logReeFlexEvent('checkout_completed', {
        path: location.pathname
      });
    }
  }, [location.pathname]);
  
  // This component doesn't render anything
  return null;
};

export default ReeFlexTracker;