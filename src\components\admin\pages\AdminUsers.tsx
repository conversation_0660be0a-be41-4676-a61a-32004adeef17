import React, { useState, useEffect } from 'react';
import {
  Users,
  Search,
  Filter,
  Eye,
  Ban,
  CheckCircle,
  Mail,
  Calendar,
  GraduationCap,
  AlertTriangle,
  Shield,
  Wallet,
  DollarSign,
  Plus
} from 'lucide-react';
import { AdminDataService } from '../../../services/AdminDataService';
import { logAdminAction } from '../../../utils/adminAuth';
import { useAuth } from '../../../hooks/useAuth';
import { Timestamp } from 'firebase/firestore';
import { User } from '../../../firebase/types';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../../../firebase/config';

const AdminUsers: React.FC = () => {
  const { userProfile } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states for replacing prompt()
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [modalConfig, setModalConfig] = useState<{
    title: string;
    placeholder: string;
    action: string;
    userId: string;
  } | null>(null);
  const [reasonInput, setReasonInput] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [selectedUniversity, setSelectedUniversity] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Wallet management state
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [walletData, setWalletData] = useState<any>(null);
  const [grantAmount, setGrantAmount] = useState('');
  const [grantDescription, setGrantDescription] = useState('');
  const [isGrantingCredit, setIsGrantingCredit] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, [selectedRole, selectedUniversity]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: any = {};
      if (selectedRole) filters.role = selectedRole;
      if (selectedUniversity) filters.university = selectedUniversity;

      const userData = await AdminDataService.getUsers({ pageSize: 100 }, filters);
      setUsers(userData.users);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (userId: string, action: string) => {
    try {
      if (userProfile) {
        await logAdminAction(userProfile, `user_${action}`, { userId });
      }

      switch (action) {
        case 'view':
          // Navigate to user profile view
          window.open(`/profile/${userId}`, '_blank');
          return; // Don't refresh for view action
        case 'block': {
          setModalConfig({
            title: 'Block User',
            placeholder: 'Enter reason for blocking this user...',
            action: 'block',
            userId
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        }
        case 'unblock':
          await AdminDataService.updateUser(userId, {
            status: 'active',
            blockedAt: undefined,
            blockedBy: undefined,
            blockReason: undefined
          });
          break;
        case 'warn': {
          setModalConfig({
            title: 'Send Warning',
            placeholder: 'Enter warning message for this user...',
            action: 'warn',
            userId
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        }
        case 'verify':
          await AdminDataService.updateUser(userId, {
            emailVerified: true
          });
          break;
        case 'ban': {
          setModalConfig({
            title: 'Suspend User',
            placeholder: 'Enter reason for suspending this user...',
            action: 'ban',
            userId
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        }
        case 'unban':
          await AdminDataService.updateUser(userId, {
            status: 'active',
            blockedAt: undefined,
            blockedBy: undefined,
            blockReason: undefined
          });
          break;
      }

      // Refresh users list
      await fetchUsers();
    } catch (err) {
      console.error(`Error performing ${action} on user:`, err);
    }
  };

  const handleModalSubmit = async () => {
    if (!modalConfig || !reasonInput.trim()) return;

    try {
      const { action, userId } = modalConfig;

      switch (action) {
        case 'block':
          await AdminDataService.updateUser(userId, {
            status: 'blocked',
            blockedAt: Timestamp.now(),
            blockedBy: userProfile?.uid,
            blockReason: reasonInput.trim()
          });
          break;
        case 'warn':
          await AdminDataService.sendAdminMessage(userId, userProfile?.uid || '', reasonInput.trim(), 'warning');
          break;
        case 'ban':
          await AdminDataService.updateUser(userId, {
            status: 'suspended',
            blockedAt: Timestamp.now(),
            blockedBy: userProfile?.uid,
            blockReason: reasonInput.trim()
          });
          break;
      }

      // Close modal and refresh users
      setShowReasonModal(false);
      setModalConfig(null);
      setReasonInput('');
      await fetchUsers();
    } catch (err) {
      console.error(`Error performing ${modalConfig.action} on user:`, err);
    }
  };

  const handleModalCancel = () => {
    setShowReasonModal(false);
    setModalConfig(null);
    setReasonInput('');
  };

  // Wallet management functions
  const handleViewWallet = async (user: User) => {
    try {
      setSelectedUser(user);
      setShowWalletModal(true);

      // Fetch wallet data for the user
      // const getWalletData = httpsCallable(functions, 'getWalletData');
      // Note: This would need to be modified to allow admin access to any user's wallet
      // For now, we'll show a placeholder
      setWalletData({
        balance: 0,
        referralCode: 'Loading...',
        history: []
      });
    } catch (error) {
      console.error('Error fetching wallet data:', error);
      setError('Failed to load wallet data');
    }
  };

  const handleGrantCredit = async () => {
    if (!selectedUser || !grantAmount || isGrantingCredit) return;

    try {
      setIsGrantingCredit(true);
      const amount = parseFloat(grantAmount);

      if (isNaN(amount) || amount <= 0) {
        setError('Please enter a valid amount');
        return;
      }

      const grantWalletCredit = httpsCallable(functions, 'grantWalletCredit');
      await grantWalletCredit({
        userId: selectedUser.uid,
        amount,
        description: grantDescription || `Admin credit grant`
      });

      // Log admin action
      if (userProfile) {
        await logAdminAction(userProfile, 'wallet_credit_grant', {
          userId: selectedUser.uid,
          amount,
          description: grantDescription
        });
      }

      // Reset form
      setGrantAmount('');
      setGrantDescription('');
      setShowWalletModal(false);

      // Show success message
      alert(`Successfully granted $${amount} to ${selectedUser.name}`);
    } catch (error) {
      console.error('Error granting wallet credit:', error);
      setError('Failed to grant wallet credit');
    } finally {
      setIsGrantingCredit(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.university?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = !selectedRole || user.role === selectedRole;
    const matchesUniversity = !selectedUniversity || user.university === selectedUniversity;

    return matchesSearch && matchesRole && matchesUniversity;
  });

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      case 'merchant': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'student': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'banned': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading users...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Users
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">User Management</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Manage user accounts, profiles, and permissions ({users.length} users)
          </p>
        </div>
        <button
          onClick={fetchUsers}
          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <Users className="h-4 w-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                id="admin-users-search"
                name="userSearch"
                type="text"
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filter Options */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Role
                </label>
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Roles</option>
                  <option value="student">Student</option>
                  <option value="merchant">Merchant</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  University
                </label>
                <input
                  type="text"
                  placeholder="Filter by university..."
                  value={selectedUniversity}
                  onChange={(e) => setSelectedUniversity(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Users Table */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {filteredUsers.length === 0 ? (
          <div className="px-4 py-12">
            <div className="text-center">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No users found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {searchTerm ? 'Try adjusting your search terms.' : 'No users match the current filters.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    University
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredUsers.map((user) => (
                  <tr key={user.uid} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded-full bg-gray-300"
                            src={user.profilePictureURL || '/placeholder-avatar.svg'}
                            alt={user.name}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder-avatar.svg';
                            }}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {user.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        <GraduationCap className="h-4 w-4 mr-2 text-gray-400" />
                        {user.university}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Calendar className="h-4 w-4 mr-2" />
                        {formatDate(user.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        {/* View Profile */}
                        <button
                          onClick={() => handleUserAction(user.uid, 'view')}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          title="View Profile"
                        >
                          <Eye className="h-4 w-4" />
                        </button>

                        {/* Wallet Management */}
                        <button
                          onClick={() => handleViewWallet(user)}
                          className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                          title="Manage Wallet"
                        >
                          <Wallet className="h-4 w-4" />
                        </button>

                        {/* Manual Email Verification */}
                        {!user.emailVerified && (
                          <button
                            onClick={() => handleUserAction(user.uid, 'verify')}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            title="Manually Verify Email"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                        )}

                        {/* Issue Warning */}
                        <button
                          onClick={() => handleUserAction(user.uid, 'warn')}
                          className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                          title="Issue Warning"
                        >
                          <AlertTriangle className="h-4 w-4" />
                        </button>

                        {/* Block/Unblock User */}
                        {user.status !== 'blocked' && user.uid !== userProfile?.uid ? (
                          <button
                            onClick={() => handleUserAction(user.uid, 'block')}
                            className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                            title="Block User"
                          >
                            <Shield className="h-4 w-4" />
                          </button>
                        ) : user.status === 'blocked' ? (
                          <button
                            onClick={() => handleUserAction(user.uid, 'unblock')}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            title="Unblock User"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                        ) : null}

                        {/* Suspend/Unsuspend User */}
                        {user.status !== 'suspended' && user.uid !== userProfile?.uid ? (
                          <button
                            onClick={() => handleUserAction(user.uid, 'ban')}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            title="Suspend User"
                          >
                            <Ban className="h-4 w-4" />
                          </button>
                        ) : user.status === 'suspended' ? (
                          <button
                            onClick={() => handleUserAction(user.uid, 'unban')}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            title="Unban User"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                        ) : null}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Wallet Management Modal */}
      {showWalletModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Wallet Management - {selectedUser.name}
              </h3>
              <button
                onClick={() => setShowWalletModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>

            {/* Current Wallet Info */}
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Wallet className="w-5 h-5 text-purple-500" />
                <span className="font-medium text-gray-900 dark:text-white">Current Balance</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                ${walletData?.balance?.toFixed(2) || '0.00'}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Referral Code: {walletData?.referralCode || 'Loading...'}
              </p>
            </div>

            {/* Grant Credit Form */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Grant Amount ($)
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={grantAmount}
                    onChange={(e) => setGrantAmount(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description (Optional)
                </label>
                <input
                  type="text"
                  value={grantDescription}
                  onChange={(e) => setGrantDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Reason for credit grant"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => setShowWalletModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleGrantCredit}
                  disabled={!grantAmount || isGrantingCredit}
                  className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                >
                  {isGrantingCredit ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>Granting...</span>
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4" />
                      <span>Grant Credit</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reason Input Modal */}
      {showReasonModal && modalConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {modalConfig.title}
            </h3>

            <div className="mb-4">
              <label htmlFor="reason-input" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Reason
              </label>
              <textarea
                id="reason-input"
                name="reason"
                value={reasonInput}
                onChange={(e) => setReasonInput(e.target.value)}
                placeholder={modalConfig.placeholder}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                rows={3}
                required
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleModalCancel}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleModalSubmit}
                disabled={!reasonInput.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminUsers;
