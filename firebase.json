{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}], "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "redirects": [{"source": "/http-redirect", "destination": "https://hivecampus.app", "type": 301}], "headers": [{"source": "/service-worker.js", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}, {"source": "**/*.@(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(self), payment=*"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' https://www.gstatic.com https://www.googleapis.com https://apis.google.com https://www.googletagmanager.com https://js.stripe.com https://api.reeflex.ai https://browser.sentry-cdn.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https:; media-src 'self' blob:; connect-src 'self' https://*.googleapis.com https://*.firebase.googleapis.com https://*.firebaseio.com https://*.cloudfunctions.net https://firestore.googleapis.com https://api.stripe.com https://connect.stripe.com https://api.reeflex.ai https://sentry.io https://*.sentry.io https://www.google-analytics.com https://www.googletagmanager.com wss://*.firebaseio.com; frame-src 'self' https://js.stripe.com https://checkout.stripe.com https://connect.stripe.com https://*.stripe.com https://*.firebaseapp.com https://*.google.com https://accounts.google.com; object-src 'none'; base-uri 'self'; form-action 'self' https://checkout.stripe.com https://connect.stripe.com; upgrade-insecure-requests"}]}], "predeploy": ["npm run build"]}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true}}}