import React, { useState, useEffect } from 'react';
import { Upload, DollarSign, Loader, X, AlertCircle, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { useListings, useFileUpload } from '../hooks';
import { useStripeConnect } from '../hooks/useStripeConnect';
import { useSellerAddressManagement } from '../hooks/useSellerAddressManagement';
import { ListingCondition, ListingType } from '../firebase/types';
import { captureTypedEvent, SentryEventType, startPerformanceTracking, captureException } from '../utils/sentry';
import OnboardingStatusBadge from '../components/OnboardingStatusBadge';
import StripeOnboardingModal from '../components/StripeOnboardingModal';
import { isRiskyMessage, getRiskType, logViolation } from '../utils/detectRisk';
import { roundPrice } from '../utils/priceUtils';

const AddListing: React.FC = () => {
  const navigate = useNavigate();
  const { addListing } = useListings();
  const { uploadMultipleFiles, isUploading, uploadProgress } = useFileUpload();
  const { accountStatus, totalPendingAmount, pendingOrderCount, loadInitialData } = useStripeConnect({ autoLoad: false });
  const { addresses: sellerAddresses, getDefaultAddress, addAddress: addSellerAddress } = useSellerAddressManagement();
  
  const [activeTab, setActiveTab] = useState<ListingType>('sell');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    category: '',
    condition: '' as ListingCondition,
    images: [] as File[],
    visibility: 'university' as 'university' | 'public',
    // Delivery method fields
    deliveryMethod: 'in_person' as 'in_person' | 'mail',
    shippingModel: 'shippo' as 'shippo' | 'manual',
    shippingPaidBy: 'buyer' as 'buyer' | 'seller',
    packageSize: 'medium' as 'small' | 'medium' | 'large',
    allowBuyerChoice: false,
    // Enhanced package details for Shippo
    useCustomPackage: false,
    packagePreset: '' as string,
    packageLength: '',
    packageWidth: '',
    packageHeight: '',
    packageWeight: '',
    packageWeightUnit: 'oz' as 'oz' | 'lb',
    packageDimensionUnit: 'in' as 'in' | 'cm',
    // Seller address fields
    sellerName: '',
    sellerStreet1: '',
    sellerStreet2: '',
    sellerCity: '',
    sellerState: '',
    sellerZip: '',
    sellerCountry: 'US',
    sellerPhone: '',
    // Rent-specific fields
    rentalPeriod: 'weekly' as 'weekly' | 'monthly',
    weeklyPrice: '',
    monthlyPrice: '',
    startDate: '',
    endDate: '',
    // Auction-specific fields
    auctionStartDate: '',
    auctionStartTime: '',
    auctionEndDate: '',
    auctionEndTime: '',
    auctionDuration: '7' as '1' | '3' | '7' | '14',
    startingBid: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showOnboardingModal, setShowOnboardingModal] = useState(false);

  // Load default seller address when Hive Shipping is selected
  useEffect(() => {
    if (formData.deliveryMethod === 'mail' && formData.shippingModel === 'shippo') {
      const defaultAddress = getDefaultAddress();
      if (defaultAddress && !formData.sellerName) {
        // Only populate if fields are empty to avoid overwriting user input
        setFormData(prev => ({
          ...prev,
          sellerName: defaultAddress.name,
          sellerStreet1: defaultAddress.street1,
          sellerStreet2: defaultAddress.street2 || '',
          sellerCity: defaultAddress.city,
          sellerState: defaultAddress.state,
          sellerZip: defaultAddress.zip,
          sellerCountry: defaultAddress.country,
          sellerPhone: defaultAddress.phone || ''
        }));
      }
    }
  }, [formData.deliveryMethod, formData.shippingModel, getDefaultAddress, formData.sellerName]);

  // Load Stripe Connect data when component mounts
  React.useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const tabs = [
    { id: 'sell' as ListingType, label: 'Sell', icon: '💰' },
    { id: 'rent' as ListingType, label: 'Rent', icon: '📅' },
    { id: 'auction' as ListingType, label: 'Auction', icon: '⚡' }
  ];

  const categories = [
    'Electronics', 'Textbooks', 'Clothing', 'Furniture', 'Sports', 'Accessories'
  ];

  // Map UI-friendly condition names to backend values
  const conditionMap: Record<string, ListingCondition> = {
    'Brand New': 'new',
    'Like New': 'like_new',
    'Very Good': 'very_good',
    'Good': 'good',
    'Fair': 'fair',
    'Poor': 'poor'
  };

  const conditions = Object.keys(conditionMap);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      
      // Limit to 8 images total
      const totalImages = formData.images.length + files.length;
      if (totalImages > 8) {
        alert('You can only upload up to 8 images');
        return;
      }
      
      setFormData({ ...formData, images: [...formData.images, ...files] });
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...formData.images];
    newImages.splice(index, 1);
    setFormData({ ...formData, images: newImages });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError(null);
    
    // Start performance tracking for listing creation
    startPerformanceTracking('listing_creation');
    
    try {
      // Validate form
      if (!formData.title || !formData.description || !formData.price ||
          !formData.category || !formData.condition) {
        throw new Error('Please fill out all required fields');
      }

      // Validate enhanced shipping fields for Hive Shipping
      if (formData.deliveryMethod === 'mail' && formData.shippingModel === 'shippo') {
        // Validate package details
        if (formData.useCustomPackage) {
          if (!formData.packageLength || !formData.packageWidth || !formData.packageHeight || !formData.packageWeight) {
            toast.error('Please fill in all package dimensions and weight for custom packages.');
            return;
          }

          const length = parseFloat(formData.packageLength);
          const width = parseFloat(formData.packageWidth);
          const height = parseFloat(formData.packageHeight);
          const weight = parseFloat(formData.packageWeight);

          if (length <= 0 || width <= 0 || height <= 0 || weight <= 0) {
            toast.error('Package dimensions and weight must be greater than 0.');
            return;
          }
        } else {
          if (!formData.packagePreset) {
            toast.error('Please select a package preset or use custom dimensions.');
            return;
          }
        }

        // Validate seller address
        if (!formData.sellerName || !formData.sellerStreet1 || !formData.sellerCity || !formData.sellerState || !formData.sellerZip) {
          toast.error('Please fill in all required seller address fields for shipping.');
          return;
        }

        // Validate ZIP code format (basic US ZIP validation)
        const zipRegex = /^\d{5}(-\d{4})?$/;
        if (!zipRegex.test(formData.sellerZip)) {
          toast.error('Please enter a valid ZIP code (e.g., 12345 or 12345-6789).');
          return;
        }
      }

      // Check for risky content in title and description
      if (isRiskyMessage(formData.title) || isRiskyMessage(formData.description)) {
        const titleRisk = isRiskyMessage(formData.title) ? getRiskType(formData.title) : null;
        const descriptionRisk = isRiskyMessage(formData.description) ? getRiskType(formData.description) : null;
        const riskType = titleRisk || descriptionRisk;

        const errorMessage = '⚠️ Personal payment details and contact info are not allowed in listings.';
        toast.error(errorMessage);

        // Log the violation
        await logViolation('listing',
          `Title: ${formData.title}\nDescription: ${formData.description}`,
          riskType,
          { listingTitle: formData.title }
        );

        return; // Don't create the listing
      }
      
      // Track listing creation started
      captureTypedEvent(SentryEventType.FEATURE_USED, {
        feature: 'listing_creation_started',
        listing_type: activeTab,
        category: formData.category,
        condition: formData.condition,
        has_images: formData.images.length > 0,
        image_count: formData.images.length
      });
      
      // Upload images first if there are any
      let uploadedImageUrls: string[] = [];
      
      if (formData.images.length > 0) {
        const uploadResults = await uploadMultipleFiles(formData.images, 'listing');
        uploadedImageUrls = uploadResults.map(result => result.downloadURL);
        
        // Track image upload success
        captureTypedEvent(SentryEventType.FEATURE_USED, {
          feature: 'listing_images_uploaded',
          image_count: uploadedImageUrls.length,
          success: true
        });
      }
      
      // Prepare additional data based on listing type
      let additionalData: any = {
        // Always include delivery method data
        deliveryMethod: formData.deliveryMethod
      };

      // Only add shipping options for mail delivery
      if (formData.deliveryMethod === 'mail') {
        const shippingOptions: any = {
          model: formData.shippingModel,
          paidBy: formData.shippingPaidBy,
          allowBuyerChoice: formData.allowBuyerChoice
        };

        // Add enhanced package details for Shippo
        if (formData.shippingModel === 'shippo') {
          if (formData.useCustomPackage) {
            // Custom package dimensions
            shippingOptions.packageDetails = {
              dimensions: {
                length: parseFloat(formData.packageLength),
                width: parseFloat(formData.packageWidth),
                height: parseFloat(formData.packageHeight),
                unit: formData.packageDimensionUnit
              },
              weight: {
                value: parseFloat(formData.packageWeight),
                unit: formData.packageWeightUnit
              },
              presetUsed: null
            };
          } else {
            // Preset package
            shippingOptions.packageDetails = {
              presetUsed: formData.packagePreset
            };
            // Keep legacy packageSize for backward compatibility
            shippingOptions.packageSize = formData.packageSize;
          }

          // Add seller address
          if (formData.sellerName && formData.sellerStreet1 && formData.sellerCity && formData.sellerState && formData.sellerZip) {
            shippingOptions.sellerAddress = {
              name: formData.sellerName.trim(),
              street1: formData.sellerStreet1.trim(),
              street2: formData.sellerStreet2?.trim() || undefined,
              city: formData.sellerCity.trim(),
              state: formData.sellerState.trim(),
              zip: formData.sellerZip.trim(),
              country: formData.sellerCountry,
              phone: formData.sellerPhone?.trim() || undefined
            };
          }
        }

        additionalData.shippingOptions = shippingOptions;
      }

      if (activeTab === 'rent') {
        additionalData = {
          ...additionalData,
          rentalPeriod: formData.rentalPeriod,
          weeklyPrice: formData.weeklyPrice ? roundPrice(parseFloat(formData.weeklyPrice)) : undefined,
          monthlyPrice: formData.monthlyPrice ? roundPrice(parseFloat(formData.monthlyPrice)) : undefined,
          startDate: formData.startDate || undefined,
          endDate: formData.endDate || undefined
        };
      } else if (activeTab === 'auction') {
        additionalData = {
          ...additionalData,
          startingBid: formData.startingBid ? roundPrice(parseFloat(formData.startingBid)) : undefined,
          auctionStartDate: formData.auctionStartDate || undefined,
          auctionStartTime: formData.auctionStartTime || undefined,
          auctionEndDate: formData.auctionEndDate || undefined,
          auctionEndTime: formData.auctionEndTime || undefined,
          auctionDuration: formData.auctionDuration
        };
      }

      // Create the listing with properly rounded price
      const result = await addListing(
        formData.title,
        formData.description,
        roundPrice(parseFloat(formData.price)),
        formData.category,
        formData.condition,
        activeTab,
        uploadedImageUrls,
        formData.visibility,
        additionalData
      );

      if (result && result.success && result.data) {
        console.log('Listing creation result:', result);
        console.log('Listing ID:', result.data.id);
        setSubmitSuccess(true);

        // Track successful listing creation
        captureTypedEvent(SentryEventType.LISTING_CREATED, {
          listing_id: result.data.id,
          title: formData.title,
          price: roundPrice(parseFloat(formData.price)),
          category: formData.category,
          condition: formData.condition,
          listing_type: activeTab,
          visibility: formData.visibility,
          image_count: uploadedImageUrls.length
        });

        // Save seller address if it's a new one for Hive Shipping
        if (formData.deliveryMethod === 'mail' && formData.shippingModel === 'shippo' && formData.sellerName) {
          try {
            // Check if this address already exists
            const existingAddress = sellerAddresses.find(addr =>
              addr.name === formData.sellerName &&
              addr.street1 === formData.sellerStreet1 &&
              addr.city === formData.sellerCity &&
              addr.state === formData.sellerState &&
              addr.zip === formData.sellerZip
            );

            if (!existingAddress) {
              await addSellerAddress({
                name: formData.sellerName,
                street1: formData.sellerStreet1,
                street2: formData.sellerStreet2 || undefined,
                city: formData.sellerCity,
                state: formData.sellerState,
                zip: formData.sellerZip,
                country: formData.sellerCountry,
                phone: formData.sellerPhone || undefined,
                isDefault: sellerAddresses.length === 0 // Make it default if it's the first address
              });
              console.log('Seller address saved successfully');
            }
          } catch (error) {
            console.error('Failed to save seller address:', error);
            // Don't block the listing creation if address saving fails
          }
        }

        // Reset form
        setFormData({
          title: '',
          description: '',
          price: '',
          category: '',
          condition: '' as ListingCondition,
          images: [],
          visibility: 'university' as 'university' | 'public',
          // Delivery method fields
          deliveryMethod: 'in_person' as 'in_person' | 'mail',
          shippingModel: 'shippo' as 'shippo' | 'manual',
          shippingPaidBy: 'buyer' as 'buyer' | 'seller',
          packageSize: 'medium' as 'small' | 'medium' | 'large',
          allowBuyerChoice: false,
          // Enhanced package details for Shippo
          useCustomPackage: false,
          packagePreset: '',
          packageLength: '',
          packageWidth: '',
          packageHeight: '',
          packageWeight: '',
          packageWeightUnit: 'oz' as 'oz' | 'lb',
          packageDimensionUnit: 'in' as 'in' | 'cm',
          // Seller address fields
          sellerName: '',
          sellerStreet1: '',
          sellerStreet2: '',
          sellerCity: '',
          sellerState: '',
          sellerZip: '',
          sellerCountry: 'US',
          sellerPhone: '',
          rentalPeriod: 'weekly' as 'weekly' | 'monthly',
          weeklyPrice: '',
          monthlyPrice: '',
          startDate: '',
          endDate: '',
          auctionStartDate: '',
          auctionStartTime: '',
          auctionEndDate: '',
          auctionEndTime: '',
          auctionDuration: '7' as '1' | '3' | '7' | '14',
          startingBid: ''
        });

        // Redirect to the new listing
        setTimeout(() => {
          navigate(`/listing/${result.data.id}`);
        }, 1500);
      }
    } catch (error: unknown) {
      setSubmitError(error instanceof Error ? error.message : 'Failed to create listing');
      console.error('Error creating listing:', error);
      
      // Track listing creation failure
      if (error instanceof Error) {
        captureException(error, {
          feature: 'listing_creation',
          listing_type: activeTab,
          category: formData.category,
          condition: formData.condition,
          has_images: formData.images.length > 0,
          image_count: formData.images.length,
          error_step: formData.images.length > 0 ? 'image_upload' : 'listing_creation'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-4xl mx-auto px-2 sm:px-3 md:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 md:py-8 w-full">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">Create Listing</h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">Share your items with fellow students</p>
        </div>

        {/* Onboarding Status */}
        {accountStatus && !accountStatus.isOnboarded && (
          <div className="mb-6">
            <OnboardingStatusBadge
              userId="current-user" // This should be the actual user ID
              isOnboarded={accountStatus.isOnboarded}
              pendingAmount={totalPendingAmount}
              orderCount={pendingOrderCount}
              onSetupClick={() => setShowOnboardingModal(true)}
              variant="detailed"
            />
          </div>
        )}

        {/* Success Message */}
        {submitSuccess && (
          <div className="mb-8 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 flex items-start">
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-green-800 dark:text-green-200">Listing Created Successfully!</h3>
              <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                Your listing has been created and is now visible to other students.
                Redirecting you to your listing...
              </p>
            </div>
          </div>
        )}

        {/* Error Message */}
        {submitError && (
          <div className="mb-8 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 flex items-start">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-red-800 dark:text-red-200">Error Creating Listing</h3>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">{submitError}</p>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 sm:mb-8 bg-gray-200 dark:bg-gray-800 p-1 rounded-2xl">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex items-center justify-center space-x-1 sm:space-x-2 py-2 sm:py-3 px-2 sm:px-4 rounded-xl font-medium transition-all text-sm sm:text-base ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
              disabled={isSubmitting}
            >
              <span className="text-base sm:text-lg">{tab.icon}</span>
              <span className="hidden min-[400px]:inline">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Form */}
        <div className="bg-white dark:bg-gray-800 rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 md:p-8">
          <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8">
            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 sm:mb-4">
                Photos (up to 8)
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 sm:gap-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative aspect-square rounded-xl overflow-hidden bg-gray-100 dark:bg-gray-700 group group">
                    <img
                      src={URL.createObjectURL(image)}
                      alt={`Upload ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 bg-black/50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      disabled={isSubmitting}
                    >
                      <X className="w-4 h-4" />
                    </button>
                    
                    {/* Upload Progress Indicator */}
                    {isUploading && uploadProgress[index] && uploadProgress[index].progress < 100 && (
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-2 shadow-lg">
                          <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary-500 rounded-full" 
                              style={{ width: `${uploadProgress[index].progress}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
                
                {formData.images.length < 8 && (
                  <label className="aspect-square border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 transition-colors">
                    <Upload className="w-8 h-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-500 dark:text-gray-400">Add Photo</span>
                    <input
                      type="file"
                      id="listing-images"
                      name="images"
                      multiple
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={handleImageUpload}
                      className="hidden"
                      disabled={isSubmitting}
                    />
                  </label>
                )}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Supported formats: JPG, PNG. Max size: 10MB per image.
              </p>
            </div>

            {/* Title */}
            <div>
              <label htmlFor="listing-title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Title
              </label>
              <input
                type="text"
                id="listing-title"
                name="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                placeholder="What are you selling?"
                required
                disabled={isSubmitting}
              />
            </div>

            {/* Category & Condition */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <label htmlFor="listing-category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <select
                  id="listing-category"
                  name="category"
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                  required
                  disabled={isSubmitting}
                >
                  <option value="">Select Category</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="listing-condition" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Condition
                </label>
                <select
                  id="listing-condition"
                  name="condition"
                  value={Object.keys(conditionMap).find(key => conditionMap[key] === formData.condition) || ''}
                  onChange={(e) => {
                    const selectedCondition = e.target.value;
                    setFormData({
                      ...formData,
                      condition: selectedCondition ? conditionMap[selectedCondition] : '' as ListingCondition
                    });
                  }}
                  className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                  required
                  disabled={isSubmitting}
                >
                  <option value="">Select Condition</option>
                  {conditions.map((condition) => (
                    <option key={condition} value={condition}>{condition}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Price */}
            <div>
              <label htmlFor="listing-price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Price
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-2.5 sm:top-3 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  id="listing-price"
                  name="price"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  className="w-full pl-10 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Visibility Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Visibility
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className={`flex items-center p-4 border rounded-xl cursor-pointer transition-all ${
                  formData.visibility === 'university'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                }`}>
                  <input
                    type="radio"
                    name="visibility"
                    value="university"
                    checked={formData.visibility === 'university'}
                    onChange={(e) => setFormData({ ...formData, visibility: e.target.value as 'university' | 'public' })}
                    className="sr-only"
                    disabled={isSubmitting}
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">University Only</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Only students from your university can see this listing</div>
                  </div>
                </label>

                <label className={`flex items-center p-4 border rounded-xl cursor-pointer transition-all ${
                  formData.visibility === 'public'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                }`}>
                  <input
                    type="radio"
                    name="visibility"
                    value="public"
                    checked={formData.visibility === 'public'}
                    onChange={(e) => setFormData({ ...formData, visibility: e.target.value as 'university' | 'public' })}
                    className="sr-only"
                    disabled={isSubmitting}
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">Public</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">All students can see this listing</div>
                  </div>
                </label>
              </div>
            </div>

            {/* Delivery Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                📦 Delivery Method
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className={`flex items-start p-4 border rounded-xl cursor-pointer transition-all ${
                  formData.deliveryMethod === 'in_person'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                }`}>
                  <input
                    type="radio"
                    name="deliveryMethod"
                    value="in_person"
                    checked={formData.deliveryMethod === 'in_person'}
                    onChange={(e) => setFormData({ ...formData, deliveryMethod: e.target.value as 'in_person' | 'mail' })}
                    className="sr-only"
                    disabled={isSubmitting}
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white mb-1">In-Person (Free)</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      We recommend meeting in a public place like a campus library or dining hall for safety.
                    </div>
                  </div>
                </label>

                <label className={`flex items-start p-4 border rounded-xl cursor-pointer transition-all ${
                  formData.deliveryMethod === 'mail'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                }`}>
                  <input
                    type="radio"
                    name="deliveryMethod"
                    value="mail"
                    checked={formData.deliveryMethod === 'mail'}
                    onChange={(e) => setFormData({ ...formData, deliveryMethod: e.target.value as 'in_person' | 'mail' })}
                    className="sr-only"
                    disabled={isSubmitting}
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white mb-1">Mail it (Shipping Required)</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Item will be shipped to the buyer's address.
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Shipping Options - Only show if mail delivery is selected */}
            {formData.deliveryMethod === 'mail' && (
              <div className="space-y-4 p-4 sm:p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg sm:rounded-xl">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">📦 Shipping Configuration</h3>

                {/* Shipping Model */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Shipping Method
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    <label className={`flex items-start p-3 border rounded-lg cursor-pointer transition-all ${
                      formData.shippingModel === 'shippo'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}>
                      <input
                        type="radio"
                        name="shippingModel"
                        value="shippo"
                        checked={formData.shippingModel === 'shippo'}
                        onChange={(e) => setFormData({ ...formData, shippingModel: e.target.value as 'shippo' | 'manual' })}
                        className="sr-only"
                        disabled={isSubmitting}
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 dark:text-white">Use Hive Shipping (via Shippo)</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          We'll generate shipping labels and handle tracking automatically.
                        </div>
                      </div>
                    </label>

                    <label className={`flex items-start p-3 border rounded-lg cursor-pointer transition-all ${
                      formData.shippingModel === 'manual'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}>
                      <input
                        type="radio"
                        name="shippingModel"
                        value="manual"
                        checked={formData.shippingModel === 'manual'}
                        onChange={(e) => setFormData({ ...formData, shippingModel: e.target.value as 'shippo' | 'manual' })}
                        className="sr-only"
                        disabled={isSubmitting}
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 dark:text-white">Ship on your own</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          You'll handle shipping and provide tracking number manually.
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Package Details - Only show for Hive Shipping */}
                {formData.shippingModel === 'shippo' && (
                  <div className="space-y-4">
                    <h4 className="text-md font-semibold text-gray-900 dark:text-white">📦 Package Details</h4>

                    {/* Package Preset Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        Choose Package Type
                      </label>
                      <div className="space-y-2">
                        <label className={`flex items-start p-3 border rounded-lg cursor-pointer transition-all ${
                          !formData.useCustomPackage
                            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                            : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                        }`}>
                          <input
                            type="radio"
                            name="packageType"
                            value="preset"
                            checked={!formData.useCustomPackage}
                            onChange={() => setFormData({ ...formData, useCustomPackage: false, packagePreset: 'Medium Box' })}
                            className="sr-only"
                            disabled={isSubmitting}
                          />
                          <div className="flex-1">
                            <div className="font-medium text-gray-900 dark:text-white">Use Preset</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Choose from common package sizes
                            </div>
                          </div>
                        </label>

                        <label className={`flex items-start p-3 border rounded-lg cursor-pointer transition-all ${
                          formData.useCustomPackage
                            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                            : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                        }`}>
                          <input
                            type="radio"
                            name="packageType"
                            value="custom"
                            checked={formData.useCustomPackage}
                            onChange={() => setFormData({ ...formData, useCustomPackage: true, packagePreset: '' })}
                            className="sr-only"
                            disabled={isSubmitting}
                          />
                          <div className="flex-1">
                            <div className="font-medium text-gray-900 dark:text-white">Custom Dimensions</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Enter exact package measurements
                            </div>
                          </div>
                        </label>
                      </div>
                    </div>

                    {/* Preset Selection */}
                    {!formData.useCustomPackage && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                          Package Preset
                        </label>
                        <select
                          value={formData.packagePreset}
                          onChange={(e) => setFormData({ ...formData, packagePreset: e.target.value })}
                          className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          disabled={isSubmitting}
                        >
                          <option value="">Select a preset...</option>
                          <option value="Small Box">📦 Small Box — 9×6×2 in, 10 oz</option>
                          <option value="Medium Box">📦 Medium Box — 12×10×4 in, 2 lbs</option>
                          <option value="Poly Mailer">📮 Poly Mailer — 10×13 in, 8 oz</option>
                        </select>
                      </div>
                    )}

                    {/* Custom Package Dimensions */}
                    {formData.useCustomPackage && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              📏 Length
                            </label>
                            <input
                              type="number"
                              value={formData.packageLength}
                              onChange={(e) => setFormData({ ...formData, packageLength: e.target.value })}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="12"
                              min="0.1"
                              step="0.1"
                              disabled={isSubmitting}
                              required
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              📐 Width
                            </label>
                            <input
                              type="number"
                              value={formData.packageWidth}
                              onChange={(e) => setFormData({ ...formData, packageWidth: e.target.value })}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="10"
                              min="0.1"
                              step="0.1"
                              disabled={isSubmitting}
                              required
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              🧱 Height
                            </label>
                            <input
                              type="number"
                              value={formData.packageHeight}
                              onChange={(e) => setFormData({ ...formData, packageHeight: e.target.value })}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="4"
                              min="0.1"
                              step="0.1"
                              disabled={isSubmitting}
                              required
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Dimension Unit
                          </label>
                          <select
                            value={formData.packageDimensionUnit}
                            onChange={(e) => setFormData({ ...formData, packageDimensionUnit: e.target.value as 'in' | 'cm' })}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                            disabled={isSubmitting}
                          >
                            <option value="in">Inches (in)</option>
                            <option value="cm">Centimeters (cm)</option>
                          </select>
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              ⚖️ Weight
                            </label>
                            <input
                              type="number"
                              value={formData.packageWeight}
                              onChange={(e) => setFormData({ ...formData, packageWeight: e.target.value })}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="2"
                              min="0.1"
                              step="0.1"
                              disabled={isSubmitting}
                              required
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Weight Unit
                            </label>
                            <select
                              value={formData.packageWeightUnit}
                              onChange={(e) => setFormData({ ...formData, packageWeightUnit: e.target.value as 'oz' | 'lb' })}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              disabled={isSubmitting}
                            >
                              <option value="oz">Ounces (oz)</option>
                              <option value="lb">Pounds (lb)</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Shipping Cost Responsibility */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Shipping Cost Responsibility
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    <label className={`flex items-start p-3 border rounded-lg cursor-pointer transition-all ${
                      formData.shippingPaidBy === 'buyer'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}>
                      <input
                        type="radio"
                        name="shippingPaidBy"
                        value="buyer"
                        checked={formData.shippingPaidBy === 'buyer'}
                        onChange={(e) => setFormData({ ...formData, shippingPaidBy: e.target.value as 'buyer' | 'seller' })}
                        className="sr-only"
                        disabled={isSubmitting}
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 dark:text-white">Buyer Pays</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Shipping fee will be added to checkout total.
                        </div>
                      </div>
                    </label>

                    <label className={`flex items-start p-3 border rounded-lg cursor-pointer transition-all ${
                      formData.shippingPaidBy === 'seller'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}>
                      <input
                        type="radio"
                        name="shippingPaidBy"
                        value="seller"
                        checked={formData.shippingPaidBy === 'seller'}
                        onChange={(e) => setFormData({ ...formData, shippingPaidBy: e.target.value as 'buyer' | 'seller' })}
                        className="sr-only"
                        disabled={isSubmitting}
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 dark:text-white">Seller Pays (You)</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Shipping fee will be deducted from your payout.
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Seller Address - Only show for Hive Shipping */}
                {formData.shippingModel === 'shippo' && (
                  <div className="space-y-4">
                    <h4 className="text-md font-semibold text-gray-900 dark:text-white">📍 Your Shipping Address</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      This address will be used as the shipping origin for your packages.
                    </p>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Full Name *
                        </label>
                        <input
                          type="text"
                          value={formData.sellerName}
                          onChange={(e) => setFormData({ ...formData, sellerName: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Your full name"
                          disabled={isSubmitting}
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          value={formData.sellerPhone}
                          onChange={(e) => setFormData({ ...formData, sellerPhone: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="(*************"
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Street Address *
                      </label>
                      <input
                        type="text"
                        value={formData.sellerStreet1}
                        onChange={(e) => setFormData({ ...formData, sellerStreet1: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        placeholder="123 Main Street"
                        disabled={isSubmitting}
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Apartment, Suite, etc. (Optional)
                      </label>
                      <input
                        type="text"
                        value={formData.sellerStreet2}
                        onChange={(e) => setFormData({ ...formData, sellerStreet2: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        placeholder="Apt 4B"
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          City *
                        </label>
                        <input
                          type="text"
                          value={formData.sellerCity}
                          onChange={(e) => setFormData({ ...formData, sellerCity: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="City"
                          disabled={isSubmitting}
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          State *
                        </label>
                        <input
                          type="text"
                          value={formData.sellerState}
                          onChange={(e) => setFormData({ ...formData, sellerState: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="CA"
                          disabled={isSubmitting}
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          ZIP Code *
                        </label>
                        <input
                          type="text"
                          value={formData.sellerZip}
                          onChange={(e) => setFormData({ ...formData, sellerZip: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="12345"
                          disabled={isSubmitting}
                          required
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Rent-specific fields */}
            {activeTab === 'rent' && (
              <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg sm:rounded-xl">
                <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white">Rental Details</h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Rental Period
                    </label>
                    <select
                      value={formData.rentalPeriod}
                      onChange={(e) => setFormData({ ...formData, rentalPeriod: e.target.value as 'weekly' | 'monthly' })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      disabled={isSubmitting}
                    >
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="listing-weekly-price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Weekly Price
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-2.5 sm:top-3 w-5 h-5 text-gray-400" />
                      <input
                        type="number"
                        id="listing-weekly-price"
                        name="weeklyPrice"
                        value={formData.weeklyPrice}
                        onChange={(e) => setFormData({ ...formData, weeklyPrice: e.target.value })}
                        className="w-full pl-10 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                        placeholder="0.00"
                        step="0.01"
                        min="0"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="listing-monthly-price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Monthly Price
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-2.5 sm:top-3 w-5 h-5 text-gray-400" />
                      <input
                        type="number"
                        id="listing-monthly-price"
                        name="monthlyPrice"
                        value={formData.monthlyPrice}
                        onChange={(e) => setFormData({ ...formData, monthlyPrice: e.target.value })}
                        className="w-full pl-10 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                        placeholder="0.00"
                        step="0.01"
                        min="0"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Available From
                    </label>
                    <input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      disabled={isSubmitting}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Available Until
                    </label>
                    <input
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      disabled={isSubmitting}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Auction-specific fields */}
            {activeTab === 'auction' && (
              <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 bg-purple-50 dark:bg-purple-900/20 rounded-lg sm:rounded-xl">
                <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white">Auction Details</h3>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Starting Bid
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-2.5 sm:top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="number"
                      value={formData.startingBid}
                      onChange={(e) => setFormData({ ...formData, startingBid: e.target.value })}
                      className="w-full pl-10 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Auction Start Date
                    </label>
                    <input
                      type="date"
                      value={formData.auctionStartDate}
                      onChange={(e) => setFormData({ ...formData, auctionStartDate: e.target.value })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      required
                      disabled={isSubmitting}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Auction Start Time
                    </label>
                    <input
                      type="time"
                      value={formData.auctionStartTime}
                      onChange={(e) => setFormData({ ...formData, auctionStartTime: e.target.value })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Auction End Date
                    </label>
                    <input
                      type="date"
                      value={formData.auctionEndDate}
                      onChange={(e) => setFormData({ ...formData, auctionEndDate: e.target.value })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      required
                      disabled={isSubmitting}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Auction End Time
                    </label>
                    <input
                      type="time"
                      value={formData.auctionEndTime}
                      onChange={(e) => setFormData({ ...formData, auctionEndTime: e.target.value })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Auction Duration
                  </label>
                  <select
                    value={formData.auctionDuration}
                    onChange={(e) => setFormData({ ...formData, auctionDuration: e.target.value as '1' | '3' | '7' | '14' })}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-base"
                    disabled={isSubmitting}
                  >
                    <option value="1">1 Day</option>
                    <option value="3">3 Days</option>
                    <option value="7">7 Days</option>
                    <option value="14">14 Days</option>
                  </select>
                </div>
              </div>
            )}

            {/* Description */}
            <div>
              <label htmlFor="listing-description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                id="listing-description"
                name="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={4}
                className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none text-base"
                placeholder="Describe your item in detail..."
                required
                disabled={isSubmitting}
              />

              {/* Safety Warning Banner */}
              <div className="mt-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div className="flex items-start text-sm text-amber-700 dark:text-amber-300">
                  <div className="flex-shrink-0 mr-2 mt-0.5">
                    📢
                  </div>
                  <span>
                    Please avoid using phone numbers, emails, or external payment links. Keep all communication and transactions within Hive Campus for your safety.
                  </span>
                </div>
              </div>
            </div>



            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <button
                type="button"
                className="w-full sm:flex-1 py-3 px-6 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg sm:rounded-xl font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed text-base"
                disabled={isSubmitting}
              >
                Save Draft
              </button>
              <button
                type="submit"
                className="w-full sm:flex-1 bg-gradient-to-r from-primary-600 to-primary-700 text-white py-3 px-6 rounded-lg sm:rounded-xl font-semibold hover:from-primary-700 hover:to-primary-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-base"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center space-x-2">
                    <Loader className="w-5 h-5 animate-spin" />
                    <span>Processing...</span>
                  </span>
                ) : (
                  activeTab === 'sell' ? 'List Item' : activeTab === 'rent' ? 'Create Rental' : 'Start Auction'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Stripe Onboarding Modal */}
      <StripeOnboardingModal
        isOpen={showOnboardingModal}
        onClose={() => setShowOnboardingModal(false)}
        pendingAmount={totalPendingAmount}
        orderCount={pendingOrderCount}
        onOnboardingComplete={() => {
          setShowOnboardingModal(false);
          // Optionally refresh account status
        }}
      />
    </div>
  );
};

export default AddListing;