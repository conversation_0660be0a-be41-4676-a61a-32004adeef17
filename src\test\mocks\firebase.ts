import { vi } from 'vitest'

// Mock auth instance
const mockAuth = {
  currentUser: null,
  onAuthStateChanged: vi.fn((callback) => {
    // Call the callback with null initially
    callback(null)
    // Return unsubscribe function
    return vi.fn()
  }),
  signInWithEmailAndPassword: vi.fn(),
  createUserWithEmailAndPassword: vi.fn(),
  signOut: vi.fn(),
  updateProfile: vi.fn(),
  updatePassword: vi.fn(),
  deleteUser: vi.fn(),
  sendPasswordResetEmail: vi.fn(),
}

// Mock Firebase modules
export const setupFirebaseMocks = () => {
  // Mock Firebase config
  vi.mock('../../firebase/config', () => ({
    default: {},
    app: {},
    auth: mockAuth,
    firestore: {},
    storage: {},
  }))

  // Mock Firebase Auth
  vi.mock('../../firebase/auth', () => ({
    signInWithEmailAndPassword: vi.fn(),
    createUserWithEmailAndPassword: vi.fn(),
    signOut: vi.fn(),
    onAuthStateChanged: vi.fn(),
    updateProfile: vi.fn(),
    updatePassword: vi.fn(),
    deleteUser: vi.fn(),
    sendPasswordResetEmail: vi.fn(),
    getUserProfile: vi.fn(),
  }))

  // Mock Firebase main export
  vi.mock('../../firebase', () => ({
    auth: mockAuth,
    firestore: {},
    storage: {},
    getUserProfile: vi.fn(),
  }))

  // Mock Sentry
  vi.mock('../../utils/sentry', () => ({
    setSentryUser: vi.fn(),
    captureTypedEvent: vi.fn(),
    SentryEventType: {
      LOGIN: 'LOGIN',
      LOGOUT: 'LOGOUT',
    },
  }))

  // Mock Firebase Firestore
  vi.mock('@/firebase/listings', () => ({
    getListings: vi.fn(() => Promise.resolve([])),
    getListing: vi.fn(() => Promise.resolve(null)),
    createListing: vi.fn(() => Promise.resolve('test-id')),
    updateListing: vi.fn(() => Promise.resolve()),
    deleteListing: vi.fn(() => Promise.resolve()),
    searchListings: vi.fn(() => Promise.resolve([])),
  }))

  // Mock Firebase Messages
  vi.mock('@/firebase/messages', () => ({
    getConversations: vi.fn(() => Promise.resolve([])),
    getMessages: vi.fn(() => Promise.resolve([])),
    sendMessage: vi.fn(() => Promise.resolve()),
    markAsRead: vi.fn(() => Promise.resolve()),
  }))

  // Mock Firebase Feedback
  vi.mock('@/firebase/feedback', () => ({
    getFeedback: vi.fn(() => Promise.resolve([])),
    createFeedback: vi.fn(() => Promise.resolve('test-id')),
    updateFeedback: vi.fn(() => Promise.resolve()),
  }))

  // Mock Firebase Storage
  vi.mock('@/firebase/uploads', () => ({
    uploadFile: vi.fn(() => Promise.resolve('https://example.com/test.jpg')),
    deleteFile: vi.fn(() => Promise.resolve()),
  }))
}

// Mock user data for tests
export const mockUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User',
  photoURL: 'https://example.com/avatar.jpg',
  emailVerified: true,
  isAnonymous: false,
  metadata: {
    creationTime: '2024-01-01T00:00:00.000Z',
    lastSignInTime: '2024-01-01T00:00:00.000Z',
  },
  providerData: [],
  refreshToken: 'test-refresh-token',
  tenantId: null,
  delete: vi.fn(),
  getIdToken: vi.fn(() => Promise.resolve('test-token')),
  getIdTokenResult: vi.fn(),
  reload: vi.fn(),
  toJSON: vi.fn(),
}

// Mock listing data for tests
export const mockListing = {
  id: 'test-listing-id',
  title: 'Test Product',
  description: 'Test Description',
  price: 99.99,
  category: 'Electronics',
  condition: 'Like New',
  images: ['https://example.com/image1.jpg'],
  sellerId: 'test-seller-id',
  sellerName: 'Test Seller',
  sellerEmail: '<EMAIL>',
  university: 'Test University',
  status: 'available',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  views: 0,
  favorites: 0,
  tags: ['electronics', 'laptop'],
}