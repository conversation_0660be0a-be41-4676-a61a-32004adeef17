import React, { useState } from 'react';
import { MessageCircle, Star, Send, Smile, Meh, Frown } from 'lucide-react';
import { captureTypedEvent, SentryEventType } from '../utils/sentry';
import { useAuth } from '../hooks/useAuth';

const Feedback: React.FC = () => {
  const { userRole } = useAuth();
  const [selectedRating, setSelectedRating] = useState(0);
  const [selectedEmoji, setSelectedEmoji] = useState('');
  const [feedback, setFeedback] = useState('');
  const [category, setCategory] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const emojiOptions = [
    { id: 'happy', icon: Smile, label: 'Great!', color: 'text-green-500' },
    { id: 'neutral', icon: Meh, label: 'Okay', color: 'text-yellow-500' },
    { id: 'sad', icon: Frown, label: 'Poor', color: 'text-red-500' }
  ];

  const categories = [
    'App Performance',
    'User Interface',
    'Search & Filters',
    'Messaging System',
    'Payment Process',
    'Safety & Security',
    'General Feedback'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Log feedback submission to console
    console.log('Feedback submitted:', {
      rating: selectedRating,
      emoji: selectedEmoji,
      category,
      feedback
    });
    
    // Track feedback submission in Sentry
    captureTypedEvent(SentryEventType.FEEDBACK_SUBMITTED, {
      rating: selectedRating,
      sentiment: selectedEmoji,
      category,
      feedback_length: feedback.length,
      user_role: userRole || 'unknown',
      // Don't include the actual feedback text to avoid PII in Sentry
      has_feedback: feedback.length > 0
    });
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitSuccess(true);
      
      // Reset form after a delay
      setTimeout(() => {
        setSelectedRating(0);
        setSelectedEmoji('');
        setCategory('');
        setFeedback('');
        setSubmitSuccess(false);
      }, 3000);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-4xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <MessageCircle className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">We Value Your Feedback</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Help us improve Hive Campus by sharing your thoughts and suggestions
          </p>
        </div>

        {/* Feedback Form */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Emoji Rating */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                How was your experience?
              </label>
              <div className="flex justify-center space-x-8">
                {emojiOptions.map((emoji) => {
                  const IconComponent = emoji.icon;
                  return (
                    <button
                      key={emoji.id}
                      type="button"
                      onClick={() => setSelectedEmoji(emoji.id)}
                      className={`flex flex-col items-center p-6 rounded-2xl transition-all transform hover:scale-110 ${
                        selectedEmoji === emoji.id
                          ? 'bg-primary-50 dark:bg-primary-900/20 ring-2 ring-primary-500'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      <IconComponent className={`w-12 h-12 mb-2 ${emoji.color}`} />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {emoji.label}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Star Rating */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Rate your overall experience
              </label>
              <div className="flex justify-center space-x-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => setSelectedRating(star)}
                    className="transition-all transform hover:scale-110"
                  >
                    <Star
                      className={`w-10 h-10 ${
                        star <= selectedRating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300 dark:text-gray-600'
                      }`}
                    />
                  </button>
                ))}
              </div>
              {selectedRating > 0 && (
                <p className="text-center text-sm text-gray-600 dark:text-gray-400 mt-2">
                  {selectedRating} out of 5 stars
                </p>
              )}
            </div>

            {/* Category Selection */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                What area would you like to provide feedback on?
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {categories.map((cat) => (
                  <button
                    key={cat}
                    type="button"
                    onClick={() => setCategory(cat)}
                    className={`p-3 text-sm font-medium rounded-xl transition-all ${
                      category === cat
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {cat}
                  </button>
                ))}
              </div>
            </div>

            {/* Feedback Text */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Tell us more about your experience
              </label>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                rows={6}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                placeholder="Share your thoughts, suggestions, or any issues you've encountered..."
                required
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                {feedback.length}/500 characters
              </p>
            </div>

            {/* Submit Button */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting || submitSuccess}
                className={`${
                  submitSuccess 
                    ? 'bg-green-600 hover:bg-green-700' 
                    : 'bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800'
                } text-white px-8 py-4 rounded-xl font-bold text-lg transform hover:scale-[1.02] transition-all duration-200 shadow-lg flex items-center space-x-2 mx-auto ${
                  (isSubmitting || submitSuccess) ? 'opacity-90 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Submitting...</span>
                  </>
                ) : submitSuccess ? (
                  <>
                    <Star className="w-5 h-5" />
                    <span>Thank You!</span>
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5" />
                    <span>Submit Feedback</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Additional Info */}
        <div className="mt-8 text-center">
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Your feedback helps us create a better marketplace for all students
          </p>
          <div className="flex justify-center space-x-8 text-sm text-gray-500 dark:text-gray-400">
            <span>✓ Anonymous feedback option</span>
            <span>✓ Response within 24 hours</span>
            <span>✓ Direct impact on improvements</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Feedback;