#!/bin/bash

# Hive Campus Production Setup Script
set -e

echo "🔧 Setting up Hive Campus for Production..."

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
  echo -e "${RED}Error: Firebase CLI is not installed${NC}"
  echo "Install it with: npm install -g firebase-tools"
  exit 1
fi

# Login check
if ! firebase projects:list &> /dev/null; then
  echo -e "${YELLOW}Please login to Firebase:${NC}"
  firebase login
fi

# Get project ID
read -p "Enter your Firebase Project ID: " PROJECT_ID

if [ -z "$PROJECT_ID" ]; then
  echo -e "${RED}Error: Project ID is required${NC}"
  exit 1
fi

echo -e "${BLUE}Setting up project: ${PROJECT_ID}${NC}"

# Use the specified project
firebase use "$PROJECT_ID"

echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm install

echo -e "${YELLOW}📦 Installing Functions dependencies...${NC}"
cd functions && npm install && cd ..

echo -e "${YELLOW}⚙️  Setting up Firebase Functions configuration...${NC}"

# Prompt for environment variables (production values should be set manually for security)
echo -e "${BLUE}Setting up Firebase Functions config...${NC}"
echo -e "${YELLOW}Note: You'll need to set these values manually for security:${NC}"

echo "Example commands to set production config:"
echo "firebase functions:config:set stripe.api_key=\"sk_live_...\" --project $PROJECT_ID"
echo "firebase functions:config:set stripe.webhook_secret=\"whsec_...\" --project $PROJECT_ID" 
echo "firebase functions:config:set stripe.auto_release_escrow_days=\"7\" --project $PROJECT_ID"
echo "firebase functions:config:set app.url=\"https://hivecampus.app\" --project $PROJECT_ID"
echo "firebase functions:config:set openai.api_key=\"sk-...\" --project $PROJECT_ID"
echo "firebase functions:config:set slack.webhook_url=\"https://hooks.slack.com/...\" --project $PROJECT_ID"
echo "firebase functions:config:set shippo.api_key=\"shippo_live_...\" --project $PROJECT_ID"
echo "firebase functions:config:set email.host=\"smtp.gmail.com\" --project $PROJECT_ID"
echo "firebase functions:config:set email.port=\"587\" --project $PROJECT_ID"
echo "firebase functions:config:set email.user=\"<EMAIL>\" --project $PROJECT_ID"
echo "firebase functions:config:set email.password=\"your-app-password\" --project $PROJECT_ID"
echo "firebase functions:config:set email.from=\"<EMAIL>\" --project $PROJECT_ID"
echo "firebase functions:config:set admin.email=\"<EMAIL>\" --project $PROJECT_ID"

echo -e "${YELLOW}🔥 Setting up Firestore Security Rules...${NC}"
firebase deploy --only firestore:rules --project "$PROJECT_ID"

echo -e "${YELLOW}📁 Setting up Storage Security Rules...${NC}"
# Create storage.rules if it doesn't exist
if [ ! -f "storage.rules" ]; then
  cat > storage.rules << EOL
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Profile pictures
    match /profile-pictures/{userId}/{fileName} {
      allow read;
      allow write: if request.auth != null && request.auth.uid == userId
                   && resource == null
                   && request.resource.size < 5 * 1024 * 1024  // 5MB limit
                   && request.resource.contentType.matches('image/.*');
    }
    
    // Listing images
    match /listing-images/{userId}/{listingId}/{fileName} {
      allow read;
      allow write: if request.auth != null && request.auth.uid == userId
                   && resource == null
                   && request.resource.size < 10 * 1024 * 1024  // 10MB limit
                   && request.resource.contentType.matches('image/.*');
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Issue report screenshots
    match /issue-reports/{userId}/{reportId}/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId
                   && resource == null
                   && request.resource.size < 5 * 1024 * 1024  // 5MB limit
                   && request.resource.contentType.matches('image/.*');
    }
  }
}
EOL
fi

firebase deploy --only storage --project "$PROJECT_ID"

echo -e "${YELLOW}🏗️  Building application...${NC}"
npm run build

echo -e "${YELLOW}🏗️  Building functions...${NC}"
cd functions && npm run build && cd ..

echo -e "${YELLOW}🚀 Deploying to Firebase...${NC}"
firebase deploy --project "$PROJECT_ID"

echo -e "${GREEN}✅ Production setup completed!${NC}"
echo -e "${BLUE}Your application is now deployed to: https://${PROJECT_ID}.web.app${NC}"

echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Set up your domain (if using custom domain)"
echo "2. Configure Stripe webhooks to point to your Functions endpoints"
echo "3. Set up monitoring and alerts"
echo "4. Configure your DNS settings"
echo "5. Test all functionality in production"

echo -e "${GREEN}🎉 Hive Campus is ready for production!${NC}"