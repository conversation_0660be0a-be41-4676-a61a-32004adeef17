import { User } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { User as AppUser } from '../firebase/types';

/**
 * Check if a user has admin privileges
 */
export const isUserAdmin = async (user: User): Promise<boolean> => {
  if (!user) return false;

  try {
    // Check custom claims first (faster)
    const idTokenResult = await user.getIdTokenResult();
    if (idTokenResult.claims.admin === true) {
      return true;
    }

    // Fallback to Firestore check
    const userDoc = await getDoc(doc(firestore, 'users', user.uid));
    if (userDoc.exists()) {
      const userData = userDoc.data() as AppUser;
      return userData.role === 'admin';
    }

    return false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

/**
 * Get admin user profile with enhanced permissions
 */
export const getAdminProfile = async (user: User): Promise<AppUser | null> => {
  if (!user) return null;

  try {
    const userDoc = await getDoc(doc(firestore, 'users', user.uid));
    if (userDoc.exists()) {
      const userData = userDoc.data() as AppUser;
      
      // Verify admin role
      if (userData.role === 'admin') {
        return userData;
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting admin profile:', error);
    return null;
  }
};

/**
 * Admin permission levels
 */
export enum AdminPermission {
  READ_USERS = 'read_users',
  WRITE_USERS = 'write_users',
  DELETE_USERS = 'delete_users',
  READ_LISTINGS = 'read_listings',
  MODERATE_LISTINGS = 'moderate_listings',
  DELETE_LISTINGS = 'delete_listings',
  READ_TRANSACTIONS = 'read_transactions',
  MANAGE_TRANSACTIONS = 'manage_transactions',
  READ_ANALYTICS = 'read_analytics',
  SYSTEM_SETTINGS = 'system_settings',
  SUPER_ADMIN = 'super_admin'
}

/**
 * Check if admin has specific permission
 */
export const hasAdminPermission = (
  adminUser: AppUser,
  _permission: AdminPermission
): boolean => {
  if (!adminUser || adminUser.role !== 'admin') {
    return false;
  }

  // For now, all admins have all permissions
  // In the future, this can be enhanced with role-based permissions
  return true;
};

/**
 * Admin action logging
 */
export const logAdminAction = async (
  adminUser: AppUser,
  action: string,
  details: Record<string, any> = {}
): Promise<void> => {
  try {
    // TODO: Implement admin action logging to Firestore
    console.log('Admin Action:', {
      adminId: adminUser.uid,
      adminEmail: adminUser.email,
      action,
      details,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error logging admin action:', error);
  }
};

/**
 * Admin session management
 */
export class AdminSession {
  private static instance: AdminSession;
  private sessionData: Map<string, any> = new Map();

  static getInstance(): AdminSession {
    if (!AdminSession.instance) {
      AdminSession.instance = new AdminSession();
    }
    return AdminSession.instance;
  }

  setSessionData(key: string, value: any): void {
    this.sessionData.set(key, value);
  }

  getSessionData(key: string): any {
    return this.sessionData.get(key);
  }

  clearSession(): void {
    this.sessionData.clear();
  }
}

/**
 * Admin route guards
 */
export const adminRouteGuards = {
  requiresPermission: (permission: AdminPermission) => {
    return (adminUser: AppUser | null): boolean => {
      if (!adminUser) return false;
      return hasAdminPermission(adminUser, permission);
    };
  },

  requiresSuperAdmin: (adminUser: AppUser | null): boolean => {
    if (!adminUser) return false;
    return hasAdminPermission(adminUser, AdminPermission.SUPER_ADMIN);
  }
};
