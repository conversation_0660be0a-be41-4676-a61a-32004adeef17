name: Deploy to Firebase Staging

on:
  push:
    branches: [ develop, staging ]
  workflow_dispatch:

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Functions dependencies
      run: |
        cd functions
        npm ci
        
    - name: Build project
      run: npm run build
      
    - name: Build functions
      run: |
        cd functions
        npm run build
        
    - name: Deploy to Firebase Staging
      uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_STAGING }}'
        projectId: '${{ secrets.FIREBASE_PROJECT_ID_STAGING }}'
        channelId: live
      env:
        STRIPE_API_KEY: ${{ secrets.STRIPE_API_KEY_TEST }}
        STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET_TEST }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        SHIPPO_API_KEY: ${{ secrets.SHIPPO_API_KEY_TEST }}
        EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
        EMAIL_PORT: ${{ secrets.EMAIL_PORT }}
        EMAIL_USER: ${{ secrets.EMAIL_USER }}
        EMAIL_PASS: ${{ secrets.EMAIL_PASS }}
        EMAIL_FROM: ${{ secrets.EMAIL_FROM }}
        VITE_APP_OWNER_EMAIL: ${{ secrets.VITE_APP_OWNER_EMAIL }}