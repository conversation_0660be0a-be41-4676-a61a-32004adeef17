import { httpsCallable } from 'firebase/functions';
import { functions } from './config';
import { ListingCondition, ListingType, ListingStatus } from './types';

// Create a new listing
export const createListing = async (data: {
  title: string;
  description: string;
  price: number;
  category: string;
  condition: ListingCondition;
  type: ListingType;
  imageURLs: string[];
  visibility?: 'university' | 'public';
  // Delivery method fields
  deliveryMethod?: 'in_person' | 'mail';
  shippingOptions?: {
    model: 'shippo' | 'manual';
    paidBy: 'buyer' | 'seller';
    packageSize?: 'small' | 'medium' | 'large';
    estimatedCost?: number;
    allowBuyerChoice?: boolean;
  };
  // Rent-specific fields
  rentalPeriod?: 'weekly' | 'monthly';
  weeklyPrice?: number;
  monthlyPrice?: number;
  startDate?: string;
  endDate?: string;
  // Auction-specific fields
  startingBid?: number;
  auctionStartDate?: string;
  auctionStartTime?: string;
  auctionEndDate?: string;
  auctionEndTime?: string;
  auctionDuration?: number;
}) => {
  try {
    const createListingFn = httpsCallable(functions, 'createListing');
    const result = await createListingFn(data);
    return result.data;
  } catch (error: unknown) {
    console.error('Error creating listing:', error);
    throw error;
  }
};

// Edit an existing listing
export const editListing = async (data: {
  listingId: string;
  title?: string;
  description?: string;
  price?: number;
  category?: string;
  condition?: ListingCondition;
  type?: ListingType;
  imageURLs?: string[];
  status?: ListingStatus;
}) => {
  try {
    const editListingFn = httpsCallable(functions, 'editListing');
    const result = await editListingFn(data);
    return result.data;
  } catch (error: unknown) {
    console.error('Error editing listing:', error);
    throw error;
  }
};

// Delete a listing
export const deleteListing = async (listingId: string) => {
  try {
    const deleteListingFn = httpsCallable(functions, 'deleteListing');
    const result = await deleteListingFn({ listingId });
    return result.data;
  } catch (error: unknown) {
    console.error('Error deleting listing:', error);
    throw error;
  }
};

// Get a single listing by ID
export const getListingById = async (listingId: string) => {
  try {
    const getListingByIdFn = httpsCallable(functions, 'getListingById');
    const result = await getListingByIdFn({ listingId });
    return result.data;
  } catch (error: unknown) {
    console.error('Error getting listing:', error);
    throw error;
  }
};

// Get listings with filtering
export const getListings = async (filters: {
  university?: string;
  category?: string;
  type?: ListingType;
  condition?: ListingCondition;
  minPrice?: number;
  maxPrice?: number;
  ownerId?: string;
  status?: ListingStatus;
  limit?: number;
  lastVisible?: string;
}) => {
  try {
    const getListingsFn = httpsCallable(functions, 'getListings');
    const result = await getListingsFn(filters);
    return result.data;
  } catch (error: unknown) {
    console.error('Error getting listings:', error);
    throw error;
  }
};

// Search listings by title or description
export const searchListings = async (query: string, limit: number = 20) => {
  try {
    const searchListingsFn = httpsCallable(functions, 'searchListings');
    const result = await searchListingsFn({ query, limit });
    return result.data;
  } catch (error: unknown) {
    console.error('Error searching listings:', error);
    throw error;
  }
};