/**
 * Utility functions for handling price calculations and formatting
 */

/**
 * Rounds a price to 2 decimal places to avoid floating-point precision issues
 * @param price - The price to round
 * @returns The rounded price
 */
export const roundPrice = (price: number): number => {
  return Math.round(price * 100) / 100;
};

/**
 * Formats a price for display with proper 2 decimal places
 * @param price - The price to format
 * @returns The formatted price string
 */
export const formatPrice = (price: number): string => {
  return roundPrice(price).toFixed(2);
};

/**
 * Calculates the total checkout price including all fees
 * @param basePrice - The item price
 * @param shippingFee - The shipping fee
 * @param platformFeeRate - The platform fee rate (0.08 for textbooks, 0.10 for others)
 * @returns Object with all price components and total
 */
export const calculateCheckoutTotal = (
  basePrice: number,
  shippingFee: number,
  platformFeeRate: number
) => {
  const roundedBasePrice = roundPrice(basePrice);
  const roundedShippingFee = roundPrice(shippingFee);
  const platformFee = roundPrice(roundedBasePrice * platformFeeRate);
  const total = roundPrice(roundedBasePrice + roundedShippingFee + platformFee);

  return {
    basePrice: roundedBasePrice,
    shippingFee: roundedShippingFee,
    platformFee,
    platformFeeRate,
    total
  };
};

/**
 * Determines if a listing is a textbook or course material for platform fee calculation
 * @param category - The listing category
 * @returns True if it's a textbook/course material (8% fee), false otherwise (10% fee)
 */
export const isTextbookOrCourseMaterial = (category: string): boolean => {
  const textbookCategories = ['textbooks', 'course-materials'];
  return textbookCategories.includes(category.toLowerCase());
};

/**
 * Gets the platform fee rate based on listing category
 * @param category - The listing category
 * @returns The platform fee rate (0.08 for textbooks, 0.10 for others)
 */
export const getPlatformFeeRate = (category: string): number => {
  return isTextbookOrCourseMaterial(category) ? 0.08 : 0.10;
};
