import * as functions from 'firebase-functions';
import * as nodemailer from 'nodemailer';

// Email configuration from environment variables
const EMAIL_HOST = process.env.EMAIL_HOST || 'smtp.gmail.com';
const EMAIL_PORT = parseInt(process.env.EMAIL_PORT || '587', 10);
const EMAIL_USER = process.env.EMAIL_USER;
const EMAIL_PASS = process.env.EMAIL_PASS;
const EMAIL_FROM = process.env.EMAIL_FROM || '<EMAIL>';
const OWNER_EMAIL = process.env.VITE_APP_OWNER_EMAIL || '<EMAIL>';

// Create a transporter for sending emails
let transporter: nodemailer.Transporter | null = null;

/**
 * Initialize the email transporter
 */
function getTransporter(): nodemailer.Transporter {
  if (!transporter) {
    // Check if email credentials are configured
    if (!EMAIL_USER || !EMAIL_PASS) {
      throw new Error('Email credentials are not configured. Set the EMAIL_USER and EMAIL_PASS environment variables.');
    }
    
    // Create the transporter
    transporter = nodemailer.createTransport({
      host: EMAIL_HOST,
      port: EMAIL_PORT,
      secure: EMAIL_PORT === 465, // true for 465, false for other ports
      auth: {
        user: EMAIL_USER,
        pass: EMAIL_PASS
      }
    });
  }
  
  return transporter;
}

/**
 * Send an email
 * 
 * @param to Recipient email address
 * @param subject Email subject
 * @param html Email content in HTML format
 * @param options Additional email options
 * @returns A promise that resolves when the email is sent
 */
export async function sendEmail(
  to: string,
  subject: string,
  html: string,
  options: {
    from?: string;
    text?: string;
    attachments?: any[];
  } = {}
): Promise<boolean> {
  try {
    // Try to get the transporter
    let emailTransporter: nodemailer.Transporter;
    
    try {
      emailTransporter = getTransporter();
    } catch (error) {
      // Log the error but don't fail - we'll use a fallback
      functions.logger.warn('Email transporter initialization failed:', error);
      
      // Log the email content for development/testing
      functions.logger.info(`Would send email to ${to} with subject "${subject}"`);
      functions.logger.debug('Email content:', html);
      
      return false;
    }
    
    // Send the email
    const info = await emailTransporter.sendMail({
      from: options.from || EMAIL_FROM,
      to,
      subject,
      text: options.text || '',
      html,
      attachments: options.attachments
    });
    
    functions.logger.info(`Email sent: ${info.messageId}`);
    return true;
  } catch (error) {
    functions.logger.error('Error sending email:', error);
    return false;
  }
}

/**
 * Send a critical error alert email
 * 
 * @param errorType The type of error
 * @param count The number of occurrences
 * @param route The route where the error occurred
 * @param sentryId Optional Sentry event ID
 * @param details Additional details about the error
 */
export async function sendCriticalErrorEmail(
  errorType: string,
  count: number,
  route: string,
  sentryId?: string,
  details?: string
): Promise<boolean> {
  const subject = `🔥 ALERT: ${count}+ ${errorType} on ${route}`;
  
  const html = `
    <h1 style="color: #e53e3e;">Critical Error Alert</h1>
    <p><strong>${count}+ ${errorType}</strong> detected on <code>${route}</code> in the last hour.</p>
    ${sentryId ? `<p><strong>Sentry ID:</strong> ${sentryId}</p>` : ''}
    ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/hive-campus/firestore/data/~2Freeflex_activity">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send a performance alert email
 * 
 * @param route The route with performance issues
 * @param duration The duration in milliseconds
 * @param count The number of occurrences
 */
export async function sendPerformanceEmail(
  route: string,
  duration: number,
  count: number
): Promise<boolean> {
  const subject = `⏱ ALERT: ${count}+ slow page loads on ${route}`;
  
  const html = `
    <h1 style="color: #dd6b20;">Performance Alert</h1>
    <p><strong>${count}+ slow page loads</strong> detected on <code>${route}</code> in the last hour.</p>
    <p><strong>Average Duration:</strong> ${duration}ms</p>
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/hive-campus/firestore/data/~2Freeflex_activity">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send a payment failure alert email
 * 
 * @param count The number of payment failures
 * @param details Additional details about the failures
 */
export async function sendPaymentFailureEmail(
  count: number,
  details?: string
): Promise<boolean> {
  const subject = `💰 ALERT: ${count}+ payment failures`;
  
  const html = `
    <h1 style="color: #e53e3e;">Payment Failure Alert</h1>
    <p><strong>${count}+ payment failures</strong> detected in the last hour.</p>
    ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/hive-campus/firestore/data/~2Freeflex_activity">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send a feedback alert email when multiple users report the same issue
 * 
 * @param route The route with multiple feedback reports
 * @param category The feedback category
 * @param count The number of feedback reports
 * @param quotes Sample user quotes
 */
export async function sendFeedbackEmail(
  route: string,
  category: string,
  count: number,
  quotes: string[] = []
): Promise<boolean> {
  const subject = `🧩 ALERT: ${count}+ users reported issues with ${category}`;
  
  let quotesHtml = '';
  if (quotes.length > 0) {
    quotesHtml = `
      <h3>Sample Feedback:</h3>
      <ul>
        ${quotes.map(q => `<li>"${q}"</li>`).join('')}
      </ul>
    `;
  }
  
  const html = `
    <h1 style="color: #3182ce;">User Feedback Alert</h1>
    <p><strong>${count}+ users reported issues</strong> with <strong>${category}</strong> on <code>${route}</code>.</p>
    ${quotesHtml}
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/hive-campus/firestore/data/~2Freeflex_feedback">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send the daily ReeFlex report email
 * 
 * @param report The report data
 * @param aiSummary The AI-generated summary
 */
export async function sendReportEmail(report: any, aiSummary: string): Promise<boolean> {
  const subject = `📊 ReeFlex Daily Report: ${new Date().toLocaleDateString()}`;
  
  // Format the email content
  const html = `
    <h1>Daily ReeFlex Report</h1>
    <p>Report period: ${report.timeRange.start} to ${report.timeRange.end}</p>
    
    <h2>AI Summary</h2>
    <div style="padding: 15px; background-color: #f8f9fa; border-radius: 5px; margin-bottom: 20px;">
      ${aiSummary.replace(/\n/g, '<br>')}
    </div>
    
    <h2>Overview</h2>
    <ul>
      <li>Total Events: ${report.summary.totalEvents}</li>
      <li>Total Errors: ${report.summary.totalErrors}</li>
      <li>Total Feedback: ${report.summary.totalFeedback}</li>
      <li>Page Views: ${report.summary.totalPageViews}</li>
      <li>User Interactions: ${report.summary.totalInteractions}</li>
    </ul>
    
    <h2>Top Issues</h2>
    <h3>Errors (${report.errors.count})</h3>
    ${report.errors.details.length > 0 ? `
      <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
        <tr>
          <th>Type</th>
          <th>Message</th>
          <th>Route</th>
          <th>Time</th>
        </tr>
        ${report.errors.details.slice(0, 5).map((error: any) => `
          <tr>
            <td>${error.type}</td>
            <td>${error.message}</td>
            <td>${error.route}</td>
            <td>${new Date(error.timestamp).toLocaleString()}</td>
          </tr>
        `).join('')}
      </table>
    ` : '<p>No errors reported in this period.</p>'}
    
    <h3>Performance Issues</h3>
    ${report.performance.slowestPages.length > 0 ? `
      <h4>Slowest Pages</h4>
      <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
        <tr>
          <th>Route</th>
          <th>Duration (ms)</th>
        </tr>
        ${report.performance.slowestPages.map((page: any) => `
          <tr>
            <td>${page.route}</td>
            <td>${page.duration}</td>
          </tr>
        `).join('')}
      </table>
    ` : '<p>No slow page loads reported in this period.</p>'}
    
    <h2>User Behavior</h2>
    <h3>Most Visited Pages</h3>
    <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
      <tr>
        <th>Route</th>
        <th>Views</th>
      </tr>
      ${report.userBehavior.topPages.map((page: any) => `
        <tr>
          <td>${page.route}</td>
          <td>${page.count}</td>
        </tr>
      `).join('')}
    </table>
    
    <h3>Checkout Funnel</h3>
    <ul>
      <li>Started: ${report.userBehavior.checkoutFunnel.started}</li>
      <li>Completed: ${report.userBehavior.checkoutFunnel.completed}</li>
      <li>Failed: ${report.userBehavior.checkoutFunnel.failed}</li>
      <li>Drop-off Rate: ${report.userBehavior.checkoutFunnel.dropOffRate}</li>
    </ul>
    
    <h2>User Feedback</h2>
    <p>
      Positive: ${report.feedback.positive} | 
      Negative: ${report.feedback.negative} | 
      Neutral: ${report.feedback.neutral}
    </p>
    
    <h3>User Quotes</h3>
    ${report.feedback.userQuotes.length > 0 ? `
      <ul>
        ${report.feedback.userQuotes.map((quote: any) => `
          <li>
            <strong>${quote.type} (${quote.category}):</strong> "${quote.message}"
            <em>- on ${quote.route}</em>
          </li>
        `).join('')}
      </ul>
    ` : '<p>No user quotes in this period.</p>'}
    
    <p>
      <a href="https://console.firebase.google.com/project/hive-campus/firestore/data/~2Freeflex_activity">
        View full data in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}