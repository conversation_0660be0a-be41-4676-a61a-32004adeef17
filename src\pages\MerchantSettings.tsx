import React, { useState } from 'react';
import { 
  Bell, 
  Moon, 
  Sun, 
  Lock, 
  User, 
  Mail, 
  Shield, 
  HelpCircle, 
  LogOut,
  ChevronRight,
  Store,
  CreditCard,
  Globe
} from 'lucide-react';


interface MerchantSettingsProps {
  toggleDarkMode: () => void;
  isDarkMode: boolean;
}

const MerchantSettings: React.FC<MerchantSettingsProps> = ({ toggleDarkMode, isDarkMode }) => {
  const [notifications, setNotifications] = useState({
    orderUpdates: true,
    newMessages: true,
    paymentAlerts: true,
    marketingEmails: false,
    systemUpdates: true
  });

  const handleNotificationChange = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const settingSections = [
    {
      title: 'Account',
      items: [
        {
          icon: User,
          label: 'Business Profile',
          description: 'Update your business information',
          action: () => console.log('Edit profile')
        },
        {
          icon: Mail,
          label: 'Email Settings',
          description: 'Manage your email preferences',
          action: () => console.log('Email settings')
        },
        {
          icon: Lock,
          label: 'Change Password',
          description: 'Update your password',
          action: () => console.log('Change password')
        }
      ]
    },
    {
      title: 'Business',
      items: [
        {
          icon: Store,
          label: 'Store Settings',
          description: 'Configure your store preferences',
          action: () => console.log('Store settings')
        },
        {
          icon: CreditCard,
          label: 'Payment Methods',
          description: 'Manage payment and billing',
          action: () => console.log('Payment settings')
        },
        {
          icon: Globe,
          label: 'Shipping & Delivery',
          description: 'Configure delivery options',
          action: () => console.log('Shipping settings')
        }
      ]
    },
    {
      title: 'Privacy & Security',
      items: [
        {
          icon: Shield,
          label: 'Privacy Settings',
          description: 'Control your business visibility',
          action: () => console.log('Privacy settings')
        },
        {
          icon: Lock,
          label: 'Two-Factor Authentication',
          description: 'Add an extra layer of security',
          action: () => console.log('2FA settings')
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          icon: HelpCircle,
          label: 'Partner Help Center',
          description: 'Get help and support',
          action: () => console.log('Help center')
        }
      ]
    }
  ];

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Partner Settings</h1>
            <p className="text-gray-600 dark:text-gray-400">Manage your business account and preferences</p>
          </div>

          <div className="space-y-6">
            {/* Appearance */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Appearance</h2>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {isDarkMode ? (
                    <Moon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  ) : (
                    <Sun className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  )}
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Dark Mode</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {isDarkMode ? 'Dark theme is enabled' : 'Light theme is enabled'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={toggleDarkMode}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 ${
                    isDarkMode ? 'bg-accent-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      isDarkMode ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* Notifications */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Notifications</h2>
              <div className="space-y-4">
                {Object.entries(notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Bell className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white capitalize">
                          {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {key === 'orderUpdates' && 'Get notified about new orders and status changes'}
                          {key === 'newMessages' && 'Get notified when you receive new messages'}
                          {key === 'paymentAlerts' && 'Get notified about payment confirmations and issues'}
                          {key === 'marketingEmails' && 'Receive promotional emails and partner updates'}
                          {key === 'systemUpdates' && 'Get notified about platform updates and maintenance'}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => handleNotificationChange(key as keyof typeof notifications)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 ${
                        value ? 'bg-accent-600' : 'bg-gray-200 dark:bg-gray-600'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          value ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Settings Sections */}
            {settingSections.map((section) => (
              <div key={section.title} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{section.title}</h2>
                <div className="space-y-2">
                  {section.items.map((item) => (
                    <button
                      key={item.label}
                      onClick={item.action}
                      className="w-full flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <item.icon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        <div className="text-left">
                          <p className="font-medium text-gray-900 dark:text-white">{item.label}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{item.description}</p>
                        </div>
                      </div>
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </button>
                  ))}
                </div>
              </div>
            ))}

            {/* Danger Zone */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-red-600 dark:text-red-400 mb-6">Danger Zone</h2>
              <div className="space-y-4">
                <button className="w-full flex items-center justify-between p-4 rounded-xl border border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                  <div className="flex items-center space-x-3">
                    <LogOut className="w-5 h-5 text-red-600 dark:text-red-400" />
                    <div className="text-left">
                      <p className="font-medium text-red-600 dark:text-red-400">Sign Out</p>
                      <p className="text-sm text-red-500 dark:text-red-500">Sign out of your partner account</p>
                    </div>
                  </div>
                  <ChevronRight className="w-5 h-5 text-red-400" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default MerchantSettings;