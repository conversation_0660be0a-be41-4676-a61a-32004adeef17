import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

// Define the BeforeInstallPromptEvent interface
interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>;
}

// Extend Window interface for PWA properties
declare global {
  interface Window {
    MSStream?: unknown;
  }
}

const PWAInstallPrompt = () => {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isIOSDevice, setIsIOSDevice] = useState(false);
  const [showIOSPrompt, setShowIOSPrompt] = useState(false);
  const [showAndroidPrompt, setShowAndroidPrompt] = useState(false);

  useEffect(() => {
    // Check if the device is iOS
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    setIsIOSDevice(isIOS);

    // For iOS, check if the app is already installed
    if (isIOS && !window.matchMedia('(display-mode: standalone)').matches) {
      // Show the iOS install prompt after a delay
      const timer = setTimeout(() => {
        setShowIOSPrompt(true);
      }, 3000);
      return () => clearTimeout(timer);
    }

    // For Android/other browsers, listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Store the event for later use
      setInstallPrompt(e as BeforeInstallPromptEvent);
      // Show the install button
      setShowAndroidPrompt(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Clean up
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!installPrompt) return;

    // Show the install prompt
    await installPrompt.prompt();

    // Wait for the user to respond to the prompt
    const choiceResult = await installPrompt.userChoice;
    
    if (choiceResult.outcome === 'accepted') {
      console.log('User accepted the install prompt');
    } else {
      console.log('User dismissed the install prompt');
    }

    // Clear the saved prompt since it can't be used again
    setInstallPrompt(null);
    setShowAndroidPrompt(false);
  };

  const dismissIOSPrompt = () => {
    setShowIOSPrompt(false);
    // Save to localStorage to avoid showing the prompt too often
    localStorage.setItem('iosPromptDismissed', Date.now().toString());
  };

  const dismissAndroidPrompt = () => {
    setShowAndroidPrompt(false);
  };

  if (!showIOSPrompt && !showAndroidPrompt) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4">
      <div className="mx-auto max-w-md bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-4 border border-gray-200">
        {isIOSDevice && showIOSPrompt ? (
          <div className="flex items-start">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">Install Hive Campus</h3>
              <p className="text-sm text-gray-600 mt-1">
                Install this app on your home screen for a better experience.
              </p>
              <div className="mt-3 text-sm">
                <p className="font-medium">How to install:</p>
                <ol className="list-decimal pl-5 mt-1 text-gray-600">
                  <li>Tap the share button <span className="inline-block w-5 h-5 bg-gray-200 rounded text-center">↑</span></li>
                  <li>Scroll down and tap "Add to Home Screen"</li>
                </ol>
              </div>
            </div>
            <button 
              onClick={dismissIOSPrompt}
              className="ml-2 text-gray-500 hover:text-gray-700"
              aria-label="Dismiss"
            >
              <X size={20} />
            </button>
          </div>
        ) : showAndroidPrompt ? (
          <div className="flex items-center">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">Install Hive Campus</h3>
              <p className="text-sm text-gray-600 mt-1">
                Install this app on your device for a better experience.
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button 
                onClick={handleInstallClick}
                className="px-4 py-2 bg-[#f9a826] text-white rounded-lg font-medium text-sm hover:bg-[#e89417] transition-colors"
              >
                Install
              </button>
              <button 
                onClick={dismissAndroidPrompt}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Dismiss"
              >
                <X size={20} />
              </button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default PWAInstallPrompt;