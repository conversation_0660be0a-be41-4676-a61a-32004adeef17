import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(_config: FullConfig) {
  const browser = await chromium.launch()
  const page = await browser.newPage()

  // Setup any global state needed for tests
  console.log('Setting up E2E test environment...')

  // You can setup test users, database state, etc. here
  // For now, we'll just ensure the app is accessible
  try {
    await page.goto('http://localhost:5173')
    await page.waitForLoadState('networkidle')
    console.log('✅ App is accessible and ready for testing')
  } catch (error) {
    console.error('❌ Failed to setup E2E environment:', error)
    throw error
  }

  await browser.close()
}

export default globalSetup