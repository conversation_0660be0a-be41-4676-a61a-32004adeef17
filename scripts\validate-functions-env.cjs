#!/usr/bin/env node

/**
 * Validate Firebase Functions Environment Configuration
 * This script checks if all required environment variables are set for production
 */

const { execSync } = require('child_process');

function validateFunctionsEnvironment() {
  console.log('🔍 Validating Firebase Functions Environment Configuration\n');
  
  try {
    // Get current Firebase Functions config
    const configOutput = execSync('firebase functions:config:get', { encoding: 'utf8' });
    const config = JSON.parse(configOutput);
    
    console.log('📋 Current Firebase Functions Configuration:');
    console.log(JSON.stringify(config, null, 2));
    
    // Required configurations
    const requiredConfig = {
      'stripe.api_key': 'Stripe API Key',
      'stripe.webhook_secret': 'Stripe Webhook Secret',
      'app.url': 'App URL'
    };
    
    // Optional but recommended configurations
    const optionalConfig = {
      'openai.api_key': 'OpenAI API Key',
      'sentry.dsn': 'Sentry DSN',
      'email.host': 'Email Host',
      'email.user': 'Email User',
      'admin.email': 'Admin Email'
    };
    
    let allValid = true;
    const missing = [];
    const warnings = [];
    
    // Check required configurations
    console.log('\n✅ Required Configuration:');
    for (const [path, description] of Object.entries(requiredConfig)) {
      const value = getNestedValue(config, path);
      if (value) {
        console.log(`  ✅ ${description}: ${maskValue(value)}`);
        
        // Additional validation
        if (path === 'stripe.api_key' && !value.startsWith('sk_live_')) {
          warnings.push(`Stripe API Key should start with 'sk_live_' for production (current: ${value.substring(0, 10)}...)`);
        }
      } else {
        console.log(`  ❌ ${description}: Missing`);
        missing.push(description);
        allValid = false;
      }
    }
    
    // Check optional configurations
    console.log('\n🔧 Optional Configuration:');
    for (const [path, description] of Object.entries(optionalConfig)) {
      const value = getNestedValue(config, path);
      if (value) {
        console.log(`  ✅ ${description}: ${maskValue(value)}`);
      } else {
        console.log(`  ⚠️  ${description}: Not set`);
      }
    }
    
    // Show warnings
    if (warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    // Summary
    console.log('\n📊 Summary:');
    if (allValid) {
      console.log('✅ All required configurations are present');
      console.log('🚀 Firebase Functions are ready for production deployment');
    } else {
      console.log('❌ Missing required configurations:');
      missing.forEach(item => console.log(`  - ${item}`));
      console.log('\n🔧 Run: npm run production:setup');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error validating environment:', error.message);
    console.log('\n💡 Make sure you are logged into Firebase CLI and have selected the correct project');
    console.log('Run: firebase login && firebase use <your-project-id>');
    process.exit(1);
  }
}

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

function maskValue(value) {
  if (typeof value !== 'string') return value;
  if (value.length <= 8) return '***';
  return value.substring(0, 4) + '***' + value.substring(value.length - 4);
}

// Run validation
validateFunctionsEnvironment();
