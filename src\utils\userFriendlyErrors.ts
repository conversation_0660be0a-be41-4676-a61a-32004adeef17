/**
 * User-Friendly Error Messages for Hive Campus
 * Converts technical errors into user-friendly messages with actionable advice
 */

export interface UserFriendlyError {
  title: string;
  message: string;
  action?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  canRetry: boolean;
  contactSupport: boolean;
}

/**
 * Convert technical errors to user-friendly messages
 */
export function getUserFriendlyError(error: Error | string): UserFriendlyError {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const lowerMessage = errorMessage.toLowerCase();

  // Authentication Errors
  if (lowerMessage.includes('unauthenticated') || lowerMessage.includes('not authenticated')) {
    return {
      title: 'Please Sign In',
      message: 'You need to be signed in to perform this action.',
      action: 'Please sign in to your account and try again.',
      severity: 'medium',
      canRetry: true,
      contactSupport: false
    };
  }

  if (lowerMessage.includes('permission') || lowerMessage.includes('unauthorized')) {
    return {
      title: 'Access Denied',
      message: 'You don\'t have permission to perform this action.',
      action: 'Please check your account permissions or contact support.',
      severity: 'medium',
      canRetry: false,
      contactSupport: true
    };
  }

  // Payment Errors
  if (lowerMessage.includes('card_declined') || lowerMessage.includes('declined')) {
    return {
      title: 'Payment Declined',
      message: 'Your payment method was declined.',
      action: 'Please try a different payment method or contact your bank.',
      severity: 'medium',
      canRetry: true,
      contactSupport: false
    };
  }

  if (lowerMessage.includes('insufficient_funds') || lowerMessage.includes('insufficient')) {
    return {
      title: 'Insufficient Funds',
      message: 'Your payment method has insufficient funds.',
      action: 'Please add funds to your account or use a different payment method.',
      severity: 'medium',
      canRetry: true,
      contactSupport: false
    };
  }

  if (lowerMessage.includes('expired_card') || lowerMessage.includes('expired')) {
    return {
      title: 'Card Expired',
      message: 'Your payment method has expired.',
      action: 'Please update your payment method with a valid card.',
      severity: 'medium',
      canRetry: true,
      contactSupport: false
    };
  }

  if (lowerMessage.includes('invalid_cvc') || lowerMessage.includes('cvc')) {
    return {
      title: 'Invalid Security Code',
      message: 'The security code (CVC) for your card is invalid.',
      action: 'Please check the 3-digit code on the back of your card.',
      severity: 'low',
      canRetry: true,
      contactSupport: false
    };
  }

  if (lowerMessage.includes('rate_limit') || lowerMessage.includes('too many requests')) {
    return {
      title: 'Too Many Attempts',
      message: 'You\'ve made too many requests. Please wait a moment.',
      action: 'Please wait a few minutes before trying again.',
      severity: 'low',
      canRetry: true,
      contactSupport: false
    };
  }

  // Network Errors
  if (lowerMessage.includes('network') || lowerMessage.includes('connection') || lowerMessage.includes('timeout')) {
    return {
      title: 'Connection Problem',
      message: 'We\'re having trouble connecting to our servers.',
      action: 'Please check your internet connection and try again.',
      severity: 'medium',
      canRetry: true,
      contactSupport: false
    };
  }

  // Firebase Errors
  if (lowerMessage.includes('unavailable') || lowerMessage.includes('deadline-exceeded')) {
    return {
      title: 'Service Temporarily Unavailable',
      message: 'Our service is temporarily unavailable.',
      action: 'Please try again in a few moments.',
      severity: 'medium',
      canRetry: true,
      contactSupport: false
    };
  }

  if (lowerMessage.includes('not-found') || lowerMessage.includes('does not exist')) {
    return {
      title: 'Item Not Found',
      message: 'The item you\'re looking for could not be found.',
      action: 'Please check the link or search for the item again.',
      severity: 'low',
      canRetry: false,
      contactSupport: false
    };
  }

  // File Upload Errors
  if (lowerMessage.includes('file too large') || lowerMessage.includes('size')) {
    return {
      title: 'File Too Large',
      message: 'The file you\'re trying to upload is too large.',
      action: 'Please choose a smaller file (under 10MB) and try again.',
      severity: 'low',
      canRetry: true,
      contactSupport: false
    };
  }

  if (lowerMessage.includes('invalid file type') || lowerMessage.includes('file type')) {
    return {
      title: 'Invalid File Type',
      message: 'The file type you\'re trying to upload is not supported.',
      action: 'Please upload an image file (JPG, PNG, or WebP).',
      severity: 'low',
      canRetry: true,
      contactSupport: false
    };
  }

  // Validation Errors
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) {
    return {
      title: 'Invalid Information',
      message: 'Some of the information you provided is invalid.',
      action: 'Please check your information and try again.',
      severity: 'low',
      canRetry: true,
      contactSupport: false
    };
  }

  // Email Errors
  if (lowerMessage.includes('email') && lowerMessage.includes('invalid')) {
    return {
      title: 'Invalid Email',
      message: 'Please enter a valid .edu email address.',
      action: 'Make sure you\'re using your university email address.',
      severity: 'low',
      canRetry: true,
      contactSupport: false
    };
  }

  // Default Error
  return {
    title: 'Something Went Wrong',
    message: 'We encountered an unexpected error.',
    action: 'Please try again. If the problem persists, contact our support team.',
    severity: 'medium',
    canRetry: true,
    contactSupport: true
  };
}

/**
 * Get error severity color for UI
 */
export function getErrorSeverityColor(severity: UserFriendlyError['severity']): string {
  switch (severity) {
    case 'low':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'medium':
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'high':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'critical':
      return 'text-red-800 bg-red-100 border-red-300';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

/**
 * Get error severity icon
 */
export function getErrorSeverityIcon(severity: UserFriendlyError['severity']): string {
  switch (severity) {
    case 'low':
      return '⚠️';
    case 'medium':
      return '🔶';
    case 'high':
      return '❌';
    case 'critical':
      return '🚨';
    default:
      return 'ℹ️';
  }
}
