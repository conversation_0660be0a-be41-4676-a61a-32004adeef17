import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { verifyAuth, handleError } from "../utils/helpers";
import { Feedback, IssueReport, Complaint } from "../utils/types";

// Submit feedback
export const submitFeedback = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);

    const { rating, category, subject, message, attachments } = data;

    if (!category || !subject || !message) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Category, subject, and message are required'
      );
    }

    if (rating && (rating < 1 || rating > 5)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Rating must be between 1 and 5'
      );
    }

    // Get user data
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    const userData = userDoc.exists ? userDoc.data() : null;

    // Create feedback object
    const feedback: Omit<Feedback, 'id'> = {
      userId: auth.uid,
      userName: userData?.name || 'Anonymous',
      userEmail: auth.email || '',
      rating: rating || null,
      category,
      subject,
      message,
      attachments: attachments || [],
      status: 'open',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };

    // Add to Firestore
    const docRef = await admin.firestore().collection('feedback').add(feedback);

    return {
      success: true,
      data: {
        id: docRef.id,
        ...feedback
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Report an issue
export const reportIssue = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { category, title, description, screenshotURL } = data;
    
    if (!category || !title || !description) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Category, title, and description are required'
      );
    }
    
    // Get user data
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    const userData = userDoc.exists ? userDoc.data() : null;
    
    // Create issue report object
    const issueReport: Omit<IssueReport, 'id'> = {
      userId: auth.uid,
      userName: userData?.name || 'Anonymous',
      category,
      title,
      description,
      status: 'open',
      createdAt: admin.firestore.Timestamp.now()
    };
    
    // Add screenshot URL if provided
    if (screenshotURL) {
      issueReport.screenshotURL = screenshotURL;
    }
    
    // Add to Firestore
    const docRef = await admin.firestore().collection('issues').add(issueReport);
    
    return {
      success: true,
      data: {
        id: docRef.id,
        ...issueReport
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get feedback (admin only)
export const getFeedback = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    // Check if the user is an admin
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can access feedback'
      );
    }
    
    const { limit = 50, lastVisible } = data;
    
    // Query feedback
    let query = admin.firestore()
      .collection('feedback')
      .orderBy('createdAt', 'desc')
      .limit(limit);
    
    // Handle pagination
    if (lastVisible) {
      const lastDoc = await admin.firestore().collection('feedback').doc(lastVisible).get();
      
      if (lastDoc.exists) {
        query = query.startAfter(lastDoc);
      }
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const feedback: Feedback[] = [];
    let lastVisibleId: string | null = null;
    
    snapshot.forEach(doc => {
      feedback.push({
        id: doc.id,
        ...doc.data() as Feedback
      });
      
      // Set the last visible document ID for pagination
      lastVisibleId = doc.id;
    });
    
    return {
      success: true,
      data: {
        feedback,
        lastVisible: lastVisibleId
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get issue reports (admin only)
export const getIssueReports = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    // Check if the user is an admin
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can access issue reports'
      );
    }
    
    const { status, limit = 50, lastVisible } = data;
    
    // Query issues
    let query = admin.firestore()
      .collection('issues')
      .orderBy('createdAt', 'desc');
    
    // Filter by status if provided
    if (status) {
      query = query.where('status', '==', status);
    }
    
    // Apply limit
    query = query.limit(limit);
    
    // Handle pagination
    if (lastVisible) {
      const lastDoc = await admin.firestore().collection('issues').doc(lastVisible).get();
      
      if (lastDoc.exists) {
        query = query.startAfter(lastDoc);
      }
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const issues: IssueReport[] = [];
    let lastVisibleId: string | null = null;
    
    snapshot.forEach(doc => {
      issues.push({
        id: doc.id,
        ...doc.data() as IssueReport
      });
      
      // Set the last visible document ID for pagination
      lastVisibleId = doc.id;
    });
    
    return {
      success: true,
      data: {
        issues,
        lastVisible: lastVisibleId
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Update issue status (admin only)
export const updateIssueStatus = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    // Check if the user is an admin
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can update issue status'
      );
    }
    
    const { issueId, status } = data;
    
    if (!issueId || !status) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Issue ID and status are required'
      );
    }
    
    if (!['open', 'in_progress', 'resolved', 'closed'].includes(status)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid status. Must be one of: open, in_progress, resolved, closed'
      );
    }
    
    // Update the issue
    await admin.firestore().collection('issues').doc(issueId).update({
      status,
      updatedAt: admin.firestore.Timestamp.now()
    });
    
    return { success: true };
  } catch (error) {
    return handleError(error);
  }
});

// Get user's own issue reports
export const getUserIssueReports = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    // Query issues for the current user
    const snapshot = await admin.firestore()
      .collection('issues')
      .where('userId', '==', auth.uid)
      .orderBy('createdAt', 'desc')
      .get();
    
    // Process results
    const issues: IssueReport[] = [];
    
    snapshot.forEach(doc => {
      issues.push({
        id: doc.id,
        ...doc.data() as IssueReport
      });
    });
    
    return {
      success: true,
      data: {
        issues
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Submit complaint
export const submitComplaint = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);

    const { category, priority, subject, description, orderId, attachments } = data;

    if (!category || !subject || !description) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Category, subject, and description are required'
      );
    }

    const validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (priority && !validPriorities.includes(priority)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid priority level'
      );
    }

    // Get user data
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    const userData = userDoc.exists ? userDoc.data() : null;

    // Create complaint object
    const complaint: Omit<Complaint, 'id'> = {
      userId: auth.uid,
      userName: userData?.name || 'Anonymous',
      userEmail: auth.email || '',
      category,
      priority: priority || 'medium',
      subject,
      description,
      orderId: orderId || null,
      attachments: attachments || [],
      status: 'open',
      assignedTo: null,
      resolution: null,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };

    // Add to Firestore
    const docRef = await admin.firestore().collection('complaints').add(complaint);

    return {
      success: true,
      data: {
        id: docRef.id,
        ...complaint
      },
      message: 'Complaint submitted successfully. We will review it and get back to you within 24 hours.'
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get user's complaint history
export const getUserComplaints = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);

    const { limit = 20, offset = 0 } = data;

    const snapshot = await admin.firestore()
      .collection('complaints')
      .where('userId', '==', auth.uid)
      .orderBy('createdAt', 'desc')
      .limit(limit)
      .offset(offset)
      .get();

    const complaints: Complaint[] = [];
    snapshot.forEach(doc => {
      complaints.push({
        id: doc.id,
        ...doc.data() as Complaint
      });
    });

    return { success: true, data: complaints };
  } catch (error) {
    return handleError(error);
  }
});