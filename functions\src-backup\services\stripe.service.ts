
import * as admin from 'firebase-admin';
import Stripe from 'stripe';
import { db } from '../config/firebase';
import { environmentConfig, getEnvironmentConfigSingleton } from '../config/environment';
import {
  Order,
  Listing,
  UserProfile,
  Wallet,
  ConnectAccount,
  SecretCode,
  ShippingLabel,
  ShippingAddress,
  PendingPayoutOrder,
  StripeAccountResponse
} from '../types';
import { Timestamp, FieldValue } from 'firebase-admin/firestore';
import { createShippingLabel as createShippoLabel } from '../stripe/shippo';
import { sendOrderEmail } from '../utils/email';

// Initialize Stripe with the secret key from environment config
const stripe = new Stripe(environmentConfig.stripe.apiKey, {
  apiVersion: '2025-05-28.basil',
});

// App URL will be retrieved from environment config when needed

/**
 * Creates a Stripe Checkout Session for a listing
 * Enhanced to support escrow flow for non-onboarded sellers
 */
export const createCheckoutSession = async (
  listingId: string,
  buyerId: string,
  useWalletBalance: boolean,
  orderDetails?: {
    type: 'buy' | 'rent' | 'bid';
    price?: number;
    period?: 'weekly' | 'monthly';
    bidAmount?: number;
    shippingAddress?: ShippingAddress;
    appliedWalletCredit?: number;
    deliveryMethod?: 'in_person' | 'mail';
    shippingFee?: number;
    shippingPaidBy?: 'buyer' | 'seller';
  }
): Promise<{ sessionId: string; sessionUrl: string }> => {
  try {
    console.log('Creating checkout session for:', { listingId, buyerId, useWalletBalance });
    console.log('Listing ID type:', typeof listingId, 'value:', JSON.stringify(listingId));
    console.log('Buyer ID type:', typeof buyerId, 'value:', JSON.stringify(buyerId));

    // Validate inputs
    if (!listingId || listingId.trim() === '') {
      console.error('Invalid listing ID provided:', listingId);
      throw new Error('Invalid listing ID');
    }

    if (!buyerId || buyerId.trim() === '') {
      console.error('Invalid buyer ID provided:', buyerId);
      throw new Error('Invalid buyer ID');
    }

    // Get the listing data
    const listingDoc = await db.collection('listings').doc(listingId).get();
    if (!listingDoc.exists) {
      throw new Error('Listing not found');
    }

    const listing = {
      id: listingDoc.id,
      ...listingDoc.data()
    } as Listing;

    console.log('Retrieved listing:', {
      id: listing.id,
      title: listing.title,
      price: listing.price,
      ownerId: listing.ownerId,
      status: listing.status
    });

    // Check if listing is available
    if (listing.status !== 'active') {
      throw new Error('Listing is not available for purchase');
    }

    // Validate buyer is not the seller
    if (listing.ownerId === buyerId) {
      throw new Error('You cannot purchase your own listing');
    }

    // Verify buyer exists or create basic user document
    const buyerDoc = await db.collection('users').doc(buyerId).get();
    if (!buyerDoc.exists) {
      console.log('Buyer document not found, creating basic user document for:', buyerId);

      // Get user info from Firebase Auth
      let userRecord;
      try {
        userRecord = await admin.auth().getUser(buyerId);
      } catch (authError) {
        console.error('Error getting user from Firebase Auth:', authError);
        throw new Error('Buyer not found in authentication system');
      }

      // Create basic user document
      const userEmail = userRecord.email || '';
      const basicUserData = {
        uid: buyerId,
        email: userEmail,
        name: userRecord.displayName || (userEmail ? userEmail.split('@')[0] : 'User'),
        role: 'student',
        university: userEmail ? (userEmail.split('@')[1]?.split('.')[0] || 'Unknown') : 'Unknown',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };

      await db.collection('users').doc(buyerId).set(basicUserData);
      console.log('Created basic user document for:', buyerId);
    }

    // Get the seller's Connect account status
    const sellerId = listing.ownerId; // Use ownerId from listing data
    console.log('Seller ID:', sellerId);

    const connectAccountDoc = await db.collection('connectAccounts').doc(sellerId).get();
    let connectAccount: ConnectAccount | null = null;
    let isSellerOnboarded = false;

    if (connectAccountDoc.exists) {
      connectAccount = connectAccountDoc.data() as ConnectAccount;
      isSellerOnboarded = connectAccount.isOnboarded;
    }

    console.log('Seller onboarding status:', { isSellerOnboarded, hasAccount: !!connectAccount });
    
    // Calculate commission rate based on category
    // 8% for textbooks and course materials, 10% for everything else (including Stripe fees)
    const isTextbookOrCourseMaterial = listing.category === 'textbooks' ||
                                      listing.category === 'course-materials';
    const commissionRate = isTextbookOrCourseMaterial ? 0.08 : 0.10;

    // Determine order details
    const orderType = orderDetails?.type || 'buy';
    let basePrice = listing.price;

    // Override price based on order type with validation
    if (orderDetails?.price) {
      if (orderDetails.price <= 0) {
        throw new Error('Invalid price specified');
      }
      basePrice = orderDetails.price;
    } else if (orderType === 'rent' && orderDetails?.period) {
      if (!orderDetails.period || !['weekly', 'monthly'].includes(orderDetails.period)) {
        throw new Error('Invalid rental period specified');
      }
      const rentalPrice = orderDetails.period === 'weekly' ? listing.weeklyPrice : listing.monthlyPrice;
      if (!rentalPrice || rentalPrice <= 0) {
        throw new Error(`${orderDetails.period} rental is not available for this item`);
      }
      basePrice = rentalPrice;
    } else if (orderType === 'bid' && orderDetails?.bidAmount) {
      if (orderDetails.bidAmount <= 0) {
        throw new Error('Invalid bid amount');
      }
      // Validate bid amount against current bid or starting price
      const minimumBid = listing.currentBid ? listing.currentBid + 1 : listing.startingBid || listing.price;
      if (orderDetails.bidAmount < minimumBid) {
        throw new Error(`Bid must be at least $${minimumBid}`);
      }
      basePrice = orderDetails.bidAmount;
    }

    // Final price validation
    if (basePrice <= 0) {
      throw new Error('Invalid item price');
    }

    // Calculate shipping fee based on delivery method and order details
    let shippingFee = 0;
    if (orderDetails?.deliveryMethod === 'mail' && orderDetails?.shippingPaidBy === 'buyer') {
      shippingFee = orderDetails?.shippingFee || 0;
    }
    // If seller pays shipping or in-person delivery, shipping fee is 0 for buyer

    // Calculate amounts
    let amount = basePrice + shippingFee;
    let walletAmountUsed = 0;

    // Apply wallet balance if requested
    if (useWalletBalance && orderDetails?.appliedWalletCredit) {
      const walletDoc = await db.collection('wallets').doc(buyerId).get();
      if (walletDoc.exists) {
        const wallet = walletDoc.data() as Wallet;

        // Validate the applied wallet credit
        const requestedCredit = orderDetails.appliedWalletCredit;

        // Security checks
        if (requestedCredit > 0 && requestedCredit <= wallet.balance && requestedCredit <= amount) {
          // Ensure remaining amount meets Stripe minimum charge requirement
          const remainingAmount = amount - requestedCredit;

          if (remainingAmount >= 0.50 || remainingAmount === 0) {
            walletAmountUsed = requestedCredit;
            amount -= walletAmountUsed;
          } else {
            // Adjust to meet minimum charge requirement
            walletAmountUsed = amount - 0.50;
            amount = 0.50;
          }
        }
      }
    } else if (useWalletBalance) {
      // Fallback to old behavior for backward compatibility
      const walletDoc = await db.collection('wallets').doc(buyerId).get();
      if (walletDoc.exists) {
        const wallet = walletDoc.data() as Wallet;

        // Only use wallet balance if it's available and the remaining amount is at least $0.50
        // (Stripe minimum charge)
        if (wallet.balance > 0) {
          const maxWalletAmount = Math.min(wallet.balance, amount - 0.50);
          if (maxWalletAmount > 0) {
            walletAmountUsed = maxWalletAmount;
            amount -= walletAmountUsed;
          }
        }
      }
    }

    // Calculate cashback amount (2% of base price, not including shipping)
    const cashbackAmount = Math.round(basePrice * 0.02 * 100) / 100;

    // Calculate seller amount (after commission, and shipping if seller pays)
    const commissionAmount = Math.round(basePrice * commissionRate * 100) / 100;
    let sellerAmount = basePrice - commissionAmount;

    // Deduct shipping fee from seller's payout if seller pays for shipping
    if (orderDetails?.deliveryMethod === 'mail' && orderDetails?.shippingPaidBy === 'seller') {
      const sellerShippingFee = orderDetails?.shippingFee || 0;
      sellerAmount = Math.max(0, sellerAmount - sellerShippingFee);
      console.log(`Seller pays shipping: $${sellerShippingFee}, adjusted seller amount: $${sellerAmount}`);
    }

    // Create a new order document
    const orderRef = db.collection('orders').doc();

    // Create line items for checkout
    const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = [
      {
        price_data: {
          currency: 'usd',
          product_data: {
            name: listing.title,
            description: listing.description.substring(0, 255),
            images: (listing.imageURLs || []).slice(0, 8), // Stripe allows up to 8 images
            metadata: {
              listingId: listing.id,
              category: listing.category,
              condition: listing.condition,
              isTextbook: isTextbookOrCourseMaterial ? 'true' : 'false',
              orderType: orderType,
            },
          },
          unit_amount: Math.round(basePrice * 100), // Convert to cents
          tax_behavior: 'exclusive', // Tax will be calculated separately
        },
        quantity: 1,
      },
    ];

    // Add shipping as a separate line item if applicable
    if (shippingFee > 0) {
      lineItems.push({
        price_data: {
          currency: 'usd',
          product_data: {
            name: 'Shipping',
            description: 'Standard shipping fee',
          },
          unit_amount: Math.round(shippingFee * 100),
          tax_behavior: 'exclusive',
        },
        quantity: 1,
      });
    }

    // Create Stripe Checkout Session with tax calculation and enhanced escrow support
    const sessionConfig: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: lineItems,
      // Enable automatic tax calculation
      automatic_tax: {
        enabled: true,
      },
      // Collect customer information for tax calculation
      billing_address_collection: 'required',
      shipping_address_collection: orderDetails?.shippingAddress ? {
        allowed_countries: ['US'], // Expand as needed
      } : undefined,
      metadata: {
        orderId: orderRef.id,
        listingId: listing.id,
        buyerId: buyerId,
        sellerId: sellerId,
        walletAmountUsed: walletAmountUsed.toString(),
        cashbackAmount: cashbackAmount.toString(),
        isTextbook: isTextbookOrCourseMaterial ? 'true' : 'false',
        sellerOnboarded: isSellerOnboarded ? 'true' : 'false',
      },
      mode: 'payment',
      success_url: `${environmentConfig.app.url}/order/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${environmentConfig.app.url}/listing/${listing.id}`,
    };

    // Configure payment intent based on seller onboarding status
    if (isSellerOnboarded && connectAccount) {
      // Seller is onboarded - direct transfer
      sessionConfig.payment_intent_data = {
        application_fee_amount: Math.round(commissionAmount * 100),
        transfer_data: {
          destination: connectAccount.stripeAccountId,
        },
        metadata: {
          orderId: orderRef.id,
          listingId: listing.id,
          buyerId: buyerId,
          sellerId: sellerId,
          walletAmountUsed: walletAmountUsed.toString(),
          cashbackAmount: cashbackAmount.toString(),
          isTextbook: isTextbookOrCourseMaterial ? 'true' : 'false',
          sellerOnboarded: 'true',
        },
      };
    } else {
      // Seller not onboarded - hold funds in escrow
      sessionConfig.payment_intent_data = {
        metadata: {
          orderId: orderRef.id,
          listingId: listing.id,
          buyerId: buyerId,
          sellerId: sellerId,
          walletAmountUsed: walletAmountUsed.toString(),
          cashbackAmount: cashbackAmount.toString(),
          isTextbook: isTextbookOrCourseMaterial ? 'true' : 'false',
          sellerOnboarded: 'false',
          pendingPayout: 'true',
          commissionAmount: commissionAmount.toString(),
          sellerAmount: sellerAmount.toString(),
        },
      };
    }

    const session = await stripe.checkout.sessions.create(sessionConfig);
    
    // Create the order in Firestore with escrow support
    const orderData: any = {
      id: orderRef.id,
      listingId: listing.id,
      buyerId: buyerId,
      sellerId: sellerId,
      title: listing.title,
      description: listing.description,
      amount: basePrice, // Base price without shipping
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      sellerAmount: sellerAmount,
      cashbackAmount: cashbackAmount,
      walletAmountUsed: walletAmountUsed,
      stripeSessionId: session.id,
      status: 'payment_pending',
      isTextbook: isTextbookOrCourseMaterial,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),

      // Add delivery method and shipping information
      deliveryMethod: orderDetails?.deliveryMethod || (listing as any).deliveryMethod || 'in_person',
      shippingFee: shippingFee,
      shippingPaidBy: orderDetails?.shippingPaidBy || (listing as any).shippingOptions?.paidBy || 'buyer',
      // Enhanced escrow fields
      sellerOnboarded: isSellerOnboarded,
      pendingPayout: !isSellerOnboarded,
      stripeAccountId: connectAccount?.stripeAccountId || null,
      // New order fields
      orderType: orderType,
    };

    // Only add optional fields if they have values (to avoid Firestore undefined errors)
    if (orderDetails?.period !== undefined) {
      orderData.rentalPeriod = orderDetails.period;
    }
    if (orderDetails?.bidAmount !== undefined) {
      orderData.bidAmount = orderDetails.bidAmount;
    }
    if (orderDetails?.shippingAddress !== undefined) {
      orderData.shippingAddress = orderDetails.shippingAddress;
    }
    if (orderDetails?.shippingAddress !== undefined) {
      orderData.shippingAddress = orderDetails.shippingAddress;
    }

    await orderRef.set(orderData);
    
    // If using wallet balance, update the wallet
    if (walletAmountUsed > 0) {
      await db.collection('wallets').doc(buyerId).update({
        balance: FieldValue.increment(-walletAmountUsed),
        lastUpdated: Timestamp.now(),
      });
    }
    
    // Don't update listing status here - only update when payment actually succeeds
    // The listing should remain 'active' until payment is confirmed
    console.log(`Checkout session created for listing ${listingId}, keeping status as 'active'`);
    
    return {
      sessionId: session.id,
      sessionUrl: session.url || '',
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
};

/**
 * Creates a Stripe Connect account for a seller (Firebase Callable Function)
 */
export const createStripeAccount = async (
  userId: string,
  accountType: 'student' | 'merchant'
): Promise<{ accountId: string; onboardingUrl: string }> => {
  try {
    // Get the user data
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new Error('User not found');
    }

    const user = userDoc.data() as UserProfile;

    // Check if user already has a Connect account
    const existingAccountDoc = await db.collection('connectAccounts').doc(userId).get();
    if (existingAccountDoc.exists) {
      const existingAccount = existingAccountDoc.data() as ConnectAccount;

      // If account exists but not onboarded, create a new account link
      if (!existingAccount.isOnboarded) {
        const accountLink = await stripe.accountLinks.create({
          account: existingAccount.stripeAccountId,
          refresh_url: `${environmentConfig.app.url}/settings/payment?refresh=true`,
          return_url: `${environmentConfig.app.url}/settings/payment?success=true`,
          type: 'account_onboarding',
        });

        // Update the onboarding URL in Firestore
        await db.collection('connectAccounts').doc(userId).update({
          onboardingUrl: accountLink.url,
          updatedAt: Timestamp.now(),
        });

        return {
          accountId: existingAccount.stripeAccountId,
          onboardingUrl: accountLink.url,
        };
      }

      // If account is already onboarded, return success with a dashboard link
      const config = getEnvironmentConfigSingleton();
      return {
        accountId: existingAccount.stripeAccountId,
        onboardingUrl: `${config.app.url}/settings/payment?already_setup=true`,
      };
    }

    // Create a new Stripe Connect account
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US',
      email: user.email,
      business_type: accountType === 'student' ? 'individual' : 'company',
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      metadata: {
        userId: userId,
        accountType: accountType,
      },
    });

    // Create an account link for onboarding
    const config = getEnvironmentConfigSingleton();
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${config.app.url}/settings/payment?refresh=true`,
      return_url: `${config.app.url}/settings/payment?success=true`,
      type: 'account_onboarding',
    });

    // Store the Connect account in Firestore
    await db.collection('connectAccounts').doc(userId).set({
      userId: userId,
      stripeAccountId: account.id,
      accountType: accountType,
      isOnboarded: false,
      chargesEnabled: false,
      payoutsEnabled: false,
      detailsSubmitted: false,
      onboardingUrl: accountLink.url,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    } as ConnectAccount);

    return {
      accountId: account.id,
      onboardingUrl: accountLink.url,
    };
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    throw error;
  }
};

/**
 * Gets Stripe onboarding link for existing account (Firebase Callable Function)
 */
export const getStripeOnboardingLink = async (
  userId: string,
  refreshUrl?: string,
  returnUrl?: string
): Promise<{ onboardingUrl: string }> => {
  try {
    const connectAccountDoc = await db.collection('connectAccounts').doc(userId).get();
    if (!connectAccountDoc.exists) {
      throw new Error('No Stripe account found for user');
    }

    const connectAccount = connectAccountDoc.data() as ConnectAccount;

    if (connectAccount.isOnboarded) {
      throw new Error('Account is already fully onboarded');
    }

    // Create a new account link
    const config = getEnvironmentConfigSingleton();
    const accountLink = await stripe.accountLinks.create({
      account: connectAccount.stripeAccountId,
      refresh_url: refreshUrl || `${config.app.url}/settings/payment?refresh=true`,
      return_url: returnUrl || `${config.app.url}/settings/payment?success=true`,
      type: 'account_onboarding',
    });

    // Update the onboarding URL in Firestore
    await db.collection('connectAccounts').doc(userId).update({
      onboardingUrl: accountLink.url,
      updatedAt: Timestamp.now(),
    });

    return {
      onboardingUrl: accountLink.url,
    };
  } catch (error) {
    console.error('Error getting onboarding link:', error);
    throw error;
  }
};

/**
 * Creates a Stripe Connect account for a seller (Legacy function for backward compatibility)
 */
export const createConnectAccount = async (
  userId: string,
  accountType: 'student' | 'merchant'
): Promise<{ accountId: string; accountLinkUrl: string }> => {
  try {
    // Get the user data
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new Error('User not found');
    }
    
    const user = userDoc.data() as UserProfile;
    
    // Check if user already has a Connect account
    const existingAccountDoc = await db.collection('connectAccounts').doc(userId).get();
    if (existingAccountDoc.exists) {
      const existingAccount = existingAccountDoc.data() as ConnectAccount;
      
      // If account exists but not onboarded, create a new account link
      if (!existingAccount.isOnboarded) {
        const config = getEnvironmentConfigSingleton();
        const accountLink = await stripe.accountLinks.create({
          account: existingAccount.stripeAccountId,
          refresh_url: `${config.app.url}/settings/payment?refresh=true`,
          return_url: `${config.app.url}/settings/payment?success=true`,
          type: 'account_onboarding',
        });
        
        return {
          accountId: existingAccount.stripeAccountId,
          accountLinkUrl: accountLink.url,
        };
      }
      
      throw new Error('User already has a Connect account');
    }
    
    // Create a new Stripe Connect account
    let account;
    try {
      account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: user.email,
        business_type: accountType === 'student' ? 'individual' : 'company',
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        metadata: {
          userId: userId,
          accountType: accountType,
        },
      });
    } catch (stripeError: any) {
      console.error('Stripe Connect account creation failed:', stripeError);

      // Check if it's a Connect not enabled error
      if (stripeError.message?.includes('signed up for Connect')) {
        console.log('Creating mock Connect account for testing purposes');

        // Create a mock Connect account in Firestore for testing
        const mockAccountId = `acct_mock_${userId}_${Date.now()}`;
        const mockConnectAccount = {
          userId: userId,
          stripeAccountId: mockAccountId,
          isOnboarded: false,
          chargesEnabled: false,
          payoutsEnabled: false,
          detailsSubmitted: false,
          onboardingUrl: `${environmentConfig.app.url}/settings/payment?mock=true`,
          dashboardUrl: `${environmentConfig.app.url}/settings/payment?mock=true`,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
          isMockAccount: true, // Flag to identify mock accounts
        };

        await db.collection('connectAccounts').doc(userId).set(mockConnectAccount);

        return {
          accountId: mockAccountId,
          accountLinkUrl: `${environmentConfig.app.url}/settings/payment?mock=true&message=Stripe Connect setup will be available soon. You can still create listings!`,
        };
      }

      // Re-throw other Stripe errors
      throw stripeError;
    }
    
    // Create an account link for onboarding
    const config = getEnvironmentConfigSingleton();
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${config.app.url}/settings/payment?refresh=true`,
      return_url: `${config.app.url}/settings/payment?success=true`,
      type: 'account_onboarding',
    });
    
    // Store the Connect account in Firestore
    await db.collection('connectAccounts').doc(userId).set({
      userId: userId,
      stripeAccountId: account.id,
      accountType: accountType,
      isOnboarded: false,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    } as ConnectAccount);
    
    return {
      accountId: account.id,
      accountLinkUrl: accountLink.url,
    };
  } catch (error) {
    console.error('Error creating Connect account:', error);
    throw error;
  }
};

/**
 * Handles the Stripe webhook events
 */
export const handleStripeWebhook = async (
  event: Stripe.Event
): Promise<void> => {
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
        
      case 'account.updated':
        await handleAccountUpdated(event.data.object as Stripe.Account);
        break;
        
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error(`Error handling webhook ${event.type}:`, error);
    throw error;
  }
};

/**
 * Handles the payment_intent.succeeded event
 */
const handlePaymentIntentSucceeded = async (
  paymentIntent: Stripe.PaymentIntent
): Promise<void> => {
  try {
    const { orderId, listingId, buyerId, sellerId, cashbackAmount, sellerOnboarded } = paymentIntent.metadata;

    if (!orderId) {
      throw new Error('Order ID not found in payment intent metadata');
    }

    // Get the order to check current status
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }

    const order = orderDoc.data() as Order;

    // Update the order status with escrow information
    const updateData: any = {
      status: 'payment_succeeded',
      stripePaymentIntentId: paymentIntent.id,
      updatedAt: Timestamp.now(),
    };

    // Set escrow release date (3 days from now)
    updateData.escrowReleaseDate = Timestamp.fromDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000));

    await db.collection('orders').doc(orderId).update(updateData);

    // Update the listing status
    await db.collection('listings').doc(listingId).update({
      status: 'sold',
      updatedAt: Timestamp.now(),
    });

    // Generate a 6-digit secret code for delivery confirmation
    const secretCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Store the secret code in Firestore
    await db.collection('codes').doc(orderId).set({
      orderId: orderId,
      code: secretCode,
      expiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days
      isUsed: false,
      createdAt: Timestamp.now(),
    } as SecretCode);

    // Send checkout confirmation email to buyer
    try {
      const buyerDoc = await db.collection('users').doc(buyerId).get();
      const sellerDoc = await db.collection('users').doc(sellerId).get();

      if (buyerDoc.exists && sellerDoc.exists) {
        const buyer = buyerDoc.data() as UserProfile;
        const seller = sellerDoc.data() as UserProfile;

        await sendOrderEmail('checkout_confirmation', buyer.email, {
          orderId: orderId,
          buyerName: buyer.displayName,
          sellerName: seller.displayName,
          itemTitle: order.title,
          amount: order.amount
        });
      }
    } catch (emailError) {
      console.error('Error sending checkout confirmation email:', emailError);
    }

    // Generate a shipping label automatically
    try {
      await generateShippingLabel(orderId);
    } catch (shippingError) {
      console.error('Error generating shipping label:', shippingError);
      // Don't fail the entire payment process if shipping label generation fails
    }

    // Deduct wallet credit now that payment is successful
    const walletAmountUsed = parseFloat(paymentIntent.metadata.walletAmountUsed || '0');
    if (walletAmountUsed > 0) {
      try {
        const walletRef = db.collection('wallets').doc(buyerId);
        const walletDoc = await walletRef.get();

        if (walletDoc.exists) {
          const currentWallet = walletDoc.data() as Wallet;

          // Validate that the user has sufficient balance
          if (currentWallet.balance >= walletAmountUsed) {
            // Create transaction record
            const transaction = {
              id: db.collection('temp').doc().id,
              type: 'debit',
              amount: walletAmountUsed,
              description: `Purchase: ${order.title}`,
              source: 'purchase_deduction',
              orderId: orderId,
              createdAt: Timestamp.now()
            };

            // Update wallet with deduction and transaction history
            await walletRef.update({
              balance: FieldValue.increment(-walletAmountUsed),
              history: FieldValue.arrayUnion(transaction),
              lastUpdated: Timestamp.now()
            });

            console.log(`Deducted $${walletAmountUsed} from wallet for order ${orderId}`);
          } else {
            console.error(`Insufficient wallet balance for user ${buyerId}. Required: $${walletAmountUsed}, Available: $${currentWallet.balance}`);
          }
        } else {
          console.error(`Wallet not found for user ${buyerId} when trying to deduct $${walletAmountUsed}`);
        }
      } catch (walletError) {
        console.error('Error deducting wallet credit:', walletError);
        // Don't fail the entire payment process if wallet deduction fails
      }
    }

    // Add cashback to buyer's wallet (2% of purchase amount)
    if (cashbackAmount && parseFloat(cashbackAmount) > 0) {
      const walletRef = db.collection('wallets').doc(buyerId);
      const walletDoc = await walletRef.get();

      if (walletDoc.exists) {
        // Update existing wallet
        await walletRef.update({
          balance: FieldValue.increment(parseFloat(cashbackAmount)),
          lastUpdated: Timestamp.now(),
        });
      } else {
        // Create new wallet
        await walletRef.set({
          userId: buyerId,
          balance: parseFloat(cashbackAmount),
          lastUpdated: Timestamp.now(),
        } as Wallet);
      }
    }

    // If seller is not onboarded, add to pending payouts for escrow
    if (sellerOnboarded !== 'true') {
      await db.collection('pendingPayouts').doc(orderId).set({
        orderId: orderId,
        sellerId: sellerId,
        amount: order.sellerAmount,
        commissionAmount: order.commissionAmount,
        status: 'pending_delivery_confirmation',
        createdAt: Timestamp.now(),
        escrowReleaseDate: updateData.escrowReleaseDate,
      });
    }

    console.log('Payment processed successfully for order:', orderId);
  } catch (error) {
    console.error('Error handling payment_intent.succeeded:', error);
    throw error;
  }
};

/**
 * Handles the account.updated event with enhanced escrow support
 */
const handleAccountUpdated = async (
  account: Stripe.Account
): Promise<void> => {
  try {
    const userId = account.metadata?.userId;

    if (!userId) {
      console.log('User ID not found in account metadata');
      return;
    }

    const wasOnboarded = await checkIfAccountWasOnboarded(userId);

    // Check if the account is now fully onboarded
    const isNowOnboarded = account.details_submitted &&
                          account.charges_enabled &&
                          account.payouts_enabled;

    // Update the Connect account in Firestore
    await db.collection('connectAccounts').doc(userId).update({
      isOnboarded: isNowOnboarded,
      chargesEnabled: account.charges_enabled || false,
      payoutsEnabled: account.payouts_enabled || false,
      detailsSubmitted: account.details_submitted || false,
      updatedAt: Timestamp.now(),
      ...(isNowOnboarded && !wasOnboarded && { onboardedAt: Timestamp.now() }),
    });

    // If seller just completed onboarding, process pending payouts
    if (isNowOnboarded && !wasOnboarded) {
      console.log(`Seller ${userId} completed onboarding, processing pending payouts`);
      await processPendingPayouts(userId);
    }
  } catch (error) {
    console.error('Error handling account.updated:', error);
    throw error;
  }
};

/**
 * Checks if account was previously onboarded
 */
const checkIfAccountWasOnboarded = async (userId: string): Promise<boolean> => {
  try {
    const connectAccountDoc = await db.collection('connectAccounts').doc(userId).get();
    if (!connectAccountDoc.exists) {
      return false;
    }

    const connectAccount = connectAccountDoc.data() as ConnectAccount;
    return connectAccount.isOnboarded || false;
  } catch (error) {
    console.error('Error checking account onboarding status:', error);
    return false;
  }
};

/**
 * Releases funds to the seller using the secret code
 */
export const releaseFundsWithCode = async (
  orderId: string,
  code: string
): Promise<boolean> => {
  try {
    // Get the secret code
    const codeDoc = await db.collection('codes').doc(orderId).get();
    if (!codeDoc.exists) {
      throw new Error('Secret code not found');
    }
    
    const secretCode = codeDoc.data() as SecretCode;
    
    // Check if the code is valid
    if (secretCode.isUsed) {
      throw new Error('Secret code has already been used');
    }
    
    if (secretCode.expiresAt.toDate() < new Date()) {
      throw new Error('Secret code has expired');
    }
    
    if (secretCode.code !== code) {
      throw new Error('Invalid secret code');
    }
    
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }
    
    const order = orderDoc.data() as Order;
    
    // Check if the order is in the correct status
    if (!['shipped_pending_code', 'payment_succeeded'].includes(order.status)) {
      throw new Error('Order is not in the correct status for code verification');
    }

    // Mark the code as used
    await db.collection('codes').doc(orderId).update({
      isUsed: true,
      usedAt: Timestamp.now(),
    });

    // Release funds to seller
    await releaseFundsToSeller(order);

    // Update the order status
    await db.collection('orders').doc(orderId).update({
      status: 'completed',
      updatedAt: Timestamp.now(),
      completedAt: Timestamp.now(),
      releasedAt: Timestamp.now(),
    });

    // Remove from pending payouts if exists
    await db.collection('pendingPayouts').doc(orderId).delete();

    return true;
  } catch (error) {
    console.error('Error releasing funds with code:', error);
    throw error;
  }
};

/**
 * Helper function to release funds to seller
 */
const releaseFundsToSeller = async (order: Order): Promise<void> => {
  try {
    // Check if seller is onboarded
    if (order.sellerOnboarded && order.stripeAccountId) {
      // Transfer funds to seller's connected account
      await stripe.transfers.create({
        amount: Math.round(order.sellerAmount * 100), // Convert to cents
        currency: 'usd',
        destination: order.stripeAccountId,
        metadata: {
          orderId: order.id,
          sellerId: order.sellerId,
          type: 'escrow_release'
        }
      });

      console.log(`Funds released to seller ${order.sellerId} for order ${order.id}`);
    } else {
      // Seller not onboarded - keep funds in escrow until they onboard
      console.log(`Seller ${order.sellerId} not onboarded - funds remain in escrow for order ${order.id}`);

      // Update pending payout status
      await db.collection('pendingPayouts').doc(order.id).update({
        status: 'ready_for_payout',
        deliveryConfirmedAt: Timestamp.now(),
      });
    }
  } catch (error) {
    console.error('Error releasing funds to seller:', error);
    throw error;
  }
};

/**
 * Automatically releases funds to the seller after the escrow period
 */
export const autoReleaseFunds = async (
  orderId: string
): Promise<boolean> => {
  try {
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }
    
    const order = orderDoc.data() as Order;
    
    // Check if the order is in the correct status
    if (!['shipped_pending_code', 'payment_succeeded'].includes(order.status)) {
      throw new Error('Order is not in the correct status for auto-release');
    }

    // Check if the escrow period has passed (3 days)
    const escrowReleaseDate = order.escrowReleaseDate?.toDate();
    const now = new Date();

    if (!escrowReleaseDate || now < escrowReleaseDate) {
      throw new Error('Escrow period has not passed yet');
    }

    // Release funds to seller
    await releaseFundsToSeller(order);

    // Update the order status
    await db.collection('orders').doc(orderId).update({
      status: 'completed',
      updatedAt: Timestamp.now(),
      completedAt: Timestamp.now(),
      releasedAt: Timestamp.now(),
    });

    // Mark the code as used (auto-released)
    await db.collection('codes').doc(orderId).update({
      isUsed: true,
      usedAt: Timestamp.now(),
    });

    // Remove from pending payouts if exists
    await db.collection('pendingPayouts').doc(orderId).delete();
    // For now, we'll just mark the order as completed
    
    return true;
  } catch (error) {
    console.error('Error auto-releasing funds:', error);
    throw error;
  }
};

/**
 * Generates a shipping label for an order using Shippo integration
 */
export const generateShippingLabel = async (
  orderId: string
): Promise<{ trackingNumber: string; labelUrl: string }> => {
  try {
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }

    const order = orderDoc.data() as Order;

    // Check if order has shipping address
    if (!order.shippingAddress) {
      throw new Error('Order does not have a shipping address');
    }

    // Use Shippo integration to create real shipping label
    const shippingLabel = await createShippoLabel(orderId);

    // Update the order with tracking info
    await db.collection('orders').doc(orderId).update({
      status: 'shipped_pending_code',
      trackingInfo: {
        carrier: shippingLabel.carrier,
        trackingNumber: shippingLabel.trackingNumber,
        trackingUrl: `https://tools.usps.com/go/TrackConfirmAction?tLabels=${shippingLabel.trackingNumber}`,
        shippedDate: Timestamp.now(),
        estimatedDeliveryDate: Timestamp.fromDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)), // 3 days
      },
      updatedAt: Timestamp.now(),
    });

    return {
      trackingNumber: shippingLabel.trackingNumber,
      labelUrl: shippingLabel.labelUrl,
    };
  } catch (error) {
    console.error('Error generating shipping label:', error);

    // Fallback to mock implementation if Shippo fails
    console.log('Falling back to mock shipping label generation');

    const trackingNumber = `HC${Math.floor(10000000 + Math.random() * 90000000)}`;
    const labelUrl = `https://hivecampus.app/api/shipping/labels/${trackingNumber}`;

    // Store the mock shipping label in Firestore
    await db.collection('shippingLabels').doc(orderId).set({
      orderId: orderId,
      carrier: 'Hive Campus Delivery',
      trackingNumber: trackingNumber,
      labelUrl: labelUrl,
      createdAt: Timestamp.now(),
    } as ShippingLabel);

    // Update the order with mock tracking info
    await db.collection('orders').doc(orderId).update({
      status: 'shipped_pending_code',
      trackingInfo: {
        carrier: 'Hive Campus Delivery',
        trackingNumber: trackingNumber,
        trackingUrl: `https://hivecampus.app/tracking/${trackingNumber}`,
        shippedDate: Timestamp.now(),
        estimatedDeliveryDate: Timestamp.fromDate(new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)), // 2 days
      },
      updatedAt: Timestamp.now(),
    });

    return {
      trackingNumber,
      labelUrl,
    };
  }
};

/**
 * Processes pending payouts for a newly onboarded seller
 */
export const processPendingPayouts = async (
  sellerId: string
): Promise<void> => {
  try {
    console.log(`Processing pending payouts for seller: ${sellerId}`);

    // Get the seller's Connect account
    const connectAccountDoc = await db.collection('connectAccounts').doc(sellerId).get();
    if (!connectAccountDoc.exists) {
      throw new Error('Connect account not found');
    }

    const connectAccount = connectAccountDoc.data() as ConnectAccount;
    if (!connectAccount.isOnboarded) {
      throw new Error('Seller is not fully onboarded');
    }

    // Get all orders with pending payouts for this seller
    const ordersSnapshot = await db
      .collection('orders')
      .where('sellerId', '==', sellerId)
      .where('pendingPayout', '==', true)
      .where('status', '==', 'payment_succeeded')
      .get();

    if (ordersSnapshot.empty) {
      console.log(`No pending payouts found for seller: ${sellerId}`);
      return;
    }

    console.log(`Found ${ordersSnapshot.docs.length} pending payouts for seller: ${sellerId}`);

    // Process each pending payout
    const promises = ordersSnapshot.docs.map(async (orderDoc) => {
      try {
        const order = orderDoc.data() as Order;
        await releasePendingPayout(order);
      } catch (error) {
        console.error(`Error processing payout for order ${orderDoc.id}:`, error);
      }
    });

    await Promise.all(promises);
    console.log(`Completed processing pending payouts for seller: ${sellerId}`);
  } catch (error) {
    console.error('Error processing pending payouts:', error);
    throw error;
  }
};

/**
 * Releases a pending payout by creating a Stripe transfer
 */
const releasePendingPayout = async (order: Order): Promise<void> => {
  try {
    if (!order.stripePaymentIntentId) {
      throw new Error('No payment intent ID found for order');
    }

    if (!order.stripeAccountId) {
      throw new Error('No Stripe account ID found for order');
    }

    // Create a transfer to the seller's Connect account
    const transfer = await stripe.transfers.create({
      amount: Math.round(order.sellerAmount * 100), // Convert to cents
      currency: 'usd',
      destination: order.stripeAccountId,
      source_transaction: order.stripePaymentIntentId,
      metadata: {
        orderId: order.id,
        sellerId: order.sellerId,
        originalAmount: order.amount.toString(),
        commissionAmount: order.commissionAmount.toString(),
      },
    });

    // Update the order to mark payout as released
    await db.collection('orders').doc(order.id).update({
      pendingPayout: false,
      stripeTransferId: transfer.id,
      payoutReleasedAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });

    console.log(`Released payout for order ${order.id}: ${transfer.id}`);
  } catch (error) {
    console.error(`Error releasing payout for order ${order.id}:`, error);
    throw error;
  }
};

/**
 * Gets pending payouts for a seller
 */
export const getPendingPayouts = async (
  sellerId: string
): Promise<PendingPayoutOrder[]> => {
  try {
    // Validate sellerId
    if (!sellerId || typeof sellerId !== 'string') {
      console.error('Invalid sellerId provided to getPendingPayouts:', sellerId);
      return [];
    }

    const ordersSnapshot = await db
      .collection('orders')
      .where('sellerId', '==', sellerId)
      .where('pendingPayout', '==', true)
      .where('status', '==', 'payment_succeeded')
      .limit(50) // Limit to prevent large queries
      .get();

    const pendingPayouts: PendingPayoutOrder[] = [];

    ordersSnapshot.docs.forEach((doc) => {
      try {
        const order = doc.data() as Order;

        // Validate order data
        if (!order.id || !order.amount || !order.sellerAmount) {
          console.warn('Invalid order data found:', doc.id, order);
          return;
        }

        pendingPayouts.push({
          orderId: order.id,
          amount: order.amount,
          commissionAmount: order.commissionAmount || 0,
          sellerAmount: order.sellerAmount,
          paymentIntentId: order.stripePaymentIntentId || '',
          createdAt: order.createdAt,
        });
      } catch (error) {
        console.error('Error processing order document:', doc.id, error);
      }
    });

    return pendingPayouts;
  } catch (error) {
    console.error('Error getting pending payouts:', error);
    // Return empty array instead of throwing to prevent cascading failures
    return [];
  }
};

/**
 * Gets Stripe account status for a user
 */
export const getStripeAccountStatus = async (
  userId: string
): Promise<StripeAccountResponse | null> => {
  try {
    // Validate userId
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getStripeAccountStatus:', userId);
      return null;
    }

    const connectAccountDoc = await db.collection('connectAccounts').doc(userId).get();

    if (!connectAccountDoc.exists) {
      console.log(`No connect account found for user: ${userId}`);
      return null;
    }

    const connectAccount = connectAccountDoc.data() as ConnectAccount;

    // Validate connect account data
    if (!connectAccount.stripeAccountId) {
      console.error('Connect account missing stripeAccountId:', connectAccount);
      return null;
    }

    // Get fresh data from Stripe with timeout (skip for mock accounts)
    let stripeAccount: Stripe.Account | null = null;
    if (!connectAccount.isMockAccount) {
      try {
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Stripe API timeout')), 10000); // 10 second timeout
        });

        stripeAccount = await Promise.race([
          stripe.accounts.retrieve(connectAccount.stripeAccountId),
          timeoutPromise
        ]);
      } catch (error) {
        console.error('Error retrieving Stripe account (using cached data):', error);
        // Continue with cached data instead of failing
      }
    } else {
      console.log('Skipping Stripe API call for mock account');
    }

    return {
      accountId: connectAccount.stripeAccountId,
      onboardingUrl: connectAccount.onboardingUrl,
      dashboardUrl: connectAccount.dashboardUrl,
      isOnboarded: connectAccount.isOnboarded,
      chargesEnabled: stripeAccount?.charges_enabled || connectAccount.chargesEnabled || false,
      payoutsEnabled: stripeAccount?.payouts_enabled || connectAccount.payoutsEnabled || false,
    };
  } catch (error) {
    console.error('Error getting Stripe account status:', error);
    // Return null instead of throwing to prevent cascading failures
    return null;
  }
};

/**
 * Gets the wallet balance for a user
 */
export const getWalletBalance = async (
  userId: string
): Promise<number> => {
  try {
    const walletDoc = await db.collection('wallets').doc(userId).get();

    if (!walletDoc.exists) {
      return 0;
    }

    const wallet = walletDoc.data() as Wallet;
    return wallet.balance;
  } catch (error) {
    console.error('Error getting wallet balance:', error);
    throw error;
  }
};