import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  CreditCard,
  DollarSign,
  Clock,
  Info
} from 'lucide-react';

interface OnboardingStatusBadgeProps {
  userId: string;
  isOnboarded: boolean;
  pendingAmount?: number;
  orderCount?: number;
  onSetupClick?: () => void;
  variant?: 'compact' | 'detailed' | 'tooltip';
  className?: string;
}

const OnboardingStatusBadge: React.FC<OnboardingStatusBadgeProps> = ({
  userId: _userId,
  isOnboarded,
  pendingAmount = 0,
  orderCount = 0,
  onSetupClick,
  variant = 'compact',
  className = ''
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  if (isOnboarded) {
    // User is fully onboarded - show success badge
    if (variant === 'compact') {
      return (
        <div className={`inline-flex items-center space-x-1 px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded-full text-xs font-medium ${className}`}>
          <CheckCircle className="w-3 h-3" />
          <span>Payouts Active</span>
        </div>
      );
    }

    if (variant === 'detailed') {
      return (
        <div className={`bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 ${className}`}>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="font-semibold text-green-800 dark:text-green-200">
                Payouts Active
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">
                Your payment account is set up and ready
              </p>
            </div>
          </div>
        </div>
      );
    }

    return null; // No tooltip needed for onboarded users
  }

  // User is not onboarded - show warning/setup badge
  if (variant === 'compact') {
    return (
      <div className={`inline-flex items-center space-x-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded-full text-xs font-medium ${className}`}>
        <AlertTriangle className="w-3 h-3" />
        <span>Setup Required</span>
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={`bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-4 ${className}`}>
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <p className="font-semibold text-yellow-800 dark:text-yellow-200">
                Payout Setup Incomplete
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
                Set up Stripe to receive money from your listings
              </p>
              
              {pendingAmount > 0 && (
                <div className="flex items-center space-x-2 mb-3">
                  <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    ${pendingAmount.toFixed(2)} waiting from {orderCount} {orderCount === 1 ? 'sale' : 'sales'}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-4">
          <button
            onClick={onSetupClick}
            className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
          >
            <CreditCard className="w-4 h-4" />
            <span>Set Up Payouts</span>
          </button>
        </div>
      </div>
    );
  }

  if (variant === 'tooltip') {
    return (
      <div className="relative">
        <button
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          onClick={onSetupClick}
          className={`inline-flex items-center space-x-1 px-3 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded-full text-sm font-medium hover:bg-yellow-200 dark:hover:bg-yellow-900/30 transition-colors ${className}`}
        >
          <AlertTriangle className="w-4 h-4" />
          <span>Setup Payouts</span>
          <Info className="w-3 h-3" />
        </button>

        {showTooltip && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg p-3 shadow-lg z-10">
            <div className="space-y-2">
              <p className="font-medium">Set up Stripe to receive payments</p>
              <p className="text-gray-300 dark:text-gray-400">
                Complete your payout setup to receive money from your listings.
              </p>
              
              {pendingAmount > 0 && (
                <div className="bg-yellow-600/20 rounded p-2 mt-2">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4 text-yellow-400" />
                    <span className="text-yellow-200 font-medium">
                      ${pendingAmount.toFixed(2)} waiting
                    </span>
                  </div>
                  <p className="text-xs text-yellow-300 mt-1">
                    From {orderCount} recent {orderCount === 1 ? 'sale' : 'sales'}
                  </p>
                </div>
              )}
            </div>
            
            {/* Tooltip arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
          </div>
        )}
      </div>
    );
  }

  return null;
};

export default OnboardingStatusBadge;
