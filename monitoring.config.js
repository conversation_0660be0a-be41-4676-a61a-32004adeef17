// Monitoring configuration for different environments
module.exports = {
  development: {
    enableErrorTracking: true,
    enablePerformanceTracking: true,
    enableUserTracking: false, // Disable in dev to avoid noise
    enableAPIMonitoring: true,
    enableCustomMetrics: true,
    sampleRate: 1.0,
    environment: 'development',
    logLevel: 'debug',
    alerts: {
      enabled: false, // Disable alerts in development
      webhookUrl: null,
    },
  },

  staging: {
    enableErrorTracking: true,
    enablePerformanceTracking: true,
    enableUserTracking: true,
    enableAPIMonitoring: true,
    enableCustomMetrics: true,
    sampleRate: 1.0,
    environment: 'staging',
    logLevel: 'info',
    alerts: {
      enabled: true,
      webhookUrl: process.env.VITE_STAGING_ALERT_WEBHOOK_URL,
      channels: ['slack'],
    },
  },

  production: {
    enableErrorTracking: true,
    enablePerformanceTracking: true,
    enableUserTracking: true,
    enableAPIMonitoring: true,
    enableCustomMetrics: true,
    sampleRate: 0.1, // Sample 10% of traffic in production
    environment: 'production',
    logLevel: 'error',
    alerts: {
      enabled: true,
      webhookUrl: process.env.VITE_ALERT_WEBHOOK_URL,
      channels: ['slack', 'email', 'pagerduty'],
      escalation: {
        enabled: true,
        timeoutMinutes: 15,
      },
    },
  },

  // Performance thresholds
  thresholds: {
    performance: {
      lcp: 2500, // Largest Contentful Paint (ms)
      fid: 100,  // First Input Delay (ms)
      cls: 0.1,  // Cumulative Layout Shift (score)
      ttfb: 600, // Time to First Byte (ms)
      fcp: 1800, // First Contentful Paint (ms)
    },
    
    errors: {
      errorRate: 5,        // Error rate percentage
      responseTime: 2000,  // API response time (ms)
      memoryUsage: 80,     // Memory usage percentage
      crashRate: 1,        // Crash rate percentage
    },
    
    business: {
      conversionRate: 10,  // Minimum conversion rate percentage
      bounceRate: 60,      // Maximum bounce rate percentage
      sessionDuration: 120, // Minimum session duration (seconds)
    },
  },

  // Alert configurations
  alertConfigurations: [
    {
      name: 'High Error Rate',
      metric: 'error_rate',
      condition: 'above',
      threshold: 5,
      severity: 'high',
      description: 'Error rate exceeded 5%',
      cooldown: 300, // 5 minutes
    },
    {
      name: 'Poor Performance',
      metric: 'lcp',
      condition: 'above',
      threshold: 4000,
      severity: 'medium',
      description: 'LCP exceeded 4 seconds',
      cooldown: 600, // 10 minutes
    },
    {
      name: 'High Memory Usage',
      metric: 'memory_usage',
      condition: 'above',
      threshold: 85,
      severity: 'high',
      description: 'Memory usage exceeded 85%',
      cooldown: 180, // 3 minutes
    },
    {
      name: 'API Response Time',
      metric: 'response_time',
      condition: 'above',
      threshold: 3000,
      severity: 'medium',
      description: 'API response time exceeded 3 seconds',
      cooldown: 300, // 5 minutes
    },
    {
      name: 'Zero Active Users',
      metric: 'active_users',
      condition: 'equals',
      threshold: 0,
      severity: 'low',
      description: 'No active users detected',
      cooldown: 1800, // 30 minutes
    },
  ],

  // Monitoring integrations
  integrations: {
    sentry: {
      enabled: true,
      dsn: process.env.VITE_SENTRY_DSN,
      tracesSampleRate: 0.1,
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
    },
    
    googleAnalytics: {
      enabled: true,
      measurementId: process.env.VITE_GA_MEASUREMENT_ID,
    },
    
    hotjar: {
      enabled: false,
      siteId: process.env.VITE_HOTJAR_SITE_ID,
    },
    
    fullstory: {
      enabled: false,
      orgId: process.env.VITE_FULLSTORY_ORG_ID,
    },
  },

  // Dashboard configuration
  dashboard: {
    refreshInterval: 30000, // 30 seconds
    metricsRetention: 7, // 7 days
    enableRealTime: true,
    widgets: [
      'performance-overview',
      'error-tracking',
      'user-analytics',
      'api-monitoring',
      'system-health',
      'custom-metrics',
    ],
  },

  // Report generation
  reporting: {
    enabled: true,
    schedule: {
      daily: {
        time: '09:00',
        recipients: ['<EMAIL>'],
        metrics: ['performance', 'errors', 'users'],
      },
      weekly: {
        day: 'monday',
        time: '09:00',
        recipients: ['<EMAIL>', '<EMAIL>'],
        metrics: ['performance', 'errors', 'users', 'business'],
      },
      monthly: {
        day: 1,
        time: '09:00',
        recipients: ['<EMAIL>', '<EMAIL>'],
        metrics: ['all'],
      },
    },
  },
};