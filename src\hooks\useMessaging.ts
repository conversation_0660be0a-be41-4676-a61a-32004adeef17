import { useState, useCallback } from 'react';
import {
  createOrGetChat,
  sendMessage,
  sendImageMessage,
  getMessages,
  getChats,
  markChatAsRead,
  subscribeToMessages as subscribeToMessagesFirebase,
  subscribeToChats as subscribeToChatsFirebase
} from '../firebase/messages';
import { Chat, Message } from '../firebase/types';
import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '../firebase/config';

// Firebase function response interface
interface ChatResponse {
  success: boolean;
  data: {
    chatId: string;
    chat: Chat;
  };
}

export const useMessaging = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [users, setUsers] = useState<Record<string, { name: string; avatar: string }>>({});

  // Start or get a chat with another user
  const startChat = useCallback(async (
    otherUserId: string,
    listingContext?: {
      listingId: string;
      listingTitle: string;
      listingImageURL?: string;
    }
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await createOrGetChat(otherUserId, listingContext) as ChatResponse;

      if (result && result.success) {
        setCurrentChat(result.data.chat);
      }

      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to start chat');
      throw err;
    }
  }, []);

  // Send a message in the current chat
  const sendChatMessage = useCallback(async (
    chatId: string,
    text: string,
    receiverId: string,
    listingContext?: {
      listingId: string;
      title: string;
      imageURL?: string;
    }
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await sendMessage(chatId, text, receiverId, listingContext);
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to send message');
      throw err;
    }
  }, []);

  // Send an image message in the current chat
  const sendChatImageMessage = useCallback(async (
    chatId: string,
    imageFile: File,
    receiverId: string,
    listingContext?: {
      listingId: string;
      title: string;
      imageURL?: string;
    }
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await sendImageMessage(chatId, imageFile, receiverId, listingContext);
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to send image');
      throw err;
    }
  }, []);

  // Load messages for a chat
  const loadMessages = useCallback(async (
    chatId: string,
    limitCount: number = 50
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getMessages(chatId, limitCount);

      if (result && result.success) {
        setMessages(result.data.messages);
      }

      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to load messages');
      throw err;
    }
  }, []);

  // Load all chats for the current user
  const loadChats = useCallback(async (userId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getChats(userId);

      if (result && result.success) {
        setChats(result.data.chats);
      }

      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Failed to load chats');
      throw err;
    }
  }, []);

  // Mark all messages in a chat as read
  const markAsRead = useCallback(async (chatId: string) => {
    try {
      const result = await markChatAsRead(chatId);
      return result;
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to mark chat as read');
      throw err;
    }
  }, []);

  // Set up real-time listener for messages in the current chat
  const subscribeToMessages = useCallback((chatId: string) => {
    return subscribeToMessagesFirebase(
      chatId,
      (messages) => {
        setMessages(messages);
      },
      (error) => {
        setError(`Failed to subscribe to messages: ${error.message}`);
      }
    );
  }, []);

  // Set up real-time listener for all chats
  const subscribeToChats = useCallback((userId: string) => {
    return subscribeToChatsFirebase(
      userId,
      async (chats) => {
        setChats(chats);

        // Fetch user data for any new participants
        const userIds = new Set<string>();
        chats.forEach(chat => {
          chat.participants.forEach(uid => {
            if (uid !== userId) {
              userIds.add(uid);
            }
          });
        });

        const newUsers = { ...users };
        let needsUpdate = false;

        for (const uid of userIds) {
          if (!newUsers[uid]) {
            try {
              const userDocRef = doc(firestore, `users/${uid}`);
              const userDoc = await getDoc(userDocRef);

              if (userDoc.exists()) {
                const userData = userDoc.data();
                newUsers[uid] = {
                  name: userData.name || 'Unknown User',
                  avatar: userData.profilePictureURL || '/placeholder-avatar.jpg'
                };
                needsUpdate = true;
              }
            } catch (err) {
              console.error(`Failed to fetch user ${uid}:`, err);
            }
          }
        }

        if (needsUpdate) {
          setUsers(newUsers);
        }
      },
      (error) => {
        setError(`Failed to subscribe to chats: ${error.message}`);
      }
    );
  }, [users]);

  return {
    isLoading,
    error,
    chats,
    currentChat,
    messages,
    users,
    startChat,
    sendChatMessage,
    sendChatImageMessage,
    loadMessages,
    loadChats,
    markAsRead,
    subscribeToMessages,
    subscribeToChats,
    setCurrentChat
  };
};