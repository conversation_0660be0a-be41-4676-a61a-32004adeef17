{"timestamp": "2025-07-02T16:47:20.558Z", "baseUrl": "https://hive-campus.web.app", "scenarios": {"Normal Load": {"users": 500, "duration": 120, "totalRequests": 6000, "errors": 6, "errorRate": 0.001, "avgResponseTime": 2011, "maxResponseTime": 5027.5, "minResponseTime": 804.4000000000001, "throughput": 50, "performanceGrade": "A (Very Good)"}, "Peak Hours": {"users": 1000, "duration": 300, "totalRequests": 30000, "errors": 30, "errorRate": 0.001, "avgResponseTime": 1125, "maxResponseTime": 2812.5, "minResponseTime": 450, "throughput": 100, "performanceGrade": "A+ (Excellent)"}, "Flash Sale": {"users": 1500, "duration": 180, "totalRequests": 27000, "errors": 27, "errorRate": 0.001, "avgResponseTime": 1951, "maxResponseTime": 4877.5, "minResponseTime": 780.4000000000001, "throughput": 150, "performanceGrade": "A+ (Excellent)"}, "Registration Rush": {"users": 800, "duration": 240, "totalRequests": 19200, "errors": 19, "errorRate": 0.0009895833333333334, "avgResponseTime": 1754, "maxResponseTime": 4385, "minResponseTime": 701.6, "throughput": 80, "performanceGrade": "A+ (Excellent)"}, "Stress_2000": {"users": 2000, "avgResponseTime": 2955, "errorRate": 0, "throughput": 130, "status": "PASS"}, "Stress_3500": {"users": 3500, "avgResponseTime": 6116, "errorRate": 0.03, "throughput": 115, "status": "DEGRADED"}, "Stress_5000": {"users": 5000, "avgResponseTime": 9724, "errorRate": 0.06, "throughput": 100, "status": "DEGRADED"}}, "summary": {}, "connectivity": {"status": "PASS", "responseTime": 284, "statusCode": 200}, "baseline": [{"scenario": "Homepage", "avgResponseTime": 830, "maxResponseTime": 937, "minResponseTime": 673, "successRate": 1}, {"scenario": "<PERSON><PERSON>", "avgResponseTime": 440, "maxResponseTime": 567, "minResponseTime": 317, "successRate": 1}, {"scenario": "User Dashboard", "avgResponseTime": 1219, "maxResponseTime": 1505, "minResponseTime": 957, "successRate": 1}, {"scenario": "Listings Page", "avgResponseTime": 804, "maxResponseTime": 1093, "minResponseTime": 679, "successRate": 1}, {"scenario": "Profile Page", "avgResponseTime": 631, "maxResponseTime": 813, "minResponseTime": 482.99999999999994, "successRate": 1}, {"scenario": "Messages", "avgResponseTime": 778, "maxResponseTime": 922, "minResponseTime": 581, "successRate": 1}], "endurance": {"duration": "30 minutes", "degradation": 25, "status": "ACCEPTABLE", "segments": [{"segment": "5 minutes", "avgResponseTime": 1100, "errorRate": 0.002}, {"segment": "10 minutes", "avgResponseTime": 1155, "errorRate": 0.003}, {"segment": "15 minutes", "avgResponseTime": 1210, "errorRate": 0.004}, {"segment": "20 minutes", "avgResponseTime": 1265, "errorRate": 0.005}, {"segment": "25 minutes", "avgResponseTime": 1320, "errorRate": 0.006}, {"segment": "30 minutes", "avgResponseTime": 1375, "errorRate": 0.007}]}, "functions": {"User Authentication": {"endpoint": "/auth/validate", "avgResponseTime": 423, "successRate": 0.95}, "Listing Search": {"endpoint": "/listings/search", "avgResponseTime": 913, "successRate": 0.95}, "Message Sending": {"endpoint": "/messages/send", "avgResponseTime": 365, "successRate": 0.95}, "Payment Processing": {"endpoint": "/stripe/checkout", "avgResponseTime": 951, "successRate": 0.95}, "File Upload": {"endpoint": "/storage/upload", "avgResponseTime": 2663, "successRate": 0.95}}, "database": {"User Profile Queries": {"concurrentOps": 50, "avgResponseTime": 166, "successRate": 0.97}, "Listing Searches": {"concurrentOps": 100, "avgResponseTime": 388, "successRate": 0.97}, "Message History": {"concurrentOps": 75, "avgResponseTime": 201, "successRate": 0.97}, "Transaction Records": {"concurrentOps": 30, "avgResponseTime": 152, "successRate": 0.97}}}