# ReeFlex Stage 2 Implementation Guide

This document outlines the Stage 2 implementation of ReeFlex, which adds AI-powered summaries, real-time alerts, and an admin dashboard.

## Overview

ReeFlex Stage 2 builds on the foundation of Stage 1.5 by adding:

1. **OpenAI Integration**: Intelligent analysis of app activity and feedback
2. **Real-time Alerts**: Slack and email notifications for critical issues
3. **Admin Dashboard**: Visual interface for monitoring app health

## Components

### 1. OpenAI Integration

The OpenAI integration enhances the daily reports with AI-powered insights:

- **Implementation**: `functions/src/utils/openai.ts`
- **Integration Point**: `functions/src/reeflex/aiSummary.ts`
- **Features**:
  - Uses GPT-4 (or GPT-3.5 as fallback) to analyze app data
  - Generates both human-readable summaries and structured JSON insights
  - Provides confidence scores based on data volume
  - Falls back to template-based summaries if OpenAI is unavailable

### 2. Real-time Alerts

The real-time alerts system monitors for critical issues and sends notifications:

- **Implementation**: 
  - `functions/src/reeflex/alerts.ts`
  - `functions/src/utils/slack.ts`
  - `functions/src/utils/email.ts`
- **Alert Types**:
  - Critical errors (5+ of the same type)
  - Performance issues (3+ slow page loads)
  - Payment failures (3+ failures)
  - Feedback patterns (3+ negative feedback on the same route/category)
- **Delivery Methods**:
  - Slack webhooks
  - Email notifications
- **Features**:
  - Alert cooldown to prevent notification spam
  - Detailed context in each alert
  - Links to Firebase console for further investigation

### 3. Admin Dashboard

The admin dashboard provides a visual interface for monitoring app health:

- **Implementation**: `src/pages/admin/ReeFlexDashboard.tsx`
- **Route**: `/admin/reeflex`
- **Features**:
  - Latest AI summary
  - Activity log timeline
  - Filtering by event type and route
  - User feedback analysis
  - Top confusion points
  - Performance metrics

## Configuration

### Environment Variables

Add the following environment variables to your Firebase functions:

```
# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Slack
SLACK_WEBHOOK_URL=your_slack_webhook_url

# Email
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=your_email_user
EMAIL_PASS=your_email_password
EMAIL_FROM=<EMAIL>
VITE_APP_OWNER_EMAIL=<EMAIL>
```

### Firebase Functions

Deploy the updated functions:

```bash
firebase deploy --only functions
```

## Security

All Firebase functions verify the user's admin role before allowing access to sensitive data:

```typescript
// Example from functions
if (!context.auth || context.auth.token.role !== 'admin') {
  throw new functions.https.HttpsError(
    'permission-denied',
    'Only admins can access this function'
  );
}
```

The Firestore security rules protect the ReeFlex collections:

```
// ReeFlex collections
match /reeflex_activity/{eventId} {
  allow create: if true; // Allow even unauthenticated users to log events
  allow read: if isAdmin();
  allow update, delete: if isAdmin();
}

match /reeflex_feedback/{feedbackId} {
  allow create: if true; // Allow even unauthenticated users to submit feedback
  allow read: if isAdmin() || (isAuthenticated() && resource.data.userId == request.auth.uid);
  allow update, delete: if isAdmin();
}

match /reeflex_reports/{reportId} {
  allow read, write: if isAdmin();
}
```

## Usage

### Viewing the Dashboard

1. Log in as an admin user
2. Navigate to `/admin/reeflex`
3. View the latest report, activity, and feedback

### Customizing Alerts

Adjust the alert thresholds in `functions/src/reeflex/alerts.ts`:

```typescript
// Alert thresholds
const ERROR_THRESHOLD = 5; // 5+ errors of the same type on the same route
const PERFORMANCE_THRESHOLD = 3; // 3+ slow page loads on the same route
const PAYMENT_FAILURE_THRESHOLD = 3; // 3+ payment failures
const FEEDBACK_THRESHOLD = 3; // 3+ negative feedback on the same route/category
```

### Customizing OpenAI Prompts

Modify the prompt template in `functions/src/utils/openai.ts` to adjust the AI analysis:

```typescript
function createOpenAIPrompt(reportData: any, includeJson: boolean = false): string {
  // Customize the prompt here
}
```

## Future Enhancements

Planned for Stage 3:

1. **Anomaly Detection**: Automatically identify unusual patterns
2. **Auto-Fix Suggestions**: Generate code fixes for common issues
3. **Predictive Analytics**: Forecast usage patterns and potential issues
4. **User Session Replay**: Reconstruct user sessions for debugging
5. **Integration with CI/CD**: Correlate issues with recent deployments

## Troubleshooting

Common issues and solutions:

- **OpenAI API Errors**: Check your API key and quota
- **Missing Alerts**: Verify webhook URLs and email configuration
- **Dashboard Loading Issues**: Check Firestore permissions
- **High Costs**: Adjust the OpenAI model or implement caching

## Conclusion

ReeFlex Stage 2 provides comprehensive monitoring and alerting for Hive Campus, helping identify and resolve issues before they impact users. The AI-powered insights make it easier to prioritize improvements and understand user behavior.