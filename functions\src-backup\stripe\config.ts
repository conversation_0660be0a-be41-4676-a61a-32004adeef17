import Stripe from 'stripe';
import { getEnvironmentConfigSingleton } from '../config/environment';

// Lazy initialization of Stripe
let _stripe: Stripe | null = null;

export const getStripe = (): Stripe => {
  if (!_stripe) {
    const config = getEnvironmentConfigSingleton();
    _stripe = new Stripe(config.stripe.apiKey, {
      apiVersion: '2025-05-28.basil', // Use the latest API version
    });
  }
  return _stripe;
};

// For backward compatibility
export const stripe = new Proxy({} as Stripe, {
  get(_target, prop) {
    const stripeInstance = getStripe();
    const value = stripeInstance[prop as keyof Stripe];
    return typeof value === 'function' ? value.bind(stripeInstance) : value;
  }
});

// Lazy Stripe Connect configuration
let _stripeConfig: any = null;

export const getStripeConfig = () => {
  if (!_stripeConfig) {
    const config = getEnvironmentConfigSingleton();
    _stripeConfig = {
      // Commission rates (including Stripe fees)
      commissionRates: {
        textbooks: 0.08, // 8% for textbooks/course materials
        other: 0.10,     // 10% for all other categories
      },

      // Cashback percentage for buyers
      cashbackRate: 0.02, // 2% cashback on purchases

      // Minimum Stripe charge amount
      minimumChargeAmount: 0.50, // $0.50 minimum charge

      // Auto-release escrow period in days
      autoReleaseEscrowDays: config.stripe.autoReleaseEscrowDays,

      // Webhook secret for verifying Stripe events
      webhookSecret: config.stripe.webhookSecret,

      // Connect Express configuration
      connect: {
        refreshUrl: `${config.app.url}/settings/payment`,
        returnUrl: `${config.app.url}/settings/payment/success`,
      }
    };
  }
  return _stripeConfig;
};

// For backward compatibility
export const stripeConfig = new Proxy({} as any, {
  get(_target, prop) {
    const config = getStripeConfig();
    return config[prop];
  }
});