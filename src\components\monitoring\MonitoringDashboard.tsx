import React, { useState, useEffect, useMemo } from 'react';
import { monitoring } from '../../utils/monitoring';
import { Activity, AlertTriangle, CheckCircle, XCircle, TrendingUp, TrendingDown, Clock, Users } from 'lucide-react';

interface MonitoringDashboardProps {
  className?: string;
}

interface MetricCard {
  title: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'neutral';
  status?: 'good' | 'warning' | 'error';
  icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({ className = '' }) => {
  const [healthStatus, setHealthStatus] = useState<Record<string, unknown> | null>(null);
  const [metrics, setMetrics] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const updateData = () => {
      const health = monitoring.getHealthStatus();
      const allMetrics = monitoring.getAllMetrics();
      
      setHealthStatus(health);
      setMetrics(allMetrics);
      setIsLoading(false);
    };

    updateData();
    const interval = setInterval(updateData, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, []);

  const metricCards: MetricCard[] = useMemo(() => {
    if (!metrics) return [];

    return [
      {
        title: 'Performance Score',
        value: metrics.performance || 0,
        unit: '%',
        trend: (metrics.performance || 0) > 90 ? 'up' : (metrics.performance || 0) < 70 ? 'down' : 'neutral',
        status: (metrics.performance || 0) > 90 ? 'good' : (metrics.performance || 0) < 70 ? 'error' : 'warning',
        icon: TrendingUp,
      },
      {
        title: 'Error Rate',
        value: metrics.error_rate || 0,
        unit: '%',
        trend: (metrics.error_rate || 0) < 1 ? 'up' : (metrics.error_rate || 0) > 5 ? 'down' : 'neutral',
        status: (metrics.error_rate || 0) < 1 ? 'good' : (metrics.error_rate || 0) > 5 ? 'error' : 'warning',
        icon: AlertTriangle,
      },
      {
        title: 'Response Time',
        value: metrics.response_time || 0,
        unit: 'ms',
        trend: (metrics.response_time || 0) < 500 ? 'up' : (metrics.response_time || 0) > 2000 ? 'down' : 'neutral',
        status: (metrics.response_time || 0) < 500 ? 'good' : (metrics.response_time || 0) > 2000 ? 'error' : 'warning',
        icon: Clock,
      },
      {
        title: 'Active Users',
        value: metrics.active_users || 0,
        trend: 'neutral',
        status: 'good',
        icon: Users,
      },
      {
        title: 'LCP Score',
        value: metrics.LCP ? (metrics.LCP / 1000).toFixed(2) : 0,
        unit: 's',
        trend: (metrics.LCP || 0) < 2500 ? 'up' : (metrics.LCP || 0) > 4000 ? 'down' : 'neutral',
        status: (metrics.LCP || 0) < 2500 ? 'good' : (metrics.LCP || 0) > 4000 ? 'error' : 'warning',
        icon: Activity,
      },
      {
        title: 'CLS Score',
        value: metrics.CLS ? metrics.CLS.toFixed(3) : 0,
        trend: (metrics.CLS || 0) < 0.1 ? 'up' : (metrics.CLS || 0) > 0.25 ? 'down' : 'neutral',
        status: (metrics.CLS || 0) < 0.1 ? 'good' : (metrics.CLS || 0) > 0.25 ? 'error' : 'warning',
        icon: Activity,
      },
    ];
  }, [metrics]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getOverallStatus = () => {
    const errorCount = metricCards.filter(card => card.status === 'error').length;
    const warningCount = metricCards.filter(card => card.status === 'warning').length;

    if (errorCount > 0) return 'error';
    if (warningCount > 0) return 'warning';
    return 'good';
  };

  const overallStatus = getOverallStatus();

  if (isLoading) {
    return (
      <div className={`monitoring-dashboard ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`monitoring-dashboard ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            System Monitoring
          </h2>
          <div className={`flex items-center px-3 py-2 rounded-lg border ${getStatusColor(overallStatus)}`}>
            {overallStatus === 'good' ? (
              <CheckCircle className="w-5 h-5 mr-2" />
            ) : overallStatus === 'warning' ? (
              <AlertTriangle className="w-5 h-5 mr-2" />
            ) : (
              <XCircle className="w-5 h-5 mr-2" />
            )}
            <span className="font-medium">
              {overallStatus === 'good' ? 'All Systems Operational' : 
               overallStatus === 'warning' ? 'Some Issues Detected' : 
               'Critical Issues Detected'}
            </span>
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Real-time system health and performance metrics
        </p>
      </div>

      {/* Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {metricCards.map((card, index) => {
          const Icon = card.icon || Activity;
          return (
            <div
              key={index}
              className={`p-6 rounded-xl border transition-all duration-200 hover:shadow-lg ${getStatusColor(card.status || 'good')}`}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Icon className="w-6 h-6 mr-3" />
                  <h3 className="font-semibold">{card.title}</h3>
                </div>
                {card.trend && getTrendIcon(card.trend)}
              </div>
              
              <div className="flex items-baseline">
                <div className="text-2xl font-bold">
                  {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                </div>
                {card.unit && (
                  <div className="ml-2 text-sm text-gray-500">{card.unit}</div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* System Status */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold mb-4">System Status</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
              <span>Application Server</span>
            </div>
            <span className="text-green-600 font-medium">Operational</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
              <span>Firebase Services</span>
            </div>
            <span className="text-green-600 font-medium">Operational</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${
                (metrics.error_rate || 0) < 1 ? 'bg-green-500' : 
                (metrics.error_rate || 0) < 5 ? 'bg-yellow-500' : 'bg-red-500'
              }`}></div>
              <span>Error Tracking</span>
            </div>
            <span className={`font-medium ${
              (metrics.error_rate || 0) < 1 ? 'text-green-600' : 
              (metrics.error_rate || 0) < 5 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {(metrics.error_rate || 0) < 1 ? 'Operational' : 
               (metrics.error_rate || 0) < 5 ? 'Degraded' : 'Issues Detected'}
            </span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${
                (metrics.response_time || 0) < 1000 ? 'bg-green-500' : 
                (metrics.response_time || 0) < 2000 ? 'bg-yellow-500' : 'bg-red-500'
              }`}></div>
              <span>Performance</span>
            </div>
            <span className={`font-medium ${
              (metrics.response_time || 0) < 1000 ? 'text-green-600' : 
              (metrics.response_time || 0) < 2000 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {(metrics.response_time || 0) < 1000 ? 'Optimal' : 
               (metrics.response_time || 0) < 2000 ? 'Degraded' : 'Poor'}
            </span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center text-gray-600 dark:text-gray-400">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            <span>System monitoring initialized</span>
            <span className="ml-auto">
              {healthStatus?.timestamp ? new Date(healthStatus.timestamp).toLocaleTimeString() : 'Now'}
            </span>
          </div>
          <div className="flex items-center text-gray-600 dark:text-gray-400">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            <span>Performance metrics collected</span>
            <span className="ml-auto">
              {new Date(Date.now() - 30000).toLocaleTimeString()}
            </span>
          </div>
          <div className="flex items-center text-gray-600 dark:text-gray-400">
            <div className="w-2 h-2 bg-gray-500 rounded-full mr-3"></div>
            <span>Error tracking active</span>
            <span className="ml-auto">
              {new Date(Date.now() - 60000).toLocaleTimeString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MonitoringDashboard;