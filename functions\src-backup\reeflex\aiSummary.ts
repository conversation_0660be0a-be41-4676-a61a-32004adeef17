/**
 * AI Summary Generator for ReeFlex Reports
 * 
 * This module generates an intelligent summary of the ReeFlex report data
 * using OpenAI's API (or a placeholder for now).
 */

import * as admin from 'firebase-admin';

// Report data interface
interface ReportData {
  timeRange: {
    start: string;
    end: string;
  };
  summary: {
    totalEvents: number;
    totalErrors: number;
    totalFeedback: number;
    totalPageViews: number;
    totalInteractions: number;
  };
  errors: {
    count: number;
    byType: Record<string, number>;
    details: Array<{
      type: string;
      message: string;
      route: string;
      userId?: string;
      timestamp: string;
      sentryEventId?: string;
    }>;
  };
  performance: {
    issues: number;
    slowestPages: Array<{
      route: string;
      duration: number;
      timestamp: string;
    }>;
    slowestResources: Array<{
      url: string;
      type: string;
      duration: number;
    }>;
  };
  userBehavior: {
    topPages: Array<{
      route: string;
      count: number;
    }>;
    checkoutFunnel: {
      started: number;
      completed: number;
      failed: number;
      dropOffRate: string;
    };
  };
  feedback: {
    positive: number;
    negative: number;
    neutral: number;
    byCategory: Record<string, unknown[]>;
    userQuotes: Array<{
      message: string;
      type: string;
      category: string;
      route: string;
    }>;
  };
}

import { generateOpenAISummary } from '../utils/openai';
import * as functions from 'firebase-functions';

/**
 * Generate an AI summary of the ReeFlex report
 * 
 * This function uses OpenAI's API to generate an intelligent summary of the report data.
 * If OpenAI is not configured or fails, it falls back to a template-based approach.
 */
export async function generateAISummary(reportData: ReportData): Promise<string> {
  try {
    // Try to use OpenAI for the summary
    try {
      const { summary, insights } = await generateOpenAISummary(reportData, {
        includeJson: true,
        temperature: 0.3
      });
      
      // Store the structured insights for future use
      if (insights) {
        await storeAIInsights(reportData.timeRange, insights);
      }
      
      return summary;
    } catch (openAiError) {
      // Log the error but continue with the fallback
      functions.logger.warn('OpenAI summary generation failed, using fallback template:', openAiError);
      
      // Fallback to template-based summary
      // Determine confidence level based on data volume
      const confidenceLevel = determineConfidenceLevel(reportData);
      
      // Generate the summary sections
      const issuesSummary = generateIssuesSummary(reportData);
      const confusionSummary = generateConfusionSummary(reportData);
      const performanceSummary = generatePerformanceSummary(reportData);
      const usageSummary = generateUsageSummary(reportData);
      const recommendationsSummary = generateRecommendationsSummary(reportData);
      const criticalIssuesSummary = generateCriticalIssuesSummary(reportData);
      
      // Combine all sections into a complete summary
      const summary = `
# ReeFlex Daily Intelligence Report

## 📉 Issues & Breakages
${issuesSummary}

## 🧩 User Confusion Points
${confusionSummary}

## ⏱ Performance Issues
${performanceSummary}

## 📊 Usage Patterns
${usageSummary}

## 🧠 Recommended Actions
${recommendationsSummary}

## 🔥 Critical Issues
${criticalIssuesSummary}

---
Summary confidence: ${confidenceLevel}
(Generated using template - OpenAI integration failed)
`;
      
      return summary;
    }
  } catch (error) {
    console.error('Error generating AI summary:', error);
    return `Failed to generate AI summary: ${error instanceof Error ? error.message : String(error)}`;
  }
}

/**
 * Store the AI-generated insights in Firestore for future reference
 */
async function storeAIInsights(timeRange: { start: string; end: string }, insights: Record<string, unknown>): Promise<void> {
  try {
    await admin.firestore().collection('reeflex_insights').add({
      timeRange,
      insights,
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    });
  } catch (error) {
    functions.logger.error('Failed to store AI insights:', error);
    // Don't throw - this is a non-critical operation
  }
}

/**
 * Determine the confidence level of the summary based on data volume
 */
function determineConfidenceLevel(reportData: ReportData): string {
  const { totalEvents, totalErrors, totalFeedback } = reportData.summary;
  
  if (totalEvents > 1000 && totalFeedback > 10 && totalErrors < totalEvents * 0.1) {
    return 'High';
  } else if (totalEvents > 100 && totalFeedback > 3 && totalErrors < totalEvents * 0.2) {
    return 'Medium';
  } else {
    return 'Low';
  }
}

/**
 * Generate a summary of issues and breakages
 */
function generateIssuesSummary(reportData: ReportData): string {
  const { errors } = reportData;
  
  if (errors.count === 0) {
    return 'No errors or breakages detected in the last 24 hours. The app appears to be functioning normally.';
  }
  
  // Get the most common error types
  const errorTypes = Object.entries(errors.byType)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3);
  
  // Get the most common error routes
  const routeCounts: Record<string, number> = {};
  errors.details.forEach(error => {
    routeCounts[error.route] = (routeCounts[error.route] || 0) + 1;
  });
  
  const topErrorRoutes = Object.entries(routeCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3);
  
  let summary = `Detected ${errors.count} errors in the last 24 hours.\n\n`;
  
  if (errorTypes.length > 0) {
    summary += 'Most common error types:\n';
    errorTypes.forEach(([type, count]) => {
      summary += `- ${type}: ${count} occurrences\n`;
    });
    summary += '\n';
  }
  
  if (topErrorRoutes.length > 0) {
    summary += 'Pages with most errors:\n';
    topErrorRoutes.forEach(([route, count]) => {
      summary += `- ${route}: ${count} errors\n`;
    });
    summary += '\n';
  }
  
  if (errors.details.length > 0) {
    summary += 'Sample error messages:\n';
    errors.details.slice(0, 3).forEach(error => {
      summary += `- "${error.message.substring(0, 100)}${error.message.length > 100 ? '...' : ''}"\n`;
    });
  }
  
  return summary;
}

/**
 * Generate a summary of user confusion points
 */
function generateConfusionSummary(reportData: ReportData): string {
  const { feedback } = reportData;
  const negativeFeedback = feedback.negative;
  
  if (negativeFeedback === 0) {
    return 'No negative feedback or confusion points detected in the last 24 hours.';
  }
  
  // Get confusion categories from negative feedback
  const confusionCategories: Record<string, number> = {};
  Object.entries(feedback.byCategory).forEach(([category, items]) => {
    const negativeItems = items.filter((item: unknown) => 
      typeof item === 'object' && 
      item !== null && 
      'feedbackType' in item && 
      (item as { feedbackType: string }).feedbackType === 'negative'
    );
    if (negativeItems.length > 0) {
      confusionCategories[category] = negativeItems.length;
    }
  });
  
  const topConfusionCategories = Object.entries(confusionCategories)
    .sort((a, b) => b[1] - a[1]);
  
  // Get routes with negative feedback
  const routesWithNegativeFeedback: Record<string, number> = {};
  feedback.userQuotes
    .filter(quote => quote.type === 'negative')
    .forEach(quote => {
      routesWithNegativeFeedback[quote.route] = (routesWithNegativeFeedback[quote.route] || 0) + 1;
    });
  
  const topConfusionRoutes = Object.entries(routesWithNegativeFeedback)
    .sort((a, b) => b[1] - a[1]);
  
  let summary = `Received ${negativeFeedback} pieces of negative feedback indicating user confusion.\n\n`;
  
  if (topConfusionCategories.length > 0) {
    summary += 'Top confusion categories:\n';
    topConfusionCategories.forEach(([category, count]) => {
      summary += `- ${category}: ${count} reports\n`;
    });
    summary += '\n';
  }
  
  if (topConfusionRoutes.length > 0) {
    summary += 'Pages causing most confusion:\n';
    topConfusionRoutes.forEach(([route, count]) => {
      summary += `- ${route}: ${count} negative feedback\n`;
    });
    summary += '\n';
  }
  
  // Include some user quotes
  const negativeQuotes = feedback.userQuotes.filter(quote => quote.type === 'negative');
  if (negativeQuotes.length > 0) {
    summary += 'Sample user feedback:\n';
    negativeQuotes.slice(0, 3).forEach(quote => {
      summary += `- "${quote.message.substring(0, 100)}${quote.message.length > 100 ? '...' : ''}" (${quote.category})\n`;
    });
  }
  
  // Check for checkout funnel issues
  const { checkoutFunnel } = reportData.userBehavior;
  if (checkoutFunnel.started > 0 && parseFloat(checkoutFunnel.dropOffRate) > 30) {
    summary += `\nCheckout flow has a high drop-off rate of ${checkoutFunnel.dropOffRate} (${checkoutFunnel.completed} completed out of ${checkoutFunnel.started} started).`;
  }
  
  return summary;
}

/**
 * Generate a summary of performance issues
 */
function generatePerformanceSummary(reportData: ReportData): string {
  const { performance } = reportData;
  
  if (performance.issues === 0) {
    return 'No significant performance issues detected in the last 24 hours.';
  }
  
  let summary = `Detected ${performance.issues} performance issues in the last 24 hours.\n\n`;
  
  if (performance.slowestPages.length > 0) {
    summary += 'Slowest pages:\n';
    performance.slowestPages.forEach(page => {
      summary += `- ${page.route}: ${page.duration}ms\n`;
    });
    summary += '\n';
  }
  
  if (performance.slowestResources.length > 0) {
    summary += 'Slowest resources:\n';
    performance.slowestResources.forEach(resource => {
      summary += `- ${resource.type}: ${resource.url.substring(0, 50)}${resource.url.length > 50 ? '...' : ''} (${resource.duration}ms)\n`;
    });
  }
  
  return summary;
}

/**
 * Generate a summary of usage patterns
 */
function generateUsageSummary(reportData: ReportData): string {
  const { userBehavior, summary } = reportData;
  
  if (summary.totalPageViews === 0) {
    return 'No usage data available for the last 24 hours.';
  }
  
  let usageSummary = `Recorded ${summary.totalPageViews} page views and ${summary.totalInteractions} user interactions.\n\n`;
  
  if (userBehavior.topPages.length > 0) {
    usageSummary += 'Most visited pages:\n';
    userBehavior.topPages.slice(0, 5).forEach(page => {
      usageSummary += `- ${page.route}: ${page.count} views\n`;
    });
    usageSummary += '\n';
  }
  
  // Checkout funnel stats
  const { checkoutFunnel } = userBehavior;
  if (checkoutFunnel.started > 0) {
    usageSummary += 'Checkout funnel:\n';
    usageSummary += `- Started: ${checkoutFunnel.started}\n`;
    usageSummary += `- Completed: ${checkoutFunnel.completed}\n`;
    usageSummary += `- Failed: ${checkoutFunnel.failed}\n`;
    usageSummary += `- Conversion rate: ${(100 - parseFloat(checkoutFunnel.dropOffRate)).toFixed(2)}%\n`;
  }
  
  return usageSummary;
}

/**
 * Generate recommendations based on the report data
 */
function generateRecommendationsSummary(reportData: ReportData): string {
  const recommendations: string[] = [];
  
  // Error-based recommendations
  if (reportData.errors.count > 10) {
    recommendations.push('Investigate and fix the most common error types, especially on high-traffic pages.');
  }
  
  // Performance recommendations
  if (reportData.performance.issues > 0) {
    if (reportData.performance.slowestPages.length > 0) {
      recommendations.push(`Optimize the performance of slow pages, particularly ${reportData.performance.slowestPages[0].route}.`);
    }
    
    if (reportData.performance.slowestResources.length > 0) {
      recommendations.push('Optimize or lazy-load slow resources to improve overall page load times.');
    }
  }
  
  // Checkout funnel recommendations
  const { checkoutFunnel } = reportData.userBehavior;
  if (checkoutFunnel.started > 0 && parseFloat(checkoutFunnel.dropOffRate) > 30) {
    recommendations.push('Improve the checkout flow to reduce the high drop-off rate.');
  }
  
  // Feedback-based recommendations
  if (reportData.feedback.negative > reportData.feedback.positive) {
    recommendations.push('Address negative feedback, focusing on the most reported categories.');
  }
  
  // Add generic recommendations if we don't have enough specific ones
  if (recommendations.length < 3) {
    if (reportData.userBehavior.topPages.length > 0) {
      recommendations.push(`Focus on enhancing the user experience on your most visited page: ${reportData.userBehavior.topPages[0].route}.`);
    }
    
    if (reportData.summary.totalEvents > 0) {
      recommendations.push('Continue monitoring user behavior to identify patterns and improvement opportunities.');
    }
  }
  
  if (recommendations.length === 0) {
    return 'No specific recommendations at this time. Continue monitoring app performance and user feedback.';
  }
  
  return recommendations.map(rec => `- ${rec}`).join('\n');
}

/**
 * Generate a summary of critical issues that need immediate attention
 */
function generateCriticalIssuesSummary(reportData: ReportData): string {
  const criticalIssues: string[] = [];
  
  // High error count is critical
  if (reportData.errors.count > 50) {
    criticalIssues.push(`High error volume: ${reportData.errors.count} errors in 24 hours.`);
  }
  
  // API failures are critical
  const apiFailed = reportData.errors.details.filter(e => e.type === 'api_failed' || e.type === 'api_error').length;
  if (apiFailed > 10) {
    criticalIssues.push(`${apiFailed} API failures detected.`);
  }
  
  // Very slow pages are critical
  const verySlow = reportData.performance.slowestPages.filter(p => p.duration > 5000).length;
  if (verySlow > 0) {
    criticalIssues.push(`${verySlow} pages with load times exceeding 5 seconds.`);
  }
  
  // High checkout failure is critical
  if (reportData.userBehavior.checkoutFunnel.failed > 5) {
    criticalIssues.push(`${reportData.userBehavior.checkoutFunnel.failed} checkout failures.`);
  }
  
  // Payment timeouts are critical
  const paymentTimeouts = reportData.errors.details.filter(e => e.type === 'payment_timeout').length;
  if (paymentTimeouts > 0) {
    criticalIssues.push(`${paymentTimeouts} payment timeout errors.`);
  }
  
  if (criticalIssues.length === 0) {
    return 'No critical issues requiring immediate attention.';
  }
  
  return criticalIssues.map(issue => `- ${issue}`).join('\n');
}
