# ReeFlex: Intelligent Observability for Hive Campus

ReeFlex is an advanced observability system built for the Hive Campus marketplace app. It provides real-time monitoring, AI-powered insights, and actionable alerts to help maintain app quality and improve user experience.

## System Components

### 1. Activity Tracking

ReeFlex automatically captures:
- Page views and navigation
- User interactions
- JavaScript errors and promise rejections
- API failures and slow responses
- Performance metrics (slow pages, long tasks)
- Checkout funnel events

Data is stored in the `reeflex_activity` Firestore collection.

### 2. Feedback Collection

The system includes a floating feedback component that allows users to:
- Rate their experience (positive, negative, neutral)
- Categorize feedback (UI, functionality, performance, etc.)
- Provide detailed comments
- Attach screenshots (optional)

Feedback is stored in the `reeflex_feedback` Firestore collection.

### 3. AI-Powered Reports

ReeFlex generates daily intelligence reports using:
- OpenAI integration for natural language insights
- Trend analysis across multiple metrics
- Identification of critical issues
- Actionable recommendations

Reports are stored in the `reeflex_reports` Firestore collection.

### 4. Real-time Alerts

The system monitors for critical issues and sends alerts via:
- Slack notifications
- Email alerts
- Admin dashboard notifications

Alert types include:
- Critical error clusters
- Performance degradations
- Payment processing failures
- Negative feedback patterns

## Admin Dashboard

The ReeFlex dashboard (`/admin/reeflex`) provides:
- Latest intelligence report with AI summary
- Activity monitoring with filtering
- Feedback analysis and sentiment tracking
- Confusion point identification
- Historical report access
- Trend visualization

## Setup Instructions

### Environment Variables

Add the following to your `.env` file:

```
# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Slack
SLACK_WEBHOOK_URL=your_slack_webhook_url

# Email
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=your_email_user
EMAIL_PASS=your_email_password
EMAIL_FROM=<EMAIL>
VITE_APP_OWNER_EMAIL=<EMAIL>
```

### Firebase Configuration

ReeFlex uses the following Firestore collections:
- `reeflex_activity`: User activity and error events
- `reeflex_feedback`: User feedback submissions
- `reeflex_reports`: Daily AI-generated reports
- `reeflex_alerts`: Alert tracking for cooldown management
- `reeflex_insights`: Structured AI insights for future analysis

### Scheduled Functions

The system includes several Firebase scheduled functions:
- `sendReeFlexReport`: Daily report generation (5:00 AM)
- `checkForCriticalErrors`: Error monitoring (every 15 minutes)
- `checkForPerformanceIssues`: Performance monitoring (every 30 minutes)
- `checkForPaymentFailures`: Payment issue detection (every 15 minutes)
- `checkForFeedbackPatterns`: Feedback pattern analysis (hourly)

## Usage

### Tracking Activity

Activity is automatically tracked through the ReeFlex client SDK:

```typescript
import { trackActivity } from '../utils/reeflex';

// Track a page view
trackActivity('page_viewed', {
  previous_path: '/previous-route'
});

// Track an error
trackActivity('javascript_error', {
  message: error.message,
  stack: error.stack
});
```

### Collecting Feedback

The floating feedback component can be added to any page:

```tsx
import FloatingFeedback from '../components/reeflex/FloatingFeedback';

function ProductPage() {
  return (
    <div>
      {/* Page content */}
      <FloatingFeedback route="/products/123" />
    </div>
  );
}
```

### Viewing Reports

Administrators can access the ReeFlex dashboard at `/admin/reeflex` to view reports, activity, and feedback.

## Extending ReeFlex

### Adding New Event Types

To track new types of events, add them to the activity tracking system:

1. Define the event type and schema
2. Update the tracking function
3. Add visualization in the dashboard

### Customizing Alerts

Alert thresholds and conditions can be modified in `functions/src/reeflex/alerts.ts`.

### Enhancing AI Analysis

The OpenAI prompt and analysis can be customized in `functions/src/utils/openai.ts`.

## Documentation

For more detailed information, see:
- [ReeFlex Dashboard Documentation](./docs/reeflex-dashboard.md)
- [API Reference](./docs/reeflex-api.md)
- [Deployment Guide](./docs/reeflex-deployment.md)