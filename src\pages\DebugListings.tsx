import React, { useState } from 'react';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { useAuth } from '../hooks/useAuth';

const DebugListings: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const { currentUser } = useAuth();

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => prev + '\n' + new Date().toLocaleTimeString() + ': ' + info);
  };

  const testCreateSampleListings = async () => {
    setIsLoading(true);
    addDebugInfo('Testing createSampleListings function...');

    try {
      const createSampleListingsFn = httpsCallable(functions, 'createSampleListings');
      addDebugInfo('Calling createSampleListings...');
      const result = await createSampleListingsFn();
      addDebugInfo('createSampleListings result: ' + JSON.stringify(result.data, null, 2));
    } catch (error: any) {
      addDebugInfo('Error creating sample listings: ' + error.message);
      addDebugInfo('Full error: ' + JSON.stringify(error, null, 2));
    }

    setIsLoading(false);
  };

  const testGetListings = async () => {
    setIsLoading(true);
    addDebugInfo('Testing getListings function...');
    
    try {
      const getListingsFn = httpsCallable(functions, 'getListings');
      addDebugInfo('Calling getListings...');
      const result = await getListingsFn({});
      addDebugInfo('getListings result: ' + JSON.stringify(result.data, null, 2));
    } catch (error) {
      addDebugInfo('Error getting listings: ' + JSON.stringify(error, null, 2));
    }
    
    setIsLoading(false);
  };

  const testGetListingById = async () => {
    setIsLoading(true);
    addDebugInfo('Testing getListingById function...');
    
    // First get all listings to find a valid ID
    try {
      const getListingsFn = httpsCallable(functions, 'getListings');
      addDebugInfo('Getting all listings first...');
      const listingsResult = await getListingsFn({});
      
      if (listingsResult.data?.success && listingsResult.data?.data?.listings?.length > 0) {
        const firstListing = listingsResult.data.data.listings[0];
        addDebugInfo('Found listing with ID: ' + firstListing.id);
        
        // Now test getListingById
        const getListingByIdFn = httpsCallable(functions, 'getListingById');
        addDebugInfo('Calling getListingById with ID: ' + firstListing.id);
        const result = await getListingByIdFn({ listingId: firstListing.id });
        addDebugInfo('getListingById result: ' + JSON.stringify(result.data, null, 2));
      } else {
        addDebugInfo('No listings found to test getListingById');
      }
    } catch (error) {
      addDebugInfo('Error testing getListingById: ' + JSON.stringify(error, null, 2));
    }
    
    setIsLoading(false);
  };

  const testCreateSingleListing = async () => {
    setIsLoading(true);
    addDebugInfo('Testing createListing function...');

    try {
      const createListingFn = httpsCallable(functions, 'createListing');
      addDebugInfo('Calling createListing...');

      const testListing = {
        title: "Test Calculus Textbook",
        description: "A test listing to verify the system is working correctly.",
        price: 50.00,
        category: "Textbooks",
        condition: "like_new",
        type: "sell",
        imageURLs: [
          "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400"
        ]
      };

      const result = await createListingFn(testListing);
      addDebugInfo('createListing result: ' + JSON.stringify(result.data, null, 2));
    } catch (error: any) {
      addDebugInfo('Error creating listing: ' + error.message);
      addDebugInfo('Full error: ' + JSON.stringify(error, null, 2));
    }

    setIsLoading(false);
  };

  const clearDebugInfo = () => {
    setDebugInfo('');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
          Debug Listings
        </h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current User Info</h2>
          <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(currentUser, null, 2)}
          </pre>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Functions</h2>
          <div className="space-y-4">
            <button
              onClick={testCreateSampleListings}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              Test Create Sample Listings
            </button>
            
            <button
              onClick={testGetListings}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded disabled:opacity-50 ml-4"
            >
              Test Get Listings
            </button>
            
            <button
              onClick={testGetListingById}
              disabled={isLoading}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded disabled:opacity-50 ml-4"
            >
              Test Get Listing By ID
            </button>

            <button
              onClick={testCreateSingleListing}
              disabled={isLoading}
              className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded disabled:opacity-50 ml-4"
            >
              Create Single Test Listing
            </button>

            <button
              onClick={clearDebugInfo}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded ml-4"
            >
              Clear Debug Info
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm overflow-auto h-96 whitespace-pre-wrap">
            {debugInfo || 'No debug information yet. Click a test button above.'}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default DebugListings;
