import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { isEducationalEmail, handleError, verifyAuth } from "../utils/helpers";
import { User, UserRole } from "../utils/types";

// Create user document in Firestore after signup
export const createUserRecord = functions.auth.user().onCreate(async (user) => {
  try {
    const { uid, email, displayName } = user;
    
    if (!email) {
      throw new Error('User email is required');
    }
    
    // Verify if the email is from an educational institution
    if (!isEducationalEmail(email)) {
      // Delete the user if email is not from an educational institution
      await admin.auth().deleteUser(uid);
      throw new Error('Only .edu email addresses are allowed to register');
    }

    // Default role is student
    const role: UserRole = 'student';

    // Extract university from email domain
    const emailParts = email.split('@');
    const domain = emailParts[1];
    let university = domain.split('.')[0];

    // Capitalize university name
    university = university.charAt(0).toUpperCase() + university.slice(1);
    
    const userData: User = {
      uid,
      name: displayName || email.split('@')[0],
      email,
      role,
      university,
      createdAt: admin.firestore.Timestamp.now()
    };
    
    await admin.firestore().collection('users').doc(uid).set(userData);

    return { success: true };
  } catch (error) {
    console.error('Error creating user record:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, error: errorMessage };
  }
});

// Update user profile
export const updateUserProfile = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);

    const { name, university, profilePictureURL, bio, graduationYear, major } = data;

    if (!name && !university && !profilePictureURL && !bio && !graduationYear && !major) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'No update data provided'
      );
    }

    const updateData: Partial<User> = {
      updatedAt: admin.firestore.Timestamp.now()
    };

    if (name) updateData.name = name;
    if (university) updateData.university = university;
    if (profilePictureURL) updateData.profilePictureURL = profilePictureURL;
    if (bio !== undefined) updateData.bio = bio;
    if (graduationYear) updateData.graduationYear = graduationYear;
    if (major) updateData.major = major;

    await admin.firestore().collection('users').doc(auth.uid).update(updateData);

    // Update display name in Auth if provided
    if (name) {
      await admin.auth().updateUser(auth.uid, {
        displayName: name
      });
    }

    return { success: true };
  } catch (error) {
    return handleError(error);
  }
});

// Get user profile
export const getUserProfile = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    
    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'User profile not found'
      );
    }
    
    return {
      success: true,
      data: userDoc.data()
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get user by ID (for public profiles)
export const getUserById = functions.https.onCall(async (data, context) => {
  try {
    await verifyAuth(context);
    
    const { userId } = data;
    
    if (!userId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'User ID is required'
      );
    }
    
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'User not found'
      );
    }
    
    // Return limited public information
    const userData = userDoc.data() as User;
    
    return {
      success: true,
      data: {
        uid: userData.uid,
        name: userData.name,
        university: userData.university,
        profilePictureURL: userData.profilePictureURL,
        role: userData.role
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Change user role (admin only)
export const changeUserRole = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    // Check if the caller is an admin
    const callerDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    
    if (!callerDoc.exists || callerDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can change user roles'
      );
    }
    
    const { userId, newRole } = data;
    
    if (!userId || !newRole) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'User ID and new role are required'
      );
    }
    
    if (!['student', 'merchant', 'admin'].includes(newRole)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid role. Must be one of: student, merchant, admin'
      );
    }
    
    await admin.firestore().collection('users').doc(userId).update({
      role: newRole,
      updatedAt: admin.firestore.Timestamp.now()
    });
    
    return { success: true };
  } catch (error) {
    return handleError(error);
  }
});