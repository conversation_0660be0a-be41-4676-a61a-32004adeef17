import { isRiskyMessage, getRiskType } from './detectRisk';

// Test cases for risk detection
export function testRiskDetection() {
  console.log('Testing Risk Detection...');
  
  const testCases = [
    // Should NOT be flagged (legitimate shipping terms)
    { text: 'Buyer pays shipping', expected: false, description: 'Buyer pays shipping' },
    { text: 'Seller pays for shipping', expected: false, description: 'Seller pays for shipping' },
    { text: 'Free shipping included', expected: false, description: 'Free shipping' },
    { text: 'In-person delivery available', expected: false, description: 'In-person delivery' },
    { text: 'Mail delivery with tracking', expected: false, description: 'Mail delivery' },
    { text: 'Package size: medium', expected: false, description: 'Package size' },
    { text: 'Hive shipping via Shippo', expected: false, description: 'Hive shipping' },
    
    // Should be flagged (actual payment methods)
    { text: 'Contact me on venmo', expected: true, description: 'Venmo mention' },
    { text: 'PayPal preferred', expected: true, description: 'PayPal mention' },
    { text: 'Send money via cashapp', expected: true, description: 'CashApp mention' },
    { text: '<PERSON>elle me the payment', expected: true, description: '<PERSON>elle mention' },
    
    // Edge cases
    { text: 'I will pay cash on delivery', expected: true, description: 'Cash payment (should be flagged)' },
    { text: 'Great textbook for calculus class', expected: false, description: 'Normal listing description' },
    { text: 'Call me at ************', expected: true, description: 'Phone number' },
    { text: 'Email <NAME_EMAIL>', expected: true, description: 'Email address' }
  ];
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((testCase, index) => {
    const result = isRiskyMessage(testCase.text);
    const riskType = getRiskType(testCase.text);
    
    if (result === testCase.expected) {
      console.log(`✅ Test ${index + 1} PASSED: ${testCase.description}`);
      passed++;
    } else {
      console.log(`❌ Test ${index + 1} FAILED: ${testCase.description}`);
      console.log(`   Text: "${testCase.text}"`);
      console.log(`   Expected: ${testCase.expected}, Got: ${result}`);
      console.log(`   Risk Type: ${riskType}`);
      failed++;
    }
  });
  
  console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
  return { passed, failed };
}

// Run tests if this file is imported
if (typeof window !== 'undefined') {
  // Make it available globally for browser testing
  (window as any).testRiskDetection = testRiskDetection;
}
