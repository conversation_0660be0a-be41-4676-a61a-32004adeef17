/**
 * Error handling middleware for Firebase Functions
 * Provides consistent error handling, logging, and response formatting
 */

import { Request, Response, NextFunction } from 'express';
import * as functions from 'firebase-functions';
import { captureException, setFunctionContext } from '../utils/sentry';

/**
 * Error types for Firebase Functions
 */
export enum FunctionErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}

/**
 * Function error class
 */
export class FunctionError extends Error {
  public readonly type: FunctionErrorType;
  public readonly statusCode: number;
  public readonly code?: string;
  public readonly context?: Record<string, unknown>;
  public readonly isOperational: boolean;
  public readonly timestamp: Date;

  constructor(
    type: FunctionErrorType,
    message: string,
    statusCode: number = 500,
    code?: string,
    context?: Record<string, unknown>,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = 'FunctionError';
    this.type = type;
    this.statusCode = statusCode;
    this.code = code;
    this.context = context;
    this.isOperational = isOperational;
    this.timestamp = new Date();

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, FunctionError);
    }
  }

  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      type: this.type,
      message: this.message,
      statusCode: this.statusCode,
      code: this.code,
      context: this.context,
      isOperational: this.isOperational,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack,
    };
  }
}

/**
 * HTTP Error class for Express middleware
 */
export class HTTPError extends FunctionError {
  constructor(
    statusCode: number,
    message: string,
    type?: FunctionErrorType,
    code?: string,
    context?: Record<string, unknown>
  ) {
    const errorType = type || HTTPError.getErrorTypeFromStatus(statusCode);
    super(errorType, message, statusCode, code, context);
    this.name = 'HTTPError';
  }

  private static getErrorTypeFromStatus(statusCode: number): FunctionErrorType {
    if (statusCode === 400) return FunctionErrorType.VALIDATION_ERROR;
    if (statusCode === 401) return FunctionErrorType.AUTHENTICATION_ERROR;
    if (statusCode === 403) return FunctionErrorType.AUTHORIZATION_ERROR;
    if (statusCode === 404) return FunctionErrorType.NOT_FOUND_ERROR;
    if (statusCode === 429) return FunctionErrorType.RATE_LIMIT_ERROR;
    if (statusCode >= 500) return FunctionErrorType.INTERNAL_ERROR;
    return FunctionErrorType.INTERNAL_ERROR;
  }
}

/**
 * Error handler for Express middleware
 */
export const expressErrorHandler = (
  err: Error | FunctionError | HTTPError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // Set function context for Sentry
  setFunctionContext('express_error_handler', {
    method: req.method,
    url: req.url,
    headers: req.headers,
    body: req.body,
  });

  let error: FunctionError;

  if (err instanceof FunctionError) {
    error = err;
  } else {
    // Convert generic errors to FunctionError
    error = new FunctionError(
      FunctionErrorType.INTERNAL_ERROR,
      err.message || 'Internal server error',
      500,
      undefined,
      { originalError: err.name, stack: err.stack }
    );
  }

  // Log error
  console.error(`[${error.type}] ${error.message}`, {
    statusCode: error.statusCode,
    code: error.code,
    context: error.context,
    stack: error.stack,
  });

  // Report to Sentry for server errors or unexpected errors
  if (error.statusCode >= 500 || !error.isOperational) {
    captureException(error, error.context, 'express_middleware');
  }

  // Send error response
  res.status(error.statusCode).json({
    error: {
      type: error.type,
      message: error.message,
      code: error.code,
      timestamp: error.timestamp.toISOString(),
      ...(process.env.NODE_ENV === 'development' && {
        stack: error.stack,
        context: error.context,
      }),
    },
  });
};

/**
 * Error handler for Firebase callable functions
 */
export const callableFunctionErrorHandler = (
  error: unknown,
  functionName: string,
  context?: functions.https.CallableContext,
  data?: Record<string, unknown>
): never => {
  // Set function context for Sentry
  setFunctionContext(functionName, {
    data,
    auth: context?.auth,
    rawRequest: context?.rawRequest,
  });

  let functionError: FunctionError;

  if (error instanceof FunctionError) {
    functionError = error;
  } else if (error instanceof Error) {
    functionError = convertToFunctionError(error);
  } else {
    functionError = new FunctionError(
      FunctionErrorType.INTERNAL_ERROR,
      'Unknown error occurred',
      500,
      undefined,
      { originalError: error }
    );
  }

  // Log error
  console.error(`[${functionName}] ${functionError.type}: ${functionError.message}`, {
    code: functionError.code,
    context: functionError.context,
    stack: functionError.stack,
  });

  // Report to Sentry for server errors
  if (functionError.statusCode >= 500 || !functionError.isOperational) {
    captureException(functionError, functionError.context, functionName);
  }

  // Throw appropriate Firebase Functions error
  const httpErrorCode = getFirebaseErrorCode(functionError.type);
  throw new functions.https.HttpsError(
    httpErrorCode,
    functionError.message,
    {
      type: functionError.type,
      code: functionError.code,
      timestamp: functionError.timestamp.toISOString(),
      ...(process.env.NODE_ENV === 'development' && {
        context: functionError.context,
      }),
    }
  );
};

/**
 * Convert generic errors to FunctionError
 */
function convertToFunctionError(error: Error): FunctionError {
  // Firebase Admin errors
  if (error.message.includes('permission-denied')) {
    return new FunctionError(
      FunctionErrorType.AUTHORIZATION_ERROR,
      'Permission denied',
      403,
      'permission-denied'
    );
  }

  if (error.message.includes('not-found')) {
    return new FunctionError(
      FunctionErrorType.NOT_FOUND_ERROR,
      'Resource not found',
      404,
      'not-found'
    );
  }

  if (error.message.includes('unauthenticated')) {
    return new FunctionError(
      FunctionErrorType.AUTHENTICATION_ERROR,
      'Authentication required',
      401,
      'unauthenticated'
    );
  }

  // Stripe errors
  if (error.message.includes('stripe') || error.name === 'StripeError') {
    return new FunctionError(
      FunctionErrorType.EXTERNAL_API_ERROR,
      `Payment processing error: ${error.message}`,
      400,
      'stripe-error'
    );
  }

  // Validation errors
  if (error.message.includes('validation') || error.name === 'ValidationError') {
    return new FunctionError(
      FunctionErrorType.VALIDATION_ERROR,
      error.message,
      400,
      'validation-error'
    );
  }

  // Network/timeout errors
  if (error.message.includes('timeout') || error.message.includes('ETIMEDOUT')) {
    return new FunctionError(
      FunctionErrorType.EXTERNAL_API_ERROR,
      'Request timeout',
      408,
      'timeout'
    );
  }

  // Default to internal error
  return new FunctionError(
    FunctionErrorType.INTERNAL_ERROR,
    error.message,
    500,
    undefined,
    { originalError: error.name, stack: error.stack }
  );
}

/**
 * Convert FunctionErrorType to Firebase Functions error code
 */
function getFirebaseErrorCode(errorType: FunctionErrorType): functions.https.FunctionsErrorCode {
  const mapping: Record<FunctionErrorType, functions.https.FunctionsErrorCode> = {
    [FunctionErrorType.VALIDATION_ERROR]: 'invalid-argument',
    [FunctionErrorType.AUTHENTICATION_ERROR]: 'unauthenticated',
    [FunctionErrorType.AUTHORIZATION_ERROR]: 'permission-denied',
    [FunctionErrorType.NOT_FOUND_ERROR]: 'not-found',
    [FunctionErrorType.RATE_LIMIT_ERROR]: 'resource-exhausted',
    [FunctionErrorType.EXTERNAL_API_ERROR]: 'unavailable',
    [FunctionErrorType.DATABASE_ERROR]: 'internal',
    [FunctionErrorType.STORAGE_ERROR]: 'internal',
    [FunctionErrorType.INTERNAL_ERROR]: 'internal',
  };

  return mapping[errorType] || 'internal';
}

/**
 * Async error wrapper for callable functions
 */
export const withErrorHandler = <T extends readonly unknown[], R>(
  functionName: string,
  handler: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args);
    } catch (error) {
      return callableFunctionErrorHandler(error, functionName, undefined, args as unknown as Record<string, unknown>);
    }
  };
};

/**
 * Async error wrapper for callable functions with context
 */
export const withCallableErrorHandler = <T extends Record<string, unknown> | undefined, R>(
  functionName: string,
  handler: (data: T, context: functions.https.CallableContext) => Promise<R>
) => {
  return async (data: T, context: functions.https.CallableContext): Promise<R> => {
    try {
      return await handler(data, context);
    } catch (error) {
      return callableFunctionErrorHandler(error, functionName, context, data);
    }
  };
};

/**
 * Create specific error types
 */
export const createErrors = {
  validation: (message: string, code?: string, context?: Record<string, unknown>) =>
    new FunctionError(FunctionErrorType.VALIDATION_ERROR, message, 400, code, context),

  authentication: (message: string = 'Authentication required', code?: string) =>
    new FunctionError(FunctionErrorType.AUTHENTICATION_ERROR, message, 401, code),

  authorization: (message: string = 'Permission denied', code?: string) =>
    new FunctionError(FunctionErrorType.AUTHORIZATION_ERROR, message, 403, code),

  notFound: (message: string = 'Resource not found', code?: string) =>
    new FunctionError(FunctionErrorType.NOT_FOUND_ERROR, message, 404, code),

  rateLimit: (message: string = 'Rate limit exceeded', code?: string) =>
    new FunctionError(FunctionErrorType.RATE_LIMIT_ERROR, message, 429, code),

  externalAPI: (message: string, code?: string, context?: Record<string, unknown>) =>
    new FunctionError(FunctionErrorType.EXTERNAL_API_ERROR, message, 503, code, context),

  database: (message: string, code?: string, context?: Record<string, unknown>) =>
    new FunctionError(FunctionErrorType.DATABASE_ERROR, message, 500, code, context),

  storage: (message: string, code?: string, context?: Record<string, unknown>) =>
    new FunctionError(FunctionErrorType.STORAGE_ERROR, message, 500, code, context),

  internal: (message: string = 'Internal server error', code?: string, context?: Record<string, unknown>) =>
    new FunctionError(FunctionErrorType.INTERNAL_ERROR, message, 500, code, context),
};

/**
 * Validation helper functions
 */
export const validate = {
  required: (value: unknown, fieldName: string) => {
    if (value === undefined || value === null || value === '') {
      throw createErrors.validation(`${fieldName} is required`);
    }
    return value;
  },

  email: (email: string, fieldName: string = 'Email') => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw createErrors.validation(`${fieldName} must be a valid email address`);
    }
    return email;
  },

  minLength: (value: string, minLength: number, fieldName: string) => {
    if (value.length < minLength) {
      throw createErrors.validation(`${fieldName} must be at least ${minLength} characters long`);
    }
    return value;
  },

  maxLength: (value: string, maxLength: number, fieldName: string) => {
    if (value.length > maxLength) {
      throw createErrors.validation(`${fieldName} must be no more than ${maxLength} characters long`);
    }
    return value;
  },

  array: (value: unknown, fieldName: string) => {
    if (!Array.isArray(value)) {
      throw createErrors.validation(`${fieldName} must be an array`);
    }
    return value;
  },

  object: (value: unknown, fieldName: string) => {
    if (typeof value !== 'object' || value === null) {
      throw createErrors.validation(`${fieldName} must be an object`);
    }
    return value;
  },
};