import React from 'react';
import { ErrorBoundary as SentryErrorBoundary } from '@sentry/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertCircle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import { captureTypedEvent, SentryEventType } from '../utils/sentry';

interface FallbackProps {
  error: Error;
  componentStack: string | null;
  eventId: string | null;
  resetError(): void;
}

const ErrorFallback: React.FC<FallbackProps> = ({
  error,
  componentStack,
  eventId,
  resetError
}) => {
  // Use React Router hooks - they must be called unconditionally
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;

  const handleGoHome = () => {
    resetError();
    navigate('/');
  };

  const handleGoBack = () => {
    resetError();
    navigate(-1);
  };

  const handleRefresh = () => {
    resetError();
    window.location.reload();
  };

  // Log the error to console in development
  React.useEffect(() => {
    console.error('Error caught by ErrorBoundary:', error);
    console.error('Component stack:', componentStack);

    // Log to Sentry as a custom event
    captureTypedEvent(SentryEventType.ERROR_BOUNDARY_TRIGGERED, {
      error_message: error.message,
      error_name: error.name,
      path: currentPath,
      event_id: eventId,
    });
  }, [error, componentStack, eventId, currentPath]);
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 text-center">
        <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <AlertCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Something went wrong
        </h1>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          We've encountered an unexpected error. Our team has been notified and is working on a fix.
        </p>
        
        {error.message && (
          <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6 text-left overflow-auto max-h-32">
            <p className="text-sm font-mono text-gray-800 dark:text-gray-300">
              {error.message}
            </p>
          </div>
        )}
        
        {eventId && (
          <p className="text-xs text-gray-500 dark:text-gray-500 mb-6">
            Error ID: {eventId}
          </p>
        )}
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={handleRefresh}
            className="flex items-center justify-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-xl transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Try Again</span>
          </button>
          
          <button
            onClick={handleGoBack}
            className="flex items-center justify-center space-x-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-xl transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Go Back</span>
          </button>
          
          <button
            onClick={handleGoHome}
            className="flex items-center justify-center space-x-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-xl transition-colors"
          >
            <Home className="w-4 h-4" />
            <span>Home</span>
          </button>
        </div>
      </div>
    </div>
  );
};

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ children }) => {
  return (
    <SentryErrorBoundary
      fallback={ErrorFallback}
      onError={(error) => {
        // You can add custom logging or analytics here
        console.error('Error caught by Sentry ErrorBoundary:', error);
      }}
    >
      {children}
    </SentryErrorBoundary>
  );
};

export default ErrorBoundary;