import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { setRouteContext } from '../utils/sentry';

/**
 * Component that tracks route changes and reports them to Sentry
 * This should be placed near the top of your component tree
 */
const SentryRouteTracker: React.FC = () => {
  const location = useLocation();
  
  useEffect(() => {
    // Set the current route in Sentry context
    setRouteContext(location.pathname);
  }, [location]);
  
  // This component doesn't render anything
  return null;
};

export default SentryRouteTracker;