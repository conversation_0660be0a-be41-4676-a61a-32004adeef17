import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { verifyAuth, createChatId, handleError } from "../utils/helpers";
import { Chat, Message } from "../utils/types";

// Create or get a chat between two users
export const createOrGetChat = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { otherUserId, listingId, listingTitle, listingImageURL } = data;
    
    if (!otherUserId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'otherUserId is required'
      );
    }
    
    // Prevent creating a chat with yourself
    if (otherUserId === auth.uid) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Cannot create a chat with yourself'
      );
    }
    
    // Check if the other user exists
    const otherUserDoc = await admin.firestore().collection('users').doc(otherUserId).get();
    
    if (!otherUserDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'User not found'
      );
    }
    
    // Create a deterministic chat ID
    const chatId = createChatId(auth.uid, otherUserId);
    
    // Check if the chat already exists
    const chatDoc = await admin.firestore().collection('chats').doc(chatId).get();
    
    if (chatDoc.exists) {
      // If the chat exists, return it
      const chatData = chatDoc.data() as Chat;
      
      return {
        success: true,
        data: {
          ...chatData,
          id: chatId
        }
      };
    }
    
    // If the chat doesn't exist, create it
    const chatData: Chat = {
      id: chatId,
      participants: [auth.uid, otherUserId],
      createdAt: admin.firestore.Timestamp.now()
    };
    
    await admin.firestore().collection('chats').doc(chatId).set(chatData);
    
    // If a listing context is provided, create an initial message
    if (listingId && listingTitle) {
      const messageData: Omit<Message, 'id'> = {
        chatId,
        senderId: auth.uid,
        content: 'Hi, I\'m interested in your listing.',
        timestamp: admin.firestore.Timestamp.now(),
        read: false,
        listingContext: {
          listingId,
          title: listingTitle,
          imageURL: listingImageURL
        }
      };
      
      await admin.firestore().collection('chats').doc(chatId).collection('messages').add(messageData);
      
      // Update the chat with the last message
      await admin.firestore().collection('chats').doc(chatId).update({
        lastMessage: messageData,
        updatedAt: admin.firestore.Timestamp.now()
      });
    }
    
    return {
      success: true,
      data: {
        ...chatData,
        id: chatId
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Send a message in a chat
export const sendMessage = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { chatId, content, listingContext } = data;
    
    if (!chatId || !content) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'chatId and content are required'
      );
    }
    
    // Check if the chat exists
    const chatDoc = await admin.firestore().collection('chats').doc(chatId).get();
    
    if (!chatDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Chat not found'
      );
    }
    
    const chatData = chatDoc.data() as Chat;
    
    // Check if the user is a participant in the chat
    if (!chatData.participants.includes(auth.uid)) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'You are not a participant in this chat'
      );
    }
    
    // Create the message
    const messageData: Omit<Message, 'id'> = {
      chatId,
      senderId: auth.uid,
      content,
      timestamp: admin.firestore.Timestamp.now(),
      read: false
    };
    
    // Add listing context if provided
    if (listingContext) {
      messageData.listingContext = listingContext;
    }
    
    // Add the message to the chat
    const messageRef = await admin.firestore()
      .collection('chats')
      .doc(chatId)
      .collection('messages')
      .add(messageData);
    
    // Update the chat with the last message
    await admin.firestore().collection('chats').doc(chatId).update({
      lastMessage: messageData,
      updatedAt: admin.firestore.Timestamp.now()
    });
    
    return {
      success: true,
      data: {
        id: messageRef.id,
        ...messageData
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get messages from a chat
export const getMessages = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { chatId, limit = 50, lastVisible } = data;
    
    if (!chatId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'chatId is required'
      );
    }
    
    // Check if the chat exists
    const chatDoc = await admin.firestore().collection('chats').doc(chatId).get();
    
    if (!chatDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Chat not found'
      );
    }
    
    const chatData = chatDoc.data() as Chat;
    
    // Check if the user is a participant in the chat
    if (!chatData.participants.includes(auth.uid)) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'You are not a participant in this chat'
      );
    }
    
    // Query messages
    let query = admin.firestore()
      .collection('chats')
      .doc(chatId)
      .collection('messages')
      .orderBy('timestamp', 'desc')
      .limit(limit);
    
    // Handle pagination
    if (lastVisible) {
      const lastDoc = await admin.firestore()
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(lastVisible)
        .get();
      
      if (lastDoc.exists) {
        query = query.startAfter(lastDoc);
      }
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const messages: Message[] = [];
    let lastVisibleId: string | null = null;
    
    snapshot.forEach(doc => {
      messages.push({
        id: doc.id,
        ...doc.data() as Message
      });
      
      // Set the last visible document ID for pagination
      lastVisibleId = doc.id;
    });
    
    // Mark messages as read
    const batch = admin.firestore().batch();
    let updateCount = 0;
    
    for (const message of messages) {
      if (!message.read && message.senderId !== auth.uid) {
        const messageRef = admin.firestore()
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(message.id!);
        
        batch.update(messageRef, { read: true });
        updateCount++;
      }
    }
    
    if (updateCount > 0) {
      await batch.commit();
    }
    
    return {
      success: true,
      data: {
        messages: messages.reverse(), // Return in chronological order
        lastVisible: lastVisibleId
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get all chats for a user
export const getChats = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    // Query chats where the user is a participant
    const snapshot = await admin.firestore()
      .collection('chats')
      .where('participants', 'array-contains', auth.uid)
      .orderBy('updatedAt', 'desc')
      .get();
    
    // Process results
    const chats: Chat[] = [];
    const userIds = new Set<string>();
    
    snapshot.forEach(doc => {
      const chatData = doc.data() as Chat;
      
      chats.push({
        ...chatData,
        id: doc.id
      });
      
      // Collect user IDs for fetching user data
      chatData.participants.forEach(uid => {
        if (uid !== auth.uid) {
          userIds.add(uid);
        }
      });
    });
    
    // Fetch user data for all participants
    const users: { [uid: string]: FirebaseFirestore.DocumentData } = {};
    
    await Promise.all(Array.from(userIds).map(async (uid) => {
      const userDoc = await admin.firestore().collection('users').doc(uid).get();
      
      if (userDoc.exists) {
        const userData = userDoc.data();
        users[uid] = {
          uid,
          name: userData?.name,
          profilePictureURL: userData?.profilePictureURL
        };
      }
    }));
    
    return {
      success: true,
      data: {
        chats,
        users
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Mark all messages in a chat as read
export const markChatAsRead = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { chatId } = data;
    
    if (!chatId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'chatId is required'
      );
    }
    
    // Check if the chat exists
    const chatDoc = await admin.firestore().collection('chats').doc(chatId).get();
    
    if (!chatDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Chat not found'
      );
    }
    
    const chatData = chatDoc.data() as Chat;
    
    // Check if the user is a participant in the chat
    if (!chatData.participants.includes(auth.uid)) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'You are not a participant in this chat'
      );
    }
    
    // Get all unread messages sent by the other user
    const snapshot = await admin.firestore()
      .collection('chats')
      .doc(chatId)
      .collection('messages')
      .where('senderId', '!=', auth.uid)
      .where('read', '==', false)
      .get();
    
    if (snapshot.empty) {
      return { success: true, data: { count: 0 } };
    }
    
    // Mark all messages as read
    const batch = admin.firestore().batch();
    
    snapshot.forEach(doc => {
      batch.update(doc.ref, { read: true });
    });
    
    await batch.commit();
    
    return {
      success: true,
      data: {
        count: snapshot.size
      }
    };
  } catch (error) {
    return handleError(error);
  }
});