import { User } from '../firebase/types';

/**
 * Utility functions for checking user permissions
 */

export interface PermissionContext {
  currentUser: User | null;
  targetUserId?: string;
  resourceOwnerId?: string;
}

/**
 * Check if the current user can edit a profile
 */
export const canEditProfile = (context: PermissionContext): boolean => {
  const { currentUser, targetUserId } = context;
  
  if (!currentUser || !targetUserId) {
    return false;
  }
  
  // Users can only edit their own profile, or admins can edit any profile
  return currentUser.uid === targetUserId || currentUser.role === 'admin';
};

/**
 * Check if the current user can view a profile
 */
export const canViewProfile = (context: PermissionContext): boolean => {
  const { currentUser } = context;
  
  // Anyone can view public profiles if authenticated
  return !!currentUser;
};

/**
 * Check if the current user can edit a listing
 */
export const canEditListing = (context: PermissionContext): boolean => {
  const { currentUser, resourceOwnerId } = context;
  
  if (!currentUser || !resourceOwnerId) {
    return false;
  }
  
  // Users can only edit their own listings, or admins can edit any listing
  return currentUser.uid === resourceOwnerId || currentUser.role === 'admin';
};

/**
 * Check if the current user can delete a listing
 */
export const canDeleteListing = (context: PermissionContext): boolean => {
  const { currentUser, resourceOwnerId } = context;
  
  if (!currentUser || !resourceOwnerId) {
    return false;
  }
  
  // Users can only delete their own listings, or admins can delete any listing
  return currentUser.uid === resourceOwnerId || currentUser.role === 'admin';
};

/**
 * Check if the current user can view an order
 */
export const canViewOrder = (context: PermissionContext & { buyerId?: string; sellerId?: string }): boolean => {
  const { currentUser, buyerId, sellerId } = context;
  
  if (!currentUser) {
    return false;
  }
  
  // Users can view orders where they are the buyer or seller, or admins can view any order
  return currentUser.uid === buyerId || 
         currentUser.uid === sellerId || 
         currentUser.role === 'admin';
};

/**
 * Check if the current user can update an order
 */
export const canUpdateOrder = (context: PermissionContext & { sellerId?: string }): boolean => {
  const { currentUser, sellerId } = context;
  
  if (!currentUser) {
    return false;
  }
  
  // Only sellers can update order status, or admins can update any order
  return currentUser.uid === sellerId || currentUser.role === 'admin';
};

/**
 * Check if the current user can view a chat
 */
export const canViewChat = (context: PermissionContext & { participants?: string[] }): boolean => {
  const { currentUser, participants } = context;
  
  if (!currentUser || !participants) {
    return false;
  }
  
  // Users can only view chats they are participants in
  return participants.includes(currentUser.uid);
};

/**
 * Check if the current user can send messages in a chat
 */
export const canSendMessage = (context: PermissionContext & { participants?: string[] }): boolean => {
  const { currentUser, participants } = context;
  
  if (!currentUser || !participants) {
    return false;
  }
  
  // Users can only send messages in chats they are participants in
  return participants.includes(currentUser.uid);
};

/**
 * Check if the current user is an admin
 */
export const isAdmin = (currentUser: User | null): boolean => {
  return currentUser?.role === 'admin';
};

/**
 * Check if the current user is a merchant
 */
export const isMerchant = (currentUser: User | null): boolean => {
  return currentUser?.role === 'merchant';
};

/**
 * Check if the current user is a student
 */
export const isStudent = (currentUser: User | null): boolean => {
  return currentUser?.role === 'student';
};

/**
 * Check if the current user can access admin features
 */
export const canAccessAdmin = (currentUser: User | null): boolean => {
  return isAdmin(currentUser);
};

/**
 * Check if the current user can access merchant features
 */
export const canAccessMerchant = (currentUser: User | null): boolean => {
  return isMerchant(currentUser) || isAdmin(currentUser);
};

/**
 * Check if users are from the same university (for listing visibility)
 */
export const isSameUniversity = (user1: User | null, user2: User | null): boolean => {
  if (!user1 || !user2) {
    return false;
  }
  
  return user1.university === user2.university;
};

/**
 * Check if the current user can view a listing based on visibility settings
 */
export const canViewListing = (
  context: PermissionContext & { 
    listingOwner?: User; 
    visibility?: 'university' | 'public' 
  }
): boolean => {
  const { currentUser, listingOwner, visibility } = context;
  
  if (!currentUser) {
    return false;
  }
  
  // Users can always view their own listings
  if (listingOwner && currentUser.uid === listingOwner.uid) {
    return true;
  }
  
  // Admins can view all listings
  if (isAdmin(currentUser)) {
    return true;
  }
  
  // For university visibility, check if users are from the same university
  if (visibility === 'university') {
    return isSameUniversity(currentUser, listingOwner);
  }
  
  // For public visibility, anyone can view
  if (visibility === 'public') {
    return true;
  }
  
  // Default to university visibility
  return isSameUniversity(currentUser, listingOwner);
};

/**
 * Get user role display name
 */
export const getRoleDisplayName = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'Administrator';
    case 'merchant':
      return 'Merchant';
    case 'student':
      return 'Student';
    default:
      return 'User';
  }
};

/**
 * Check if a user has elevated permissions (admin or merchant)
 */
export const hasElevatedPermissions = (currentUser: User | null): boolean => {
  return isAdmin(currentUser) || isMerchant(currentUser);
};

/**
 * Validate that a user can perform an action, throwing an error if not
 */
export const validatePermission = (
  hasPermission: boolean, 
  errorMessage: string = 'You do not have permission to perform this action'
): void => {
  if (!hasPermission) {
    throw new Error(errorMessage);
  }
};

/**
 * Get permission error message for UI display
 */
export const getPermissionErrorMessage = (action: string): string => {
  return `You don't have permission to ${action}. Please contact support if you believe this is an error.`;
};
