import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import * as path from "path";
import * as os from "os";
import * as fs from "fs";
import Busboy from "busboy";
import { isValidFileType, verifyAuth, handleError } from "../utils/helpers";

// Generate a signed URL for uploading images directly to Firebase Storage
export const getSignedUploadUrl = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { fileName, contentType, uploadType } = data;
    
    if (!fileName || !contentType || !uploadType) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'fileName, contentType, and uploadType are required'
      );
    }
    
    // Validate file type
    if (!isValidFileType(fileName)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid file type. Only .jpg, .jpeg, and .png files are allowed'
      );
    }
    
    // Determine the storage path based on upload type
    let storagePath = '';
    
    switch (uploadType) {
      case 'profile':
        storagePath = `users/${auth.uid}/profile/${fileName}`;
        break;
      case 'listing':
        storagePath = `listings/${auth.uid}/${Date.now()}_${fileName}`;
        break;
      case 'issue':
        storagePath = `issues/${auth.uid}/${Date.now()}_${fileName}`;
        break;
      default:
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Invalid upload type. Must be one of: profile, listing, issue'
        );
    }
    
    // Create a storage reference
    const bucket = admin.storage().bucket();
    const file = bucket.file(storagePath);
    
    // Generate a signed URL for uploading
    const [signedUrl] = await file.getSignedUrl({
      version: 'v4',
      action: 'write',
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
      contentType
    });
    
    // Generate a download URL for after upload
    const [downloadUrl] = await file.getSignedUrl({
      version: 'v4',
      action: 'read',
      expires: Date.now() + 365 * 24 * 60 * 60 * 1000 // 1 year
    });
    
    return {
      success: true,
      data: {
        signedUrl,
        downloadUrl,
        storagePath
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// HTTP function for direct file uploads (alternative to signed URLs)
export const uploadFile = functions.https.onRequest(async (req, res) => {
  // Set CORS headers
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'POST');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }
  
  if (req.method !== 'POST') {
    res.status(405).send('Method Not Allowed');
    return;
  }
  
  try {
    // Verify authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).send('Unauthorized');
      return;
    }
    
    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    const uid = decodedToken.uid;
    
    if (!uid) {
      res.status(401).send('Unauthorized');
      return;
    }
    
    // Parse the multipart form data
    const busboy = Busboy({ headers: req.headers });
    const tmpdir = os.tmpdir();
    const uploads: { [fieldname: string]: { filepath: string, filename: string, mimetype: string } } = {};
    const fields: { [fieldname: string]: string } = {};
    
    // Process fields
    busboy.on('field', (fieldname: string, val: string) => {
      fields[fieldname] = val;
    });
    
    // Process files
    busboy.on('file', (fieldname: string, file: NodeJS.ReadableStream, { filename, mimetype }: { filename: string, mimetype: string }) => {
      if (!filename) {
        return;
      }
      
      // Validate file type
      if (!isValidFileType(filename)) {
        res.status(400).send('Invalid file type. Only .jpg, .jpeg, and .png files are allowed');
        return;
      }
      
      // Create a temp file path
      const filepath = path.join(tmpdir, filename);
      uploads[fieldname] = { filepath, filename, mimetype };
      
      // Write the file
      const writeStream = fs.createWriteStream(filepath);
      file.pipe(writeStream);
    });
    
    // Handle the end of form data
    busboy.on('finish', async () => {
      try {
        const uploadType = fields.uploadType;
        
        if (!uploadType) {
          res.status(400).send('uploadType is required');
          return;
        }
        
        const bucket = admin.storage().bucket();
        const uploadResults = [];
        
        // Upload each file to Firebase Storage
        for (const fieldname in uploads) {
          const { filepath, filename, mimetype } = uploads[fieldname];
          
          // Determine the storage path based on upload type
          let storagePath = '';
          
          switch (uploadType) {
            case 'profile':
              storagePath = `users/${uid}/profile/${filename}`;
              break;
            case 'listing':
              storagePath = `listings/${uid}/${Date.now()}_${filename}`;
              break;
            case 'issue':
              storagePath = `issues/${uid}/${Date.now()}_${filename}`;
              break;
            default:
              res.status(400).send('Invalid upload type. Must be one of: profile, listing, issue');
              return;
          }
          
          // Upload the file
          await bucket.upload(filepath, {
            destination: storagePath,
            metadata: {
              contentType: mimetype,
              metadata: {
                firebaseStorageDownloadTokens: uid
              }
            }
          });
          
          // Get the download URL
          const file = bucket.file(storagePath);
          const [downloadUrl] = await file.getSignedUrl({
            version: 'v4',
            action: 'read',
            expires: Date.now() + 365 * 24 * 60 * 60 * 1000 // 1 year
          });
          
          // Clean up the temp file
          fs.unlinkSync(filepath);
          
          uploadResults.push({
            fieldname,
            filename,
            downloadUrl,
            storagePath
          });
        }
        
        res.status(200).json({
          success: true,
          data: uploadResults
        });
      } catch (error) {
        console.error('Error processing uploads:', error);
        res.status(500).send('Internal Server Error');
      }
    });
    
    busboy.end(req.rawBody);
  } catch (error) {
    console.error('Error in uploadFile:', error);
    res.status(500).send('Internal Server Error');
  }
});

// Delete a file from Firebase Storage
export const deleteFile = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);
    
    const { storagePath } = data;
    
    if (!storagePath) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'storagePath is required'
      );
    }
    
    // Verify that the user owns the file
    if (!storagePath.includes(`/${auth.uid}/`)) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'You can only delete your own files'
      );
    }
    
    // Delete the file
    const bucket = admin.storage().bucket();
    await bucket.file(storagePath).delete();
    
    return { success: true };
  } catch (error) {
    return handleError(error);
  }
});