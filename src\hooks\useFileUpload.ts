import { useState, useCallback } from 'react';
import { uploadFile, deleteFile } from '../firebase';

interface UploadProgress {
  file: File;
  progress: number;
  error?: string;
  url?: string;
  storagePath?: string;
}

export const useFileUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Upload a single file
  const uploadSingleFile = useCallback(async (
    file: File,
    uploadType: 'profile' | 'listing' | 'issue'
  ) => {
    setIsUploading(true);
    setError(null);
    
    try {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Invalid file type. Only JPG and PNG files are allowed.');
      }
      
      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size exceeds 10MB limit.');
      }
      
      // Upload the file
      const result = await uploadFile(file, uploadType);
      
      setIsUploading(false);
      return result;
    } catch (err: unknown) {
      setIsUploading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to upload file';
      setError(errorMsg);
      throw err;
    }
  }, []);

  // Upload multiple files with progress tracking
  const uploadMultipleFiles = useCallback(async (
    files: File[],
    uploadType: 'profile' | 'listing' | 'issue',
    maxFiles: number = 8
  ) => {
    if (files.length > maxFiles) {
      setError(`You can only upload up to ${maxFiles} files.`);
      return [];
    }
    
    setIsUploading(true);
    setError(null);
    
    // Initialize progress tracking
    const initialProgress = files.map(file => ({
      file,
      progress: 0
    }));
    
    setUploadProgress(initialProgress);
    
    const uploadResults = [];
    
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        try {
          // Update progress to indicate starting
          setUploadProgress(prev => 
            prev.map((item, index) => 
              index === i ? { ...item, progress: 10 } : item
            )
          );
          
          // Upload the file
          const result = await uploadFile(file, uploadType);
          
          // Update progress to indicate completion
          setUploadProgress(prev => 
            prev.map((item, index) => 
              index === i ? { 
                ...item, 
                progress: 100,
                url: result.downloadURL,
                storagePath: result.storagePath
              } : item
            )
          );
          
          uploadResults.push(result);
        } catch (err: unknown) {
          // Update progress to indicate error
          const errorMsg = err instanceof Error ? err.message : 'Upload failed';
          setUploadProgress(prev => 
            prev.map((item, index) => 
              index === i ? { 
                ...item, 
                progress: 0,
                error: errorMsg
              } : item
            )
          );
        }
      }
      
      setIsUploading(false);
      return uploadResults;
    } catch (err: unknown) {
      setIsUploading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to upload files';
      setError(errorMsg);
      throw err;
    }
  }, []);

  // Delete a file
  const removeFile = useCallback(async (storagePath: string) => {
    try {
      await deleteFile(storagePath);
      return true;
    } catch (err: unknown) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to delete file';
      setError(errorMsg);
      throw err;
    }
  }, []);

  return {
    isUploading,
    uploadProgress,
    error,
    uploadSingleFile,
    uploadMultipleFiles,
    removeFile
  };
};