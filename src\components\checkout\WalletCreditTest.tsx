import React, { useState } from 'react';
import WalletCreditSelector from './WalletCreditSelector';

const WalletCreditTest: React.FC = () => {
  const [appliedCredit, setAppliedCredit] = useState(0);
  const [testScenario, setTestScenario] = useState(1);

  const scenarios = [
    {
      id: 1,
      name: 'Normal Purchase',
      walletBalance: 15.50,
      itemPrice: 25.00,
      shippingFee: 5.99
    },
    {
      id: 2,
      name: 'High Wallet Balance',
      walletBalance: 50.00,
      itemPrice: 20.00,
      shippingFee: 5.99
    },
    {
      id: 3,
      name: 'Low Wallet Balance',
      walletBalance: 3.25,
      itemPrice: 25.00,
      shippingFee: 5.99
    },
    {
      id: 4,
      name: 'Zero Wallet Balance',
      walletBalance: 0,
      itemPrice: 25.00,
      shippingFee: 5.99
    },
    {
      id: 5,
      name: 'Exact Match',
      walletBalance: 30.99,
      itemPrice: 25.00,
      shippingFee: 5.99
    }
  ];

  const currentScenario = scenarios.find(s => s.id === testScenario) || scenarios[0];
  const totalPrice = currentScenario.itemPrice + currentScenario.shippingFee;
  const finalAmount = Math.max(0, totalPrice - appliedCredit);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Wallet Credit Selector Test
        </h1>
        
        {/* Scenario Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Test Scenario:
          </label>
          <select
            value={testScenario}
            onChange={(e) => setTestScenario(parseInt(e.target.value))}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
          >
            {scenarios.map(scenario => (
              <option key={scenario.id} value={scenario.id}>
                {scenario.name} - Wallet: ${scenario.walletBalance.toFixed(2)}, Item: ${scenario.itemPrice.toFixed(2)}
              </option>
            ))}
          </select>
        </div>

        {/* Current Scenario Details */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
            Current Scenario: {currentScenario.name}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Wallet Balance:</span>
              <p className="font-semibold text-gray-900 dark:text-white">
                ${currentScenario.walletBalance.toFixed(2)}
              </p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Item Price:</span>
              <p className="font-semibold text-gray-900 dark:text-white">
                ${currentScenario.itemPrice.toFixed(2)}
              </p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Shipping Fee:</span>
              <p className="font-semibold text-gray-900 dark:text-white">
                ${currentScenario.shippingFee.toFixed(2)}
              </p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Total Price:</span>
              <p className="font-semibold text-gray-900 dark:text-white">
                ${totalPrice.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Wallet Credit Selector */}
      <WalletCreditSelector
        walletBalance={currentScenario.walletBalance}
        itemPrice={currentScenario.itemPrice}
        shippingFee={currentScenario.shippingFee}
        onCreditChange={setAppliedCredit}
        isLoading={false}
        disabled={false}
      />

      {/* Results Display */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Calculation Results
        </h2>
        
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600 dark:text-gray-400">Applied Wallet Credit:</span>
            <span className="font-semibold text-green-600 dark:text-green-400">
              ${appliedCredit.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600 dark:text-gray-400">Amount to Charge via Stripe:</span>
            <span className="font-semibold text-blue-600 dark:text-blue-400">
              ${finalAmount.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600 dark:text-gray-400">Remaining Wallet Balance:</span>
            <span className="font-semibold text-gray-900 dark:text-white">
              ${(currentScenario.walletBalance - appliedCredit).toFixed(2)}
            </span>
          </div>
          
          <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
            <div className="flex justify-between items-center">
              <span className="font-semibold text-gray-900 dark:text-white">Validation Status:</span>
              <span className={`font-semibold ${
                appliedCredit <= currentScenario.walletBalance && 
                appliedCredit <= totalPrice && 
                appliedCredit >= 0
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {appliedCredit <= currentScenario.walletBalance && 
                 appliedCredit <= totalPrice && 
                 appliedCredit >= 0 ? 'Valid' : 'Invalid'}
              </span>
            </div>
          </div>
        </div>

        {/* Validation Details */}
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Validation Checks:
          </h4>
          <div className="space-y-1 text-xs">
            <div className={`flex items-center space-x-2 ${
              appliedCredit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            }`}>
              <span>{appliedCredit >= 0 ? '✓' : '✗'}</span>
              <span>Credit amount is non-negative</span>
            </div>
            <div className={`flex items-center space-x-2 ${
              appliedCredit <= currentScenario.walletBalance ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            }`}>
              <span>{appliedCredit <= currentScenario.walletBalance ? '✓' : '✗'}</span>
              <span>Credit does not exceed wallet balance</span>
            </div>
            <div className={`flex items-center space-x-2 ${
              appliedCredit <= totalPrice ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            }`}>
              <span>{appliedCredit <= totalPrice ? '✓' : '✗'}</span>
              <span>Credit does not exceed item total</span>
            </div>
            <div className={`flex items-center space-x-2 ${
              finalAmount >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            }`}>
              <span>{finalAmount >= 0 ? '✓' : '✗'}</span>
              <span>Final amount is non-negative</span>
            </div>
          </div>
        </div>

        {/* Stripe Minimum Charge Warning */}
        {finalAmount > 0 && finalAmount < 0.50 && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-yellow-600 dark:text-yellow-400">⚠️</span>
              <span className="text-sm text-yellow-800 dark:text-yellow-300">
                Warning: Final amount (${finalAmount.toFixed(2)}) is below Stripe's minimum charge of $0.50
              </span>
            </div>
          </div>
        )}

        {/* Zero Charge Info */}
        {finalAmount === 0 && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-blue-600 dark:text-blue-400">ℹ️</span>
              <span className="text-sm text-blue-800 dark:text-blue-300">
                Full payment covered by wallet credit. A minimum processing fee may still apply.
              </span>
            </div>
          </div>
        )}
      </div>

      {/* API Payload Preview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          API Payload Preview
        </h2>
        <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-sm overflow-x-auto">
{JSON.stringify({
  listingId: "example-listing-id",
  useWalletBalance: appliedCredit > 0,
  orderDetails: {
    type: "buy",
    price: currentScenario.itemPrice,
    appliedWalletCredit: appliedCredit,
    shippingAddress: {
      fullName: "Test User",
      phone: "+1234567890",
      line1: "123 Test St",
      city: "Test City",
      state: "TS",
      zipCode: "12345",
      country: "US"
    }
  }
}, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default WalletCreditTest;
