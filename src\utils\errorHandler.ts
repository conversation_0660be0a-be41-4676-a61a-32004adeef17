﻿/**
 * Comprehensive error handling system for Hive Campus
 * Provides consistent error handling, logging, and user feedback
 */

import { captureException, captureTypedEvent, SentryEventType } from './sentry';
import { FirebaseError } from 'firebase/app';

/**
 * Error types for the application
 */
export enum AppErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  FIREBASE_ERROR = 'FIREBASE_ERROR',
  STRIPE_ERROR = 'STRIPE_ERROR',
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
}

/**
 * Severity levels for errors
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Application error class
 */
export class AppError extends Error {
  public readonly type: AppErrorType;
  public readonly severity: ErrorSeverity;
  public readonly code?: string;
  public readonly context?: Record<string, unknown>;
  public readonly userMessage: string;
  public readonly timestamp: Date;

  constructor(
    type: AppErrorType,
    message: string,
    userMessage?: string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    code?: string,
    context?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.severity = severity;
    this.code = code;
    this.context = context;
    this.userMessage = userMessage || this.getDefaultUserMessage(type);
    this.timestamp = new Date();

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  private getDefaultUserMessage(type: AppErrorType): string {
    const messages: Record<AppErrorType, string> = {
      [AppErrorType.NETWORK_ERROR]: 'Network connection issue. Please check your internet connection and try again.',
      [AppErrorType.AUTHENTICATION_ERROR]: 'Please log in to continue.',
      [AppErrorType.AUTHORIZATION_ERROR]: 'You don\'t have permission to perform this action.',
      [AppErrorType.VALIDATION_ERROR]: 'Please check your input and try again.',
      [AppErrorType.NOT_FOUND_ERROR]: 'The requested item was not found.',
      [AppErrorType.RATE_LIMIT_ERROR]: 'Too many requests. Please wait a moment and try again.',
      [AppErrorType.SERVER_ERROR]: 'Server error. Our team has been notified.',
      [AppErrorType.CLIENT_ERROR]: 'Something went wrong. Please try again.',
      [AppErrorType.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
      [AppErrorType.FIREBASE_ERROR]: 'Service temporarily unavailable. Please try again.',
      [AppErrorType.STRIPE_ERROR]: 'Payment processing error. Please try again.',
      [AppErrorType.FILE_UPLOAD_ERROR]: 'File upload failed. Please try again.',
    };

    return messages[type] || 'An error occurred. Please try again.';
  }

  /**
   * Convert to plain object for logging
   */
  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      type: this.type,
      message: this.message,
      userMessage: this.userMessage,
      severity: this.severity,
      code: this.code,
      context: this.context,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack,
    };
  }
}

/**
 * Error handler utility class
 */
export class ErrorHandler {
  /**
   * Handle and process errors consistently
   */
  static handle(error: Error | AppError | FirebaseError | unknown, context?: Record<string, unknown>): AppError {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
    } else if (error instanceof Error) {
      appError = this.convertToAppError(error, context);
    } else {
      appError = new AppError(
        AppErrorType.UNKNOWN_ERROR,
        'Unknown error occurred',
        undefined,
        ErrorSeverity.MEDIUM,
        undefined,
        { originalError: error, ...context }
      );
    }

    // Log the error
    this.logError(appError);

    // Report to Sentry if critical or high severity
    if (appError.severity === ErrorSeverity.CRITICAL || appError.severity === ErrorSeverity.HIGH) {
      this.reportToSentry(appError);
    }

    return appError;
  }

  /**
   * Convert various error types to AppError
   */
  private static convertToAppError(error: Error, context?: Record<string, unknown>): AppError {
    // Firebase errors
    if ('code' in error && 'message' in error) {
      const firebaseError = error as FirebaseError;
      return this.handleFirebaseError(firebaseError, context);
    }

    // Network errors
    if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      return new AppError(
        AppErrorType.NETWORK_ERROR,
        error.message,
        undefined,
        ErrorSeverity.MEDIUM,
        undefined,
        context
      );
    }

    // Validation errors
    if (error.name === 'ValidationError' || error.message.includes('validation')) {
      return new AppError(
        AppErrorType.VALIDATION_ERROR,
        error.message,
        undefined,
        ErrorSeverity.LOW,
        undefined,
        context
      );
    }

    // Rate limit errors
    if (error.message.includes('rate limit') || error.message.includes('429')) {
      return new AppError(
        AppErrorType.RATE_LIMIT_ERROR,
        error.message,
        undefined,
        ErrorSeverity.MEDIUM,
        '429',
        context
      );
    }

    // Default to client error
    return new AppError(
      AppErrorType.CLIENT_ERROR,
      error.message,
      undefined,
      ErrorSeverity.MEDIUM,
      undefined,
      { originalError: error, ...context }
    );
  }

  /**
   * Handle Firebase-specific errors
   */
  private static handleFirebaseError(error: FirebaseError, context?: Record<string, unknown>): AppError {
    const errorMappings: Record<string, { type: AppErrorType; severity: ErrorSeverity; userMessage?: string }> = {
      // Auth errors
      'auth/user-not-found': {
        type: AppErrorType.AUTHENTICATION_ERROR,
        severity: ErrorSeverity.LOW,
        userMessage: 'User account not found. Please check your email or sign up.',
      },
      'auth/wrong-password': {
        type: AppErrorType.AUTHENTICATION_ERROR,
        severity: ErrorSeverity.LOW,
        userMessage: 'Incorrect password. Please try again.',
      },
      'auth/email-already-in-use': {
        type: AppErrorType.VALIDATION_ERROR,
        severity: ErrorSeverity.LOW,
        userMessage: 'An account with this email already exists.',
      },
      'auth/weak-password': {
        type: AppErrorType.VALIDATION_ERROR,
        severity: ErrorSeverity.LOW,
        userMessage: 'Password is too weak. Please choose a stronger password.',
      },
      'auth/invalid-email': {
        type: AppErrorType.VALIDATION_ERROR,
        severity: ErrorSeverity.LOW,
        userMessage: 'Please enter a valid email address.',
      },
      'auth/user-disabled': {
        type: AppErrorType.AUTHORIZATION_ERROR,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Your account has been disabled. Please contact support.',
      },
      'auth/too-many-requests': {
        type: AppErrorType.RATE_LIMIT_ERROR,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Too many failed attempts. Please try again later.',
      },

      // Firestore errors
      'permission-denied': {
        type: AppErrorType.AUTHORIZATION_ERROR,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'You don\'t have permission to access this data.',
      },
      'not-found': {
        type: AppErrorType.NOT_FOUND_ERROR,
        severity: ErrorSeverity.LOW,
        userMessage: 'The requested data was not found.',
      },
      'unavailable': {
        type: AppErrorType.SERVER_ERROR,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Service temporarily unavailable. Please try again.',
      },
      'deadline-exceeded': {
        type: AppErrorType.SERVER_ERROR,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Request timed out. Please try again.',
      },

      // Storage errors
      'storage/unauthorized': {
        type: AppErrorType.AUTHORIZATION_ERROR,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'You don\'t have permission to upload files.',
      },
      'storage/canceled': {
        type: AppErrorType.CLIENT_ERROR,
        severity: ErrorSeverity.LOW,
        userMessage: 'Upload was canceled.',
      },
      'storage/quota-exceeded': {
        type: AppErrorType.SERVER_ERROR,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Storage quota exceeded. Please contact support.',
      },
    };

    const mapping = errorMappings[error.code];
    if (mapping) {
      return new AppError(
        mapping.type,
        error.message,
        mapping.userMessage,
        mapping.severity,
        error.code,
        context
      );
    }

    // Default Firebase error handling
    return new AppError(
      AppErrorType.FIREBASE_ERROR,
      error.message,
      undefined,
      ErrorSeverity.MEDIUM,
      error.code,
      context
    );
  }

  /**
   * Log error to console with proper formatting
   */
  private static logError(error: AppError): void {
    const logData = {
      ...error.toJSON(),
      timestamp: new Date().toISOString(),
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('ðŸš¨ CRITICAL ERROR:', logData);
        break;
      case ErrorSeverity.HIGH:
        console.error('âŒ HIGH ERROR:', logData);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('âš ï¸ MEDIUM ERROR:', logData);
        break;
      case ErrorSeverity.LOW:
        console.info('â„¹ï¸ LOW ERROR:', logData);
        break;
    }
  }

  /**
   * Report error to Sentry
   */
  private static reportToSentry(error: AppError): void {
    captureException(error, {
      errorType: error.type,
      severity: error.severity,
      code: error.code,
      userMessage: error.userMessage,
      ...error.context,
    });

    // Log specific events for tracking
    captureTypedEvent(SentryEventType.ERROR_BOUNDARY_TRIGGERED, {
      errorType: error.type,
      severity: error.severity,
      message: error.message,
    });
  }

  /**
   * Create error for API responses
   */
  static createAPIError(response: Response, context?: Record<string, unknown>): AppError {
    const status = response.status;
    const statusText = response.statusText;

    if (status >= 400 && status < 500) {
      const type = status === 401 ? AppErrorType.AUTHENTICATION_ERROR :
                   status === 403 ? AppErrorType.AUTHORIZATION_ERROR :
                   status === 404 ? AppErrorType.NOT_FOUND_ERROR :
                   status === 429 ? AppErrorType.RATE_LIMIT_ERROR :
                   AppErrorType.CLIENT_ERROR;

      return new AppError(
        type,
        `API Error: ${statusText}`,
        undefined,
        ErrorSeverity.MEDIUM,
        status.toString(),
        { url: response.url, ...context }
      );
    }

    if (status >= 500) {
      return new AppError(
        AppErrorType.SERVER_ERROR,
        `Server Error: ${statusText}`,
        undefined,
        ErrorSeverity.HIGH,
        status.toString(),
        { url: response.url, ...context }
      );
    }

    return new AppError(
      AppErrorType.UNKNOWN_ERROR,
      `Unknown API Error: ${statusText}`,
      undefined,
      ErrorSeverity.MEDIUM,
      status.toString(),
      { url: response.url, ...context }
    );
  }

  /**
   * Create error for async operations
   */
  static createAsyncError(operation: string, originalError: unknown, context?: Record<string, unknown>): AppError {
    return this.handle(originalError, {
      operation,
      timestamp: new Date().toISOString(),
      ...context,
    });
  }

  /**
   * Retry mechanism for operations
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    context?: Record<string, unknown>
  ): Promise<T> {
    let lastError: unknown;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries) {
          throw this.handle(error, {
            operation: 'retry_operation',
            attempt,
            maxRetries,
            ...context,
          });
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }

    throw this.handle(lastError, {
      operation: 'retry_operation_failed',
      maxRetries,
      ...context,
    });
  }
}

/**
 * Global error handler for unhandled errors
 */
export const setupGlobalErrorHandling = () => {
  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    const error = ErrorHandler.handle(event.error, {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      type: 'uncaught_error',
    });

    console.error('Uncaught error:', error);
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = ErrorHandler.handle(event.reason, {
      type: 'unhandled_promise_rejection',
    });

    console.error('Unhandled promise rejection:', error);

    // Prevent default browser behavior
    event.preventDefault();
  });

  console.log('Global error handling initialized');
};

/**
 * Error boundary hook for React components
 */
export const useErrorHandler = () => {
  return {
    handleError: ErrorHandler.handle,
    createAPIError: ErrorHandler.createAPIError,
    createAsyncError: ErrorHandler.createAsyncError,
    withRetry: ErrorHandler.withRetry,
  };
};
