const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('../functions/service-account-key.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://h1c1-798a8-default-rtdb.firebaseio.com'
});

const db = admin.firestore();
const auth = admin.auth();

async function fixAdminUser() {
  try {
    const adminEmail = '<EMAIL>';
    
    console.log('Fixing admin user:', adminEmail);
    
    // Get user by email
    const userRecord = await auth.getUserByEmail(adminEmail);
    console.log('Found user:', userRecord.uid);
    
    // Set custom claims with both admin and role
    await auth.setCustomUserClaims(userRecord.uid, { 
      admin: true,
      role: 'admin'
    });
    console.log('✅ Custom claims set');
    
    // Update or create user profile in Firestore with complete admin setup
    await db.collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });
    console.log('✅ Firestore document updated');
    
    // Create admin settings document if it doesn't exist
    await db.collection('adminSettings').doc('security').set({
      adminPinRequired: true,
      sessionTimeoutMinutes: 240, // 4 hours
      maxLoginAttempts: 5,
      createdAt: admin.firestore.Timestamp.now()
    }, { merge: true });
    console.log('✅ Admin settings created');
    
    console.log('🎉 Admin user fixed successfully!');
    console.log('Admin can now login with:', adminEmail);
    console.log('Next step: Set up 8-digit PIN after login');
    
  } catch (error) {
    console.error('❌ Error fixing admin user:', error);
  }
}

// Run the fix
fixAdminUser().then(() => {
  console.log('Script completed');
  process.exit(0);
}).catch((error) => {
  console.error('Script failed:', error);
  process.exit(1);
});
