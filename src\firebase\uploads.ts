import { httpsCallable } from 'firebase/functions';
import { functions, storage, auth } from './config';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Get a signed URL for direct upload to Firebase Storage
export const getSignedUploadUrl = async (
  fileName: string,
  contentType: string,
  uploadType: 'profile' | 'listing' | 'issue' | 'chat'
) => {
  try {
    const getSignedUploadUrlFn = httpsCallable(functions, 'getSignedUploadUrl');
    const result = await getSignedUploadUrlFn({ fileName, contentType, uploadType });
    return result.data;
  } catch (error: unknown) {
    console.error('Error getting signed URL:', error);
    throw error;
  }
};

// Upload a file using the signed URL
export const uploadFileWithSignedUrl = async (signedUrl: string, file: File) => {
  try {
    const response = await fetch(signedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type
      },
      body: file
    });
    
    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }
    
    return true;
  } catch (error: unknown) {
    console.error('Error uploading file:', error);
    throw error;
  }
};

// Upload a file directly to Firebase Storage
export const uploadFile = async (
  file: File,
  uploadType: 'profile' | 'listing' | 'issue' | 'chat'
) => {
  try {
    console.log('uploadFile called with:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadType
    });

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      console.error('Invalid file type in uploadFile:', file.type);
      throw new Error('Invalid file type. Only JPG, PNG, and WebP images are allowed.');
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      console.error('File too large in uploadFile:', file.size);
      throw new Error('File size exceeds 10MB limit.');
    }

    // Get current user ID
    const userId = auth.currentUser?.uid;
    console.log('Current user ID:', userId);

    if (!userId) {
      console.error('No authenticated user');
      throw new Error('User not authenticated');
    }
    
    // Determine storage path
    let storagePath = '';
    const timestamp = Date.now();
    const fileName = `${timestamp}_${file.name}`;
    
    switch (uploadType) {
      case 'profile':
        storagePath = `users/${userId}/profile/${fileName}`;
        break;
      case 'listing':
        storagePath = `listings/${userId}/${fileName}`;
        break;
      case 'issue':
        storagePath = `issues/${userId}/${fileName}`;
        break;
      case 'chat':
        storagePath = `chats/${userId}/${fileName}`;
        break;
    }
    
    console.log('Storage path:', storagePath);

    // Create storage reference
    const storageRef = ref(storage, storagePath);
    console.log('Storage reference created');

    // Upload file
    console.log('Starting file upload to Firebase Storage...');
    await uploadBytes(storageRef, file);
    console.log('File upload completed');

    // Get download URL
    console.log('Getting download URL...');
    const downloadURL = await getDownloadURL(storageRef);
    console.log('Download URL obtained:', downloadURL);

    return {
      downloadURL,
      storagePath
    };
  } catch (error: unknown) {
    console.error('Error uploading file:', error);
    throw error;
  }
};

// Upload a chat image with optimized settings
export const uploadChatImage = async (file: File) => {
  try {
    console.log('uploadChatImage called with:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      console.error('Invalid file type:', file.type);
      throw new Error('Invalid file type. Only JPG, PNG, and WebP images are allowed.');
    }

    // Validate file size (5MB max for chat images)
    if (file.size > 5 * 1024 * 1024) {
      console.error('File too large:', file.size);
      throw new Error('Image size exceeds 5MB limit.');
    }

    console.log('File validation passed, calling uploadFile...');

    // Upload using the existing uploadFile function
    const result = await uploadFile(file, 'chat');

    console.log('uploadFile completed:', result);

    return {
      ...result,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    };
  } catch (error: unknown) {
    console.error('Error uploading chat image:', error);
    throw error;
  }
};

// Delete a file from Firebase Storage
export const deleteFile = async (storagePath: string) => {
  try {
    // Option 1: Use the Cloud Function
    const deleteFileFn = httpsCallable(functions, 'deleteFile');
    await deleteFileFn({ storagePath });

    // Option 2: Use the Storage SDK directly
    // const storageRef = ref(storage, storagePath);
    // await deleteObject(storageRef);

    return true;
  } catch (error: unknown) {
    console.error('Error deleting file:', error);
    throw error;
  }
};