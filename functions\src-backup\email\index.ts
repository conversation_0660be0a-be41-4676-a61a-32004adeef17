import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as nodemailer from 'nodemailer';
import { environmentConfig } from '../config/environment';

// Interface for onboarding reminder data
interface OnboardingReminderData {
  email: string;
  name?: string;
  pendingAmount?: number;
  orderCount?: number;
  onboardingUrl: string;
}

// Email transporter configuration
const transporter = nodemailer.createTransport({
  host: environmentConfig.email.host,
  port: environmentConfig.email.port,
  secure: environmentConfig.email.port === 465,
  auth: {
    user: environmentConfig.email.user,
    pass: environmentConfig.email.password,
  },
});

// Send payment confirmation email
export const sendPaymentConfirmationEmail = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { orderId, buyerEmail, sellerEmail, orderDetails } = data;

    if (!orderId || !buyerEmail || !orderDetails) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    // Send email to buyer
    const buyerEmailOptions = {
      from: environmentConfig.email.from,
      to: buyerEmail,
      subject: `Payment Confirmed - Order #${orderId}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0;">Payment Confirmed! 🎉</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <h2 style="color: #333;">Hi there!</h2>
            <p style="color: #666; line-height: 1.6;">
              Great news! Your payment has been successfully processed for your recent purchase on Hive Campus.
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">Order Details</h3>
              <p><strong>Order ID:</strong> #${orderId}</p>
              <p><strong>Item:</strong> ${orderDetails.title}</p>
              <p><strong>Amount:</strong> $${orderDetails.amount}</p>
              <p><strong>Seller:</strong> ${orderDetails.sellerName}</p>
            </div>
            
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h4 style="color: #1976d2; margin-top: 0;">What's Next?</h4>
              <p style="color: #333; margin-bottom: 0;">
                Your funds are securely held in escrow until you confirm delivery. 
                Once you receive your item, you'll get a delivery code to release the payment to the seller.
              </p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://hivecampus.app/order/${orderId}" 
                 style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Track Your Order
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              If you have any questions, feel free to reach out to our support team.
            </p>
          </div>
          
          <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
            <p>© 2024 Hive Campus. All rights reserved.</p>
          </div>
        </div>
      `
    };

    await transporter.sendMail(buyerEmailOptions);

    // Send notification to seller if email provided
    if (sellerEmail) {
      const sellerEmailOptions = {
        from: environmentConfig.email.from,
        to: sellerEmail,
        subject: `New Sale - Order #${orderId}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); padding: 30px; text-align: center;">
              <h1 style="color: white; margin: 0;">You Made a Sale! 💰</h1>
            </div>
            
            <div style="padding: 30px; background: #f8f9fa;">
              <h2 style="color: #333;">Congratulations!</h2>
              <p style="color: #666; line-height: 1.6;">
                Your item has been purchased on Hive Campus. The payment is securely held in escrow.
              </p>
              
              <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #333; margin-top: 0;">Sale Details</h3>
                <p><strong>Order ID:</strong> #${orderId}</p>
                <p><strong>Item:</strong> ${orderDetails.title}</p>
                <p><strong>Sale Amount:</strong> $${orderDetails.amount}</p>
                <p><strong>Your Earnings:</strong> $${orderDetails.sellerAmount}</p>
              </div>
              
              <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #f57c00; margin-top: 0;">Next Steps</h4>
                <p style="color: #333; margin-bottom: 0;">
                  Please prepare your item for delivery. Once the buyer confirms receipt, 
                  the payment will be transferred to your account.
                </p>
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="https://hivecampus.app/order/${orderId}" 
                   style="background: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  View Order Details
                </a>
              </div>
            </div>
            
            <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
              <p>© 2024 Hive Campus. All rights reserved.</p>
            </div>
          </div>
        `
      };

      await transporter.sendMail(sellerEmailOptions);
    }

    return { success: true, message: 'Payment confirmation emails sent successfully' };
  } catch (error) {
    console.error('Error sending payment confirmation email:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send email');
  }
});

// Send payment failure email
export const sendPaymentFailureEmail = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { buyerEmail, orderDetails, errorMessage } = data;

    if (!buyerEmail || !orderDetails) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    const emailOptions = {
      from: environmentConfig.email.from,
      to: buyerEmail,
      subject: 'Payment Failed - Hive Campus',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0;">Payment Failed</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <h2 style="color: #333;">Payment Unsuccessful</h2>
            <p style="color: #666; line-height: 1.6;">
              We're sorry, but your payment for the following item could not be processed.
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">Order Details</h3>
              <p><strong>Item:</strong> ${orderDetails.title}</p>
              <p><strong>Amount:</strong> $${orderDetails.amount}</p>
              <p><strong>Seller:</strong> ${orderDetails.sellerName}</p>
            </div>
            
            ${errorMessage ? `
              <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #c62828; margin-top: 0;">Error Details</h4>
                <p style="color: #333; margin-bottom: 0;">${errorMessage}</p>
              </div>
            ` : ''}
            
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h4 style="color: #2e7d32; margin-top: 0;">What You Can Do</h4>
              <ul style="color: #333; margin-bottom: 0;">
                <li>Check your payment method details</li>
                <li>Ensure sufficient funds are available</li>
                <li>Try a different payment method</li>
                <li>Contact your bank if the issue persists</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://hivecampus.app/listing/${orderDetails.listingId}" 
                 style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Try Again
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              If you continue to experience issues, please contact our support team for assistance.
            </p>
          </div>
          
          <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
            <p>© 2024 Hive Campus. All rights reserved.</p>
          </div>
        </div>
      `
    };

    await transporter.sendMail(emailOptions);

    return { success: true, message: 'Payment failure email sent successfully' };
  } catch (error) {
    console.error('Error sending payment failure email:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send email');
  }
});

// Send Stripe onboarding reminder email
export const sendOnboardingReminderEmail = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { email, name, pendingAmount, orderCount, onboardingUrl } = data;

    if (!email || !onboardingUrl) {
      throw new functions.https.HttpsError('invalid-argument', 'Email and onboarding URL are required');
    }

    const emailOptions = {
      from: environmentConfig.email.from,
      to: email,
      subject: '💰 You have money waiting for you on Hive Campus!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0;">💰 Money Waiting for You!</h1>
          </div>

          <div style="padding: 30px; background: #f8f9fa;">
            <h2 style="color: #333;">Hi ${name || 'there'}!</h2>
            <p style="color: #666; line-height: 1.6;">
              Great news! You have <strong>$${pendingAmount || '0.00'}</strong> waiting for you from
              ${orderCount || 1} recent ${orderCount === 1 ? 'sale' : 'sales'} on Hive Campus.
            </p>

            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;">
              <h3 style="color: #333; margin-top: 0;">🚀 Set up payouts in less than 2 minutes</h3>
              <p style="color: #666; margin-bottom: 0;">
                Complete your Stripe setup to start receiving payments directly to your bank account.
                It's quick, secure, and required by financial regulations.
              </p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${onboardingUrl}"
                 style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;">
                Set Up Payouts Now
              </a>
            </div>

            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h4 style="color: #1976d2; margin-top: 0;">Why do I need to do this?</h4>
              <ul style="color: #333; margin-bottom: 0;">
                <li>Secure and instant payments to your bank account</li>
                <li>Required for tax compliance and fraud prevention</li>
                <li>One-time setup, then automatic payments forever</li>
                <li>Powered by Stripe - trusted by millions of businesses</li>
              </ul>
            </div>

            <p style="color: #666; font-size: 14px; margin-top: 30px;">
              Questions? Reply to this email or contact our support team. We're here to help!
            </p>
          </div>

          <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
            <p>© 2024 Hive Campus. All rights reserved.</p>
            <p style="margin: 5px 0 0 0;">
              <a href="https://hivecampus.app/unsubscribe" style="color: #ccc;">Unsubscribe</a>
            </p>
          </div>
        </div>
      `
    };

    await transporter.sendMail(emailOptions);

    return { success: true, message: 'Onboarding reminder email sent successfully' };
  } catch (error) {
    console.error('Error sending onboarding reminder email:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send onboarding reminder email');
  }
});

// Send batch onboarding reminder emails (internal function)
export const sendBatchOnboardingReminders = async (reminderData: OnboardingReminderData[]) => {
  try {
    const emailPromises = reminderData.map(async (data) => {
      const emailOptions = {
        from: environmentConfig.email.from,
        to: data.email,
        subject: '💰 You have money waiting for you on Hive Campus!',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
              <h1 style="color: white; margin: 0;">💰 Money Waiting for You!</h1>
            </div>

            <div style="padding: 30px; background: #f8f9fa;">
              <h2 style="color: #333;">Hi ${data.name || 'there'}!</h2>
              <p style="color: #666; line-height: 1.6;">
                Great news! You have <strong>$${data.pendingAmount || '0.00'}</strong> waiting for you from
                ${data.orderCount || 1} recent ${data.orderCount === 1 ? 'sale' : 'sales'} on Hive Campus.
              </p>

              <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;">
                <h3 style="color: #333; margin-top: 0;">🚀 Set up payouts in less than 2 minutes</h3>
                <p style="color: #666; margin-bottom: 0;">
                  Complete your Stripe setup to start receiving payments directly to your bank account.
                  It's quick, secure, and required by financial regulations.
                </p>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.onboardingUrl}"
                   style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;">
                  Set Up Payouts Now
                </a>
              </div>

              <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #1976d2; margin-top: 0;">Why do I need to do this?</h4>
                <ul style="color: #333; margin-bottom: 0;">
                  <li>Secure and instant payments to your bank account</li>
                  <li>Required for tax compliance and fraud prevention</li>
                  <li>One-time setup, then automatic payments forever</li>
                  <li>Powered by Stripe - trusted by millions of businesses</li>
                </ul>
              </div>

              <p style="color: #666; font-size: 14px; margin-top: 30px;">
                Questions? Reply to this email or contact our support team. We're here to help!
              </p>
            </div>

            <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
              <p>© 2024 Hive Campus. All rights reserved.</p>
              <p style="margin: 5px 0 0 0;">
                <a href="https://hivecampus.app/unsubscribe" style="color: #ccc;">Unsubscribe</a>
              </p>
            </div>
          </div>
        `
      };

      return transporter.sendMail(emailOptions);
    });

    await Promise.all(emailPromises);
    console.log(`Sent ${reminderData.length} onboarding reminder emails`);
  } catch (error) {
    console.error('Error sending batch onboarding reminders:', error);
    throw error;
  }
};

// Resend verification email
export const resendVerificationEmail = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { email } = data;

    if (!email) {
      throw new functions.https.HttpsError('invalid-argument', 'Email is required');
    }

    // Generate verification link (you'll need to implement this based on your auth system)
    const actionCodeSettings = {
      url: 'https://hivecampus.app/verify-email',
      handleCodeInApp: true,
    };

    // Send verification email using Firebase Auth
    await admin.auth().generateEmailVerificationLink(email, actionCodeSettings);

    return { success: true, message: 'Verification email sent successfully' };
  } catch (error) {
    console.error('Error resending verification email:', error);
    throw new functions.https.HttpsError('internal', 'Failed to resend verification email');
  }
});
