# Firebase Setup Instructions

## Step 1: Enable Firebase Authentication

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `h1c1-798a8`
3. Navigate to **Authentication** > **Sign-in method**
4. Enable **Email/Password** authentication
5. Enable **Microsoft** authentication (see Microsoft Setup section below)

## Step 2: Create Test Accounts

Go to **Authentication** > **Users** and manually add these test accounts:

### Student Account
- **Email:** <EMAIL>
- **Password:** testpassword123
- Click "Add user"

### Merchant Account
- **Email:** <EMAIL>
- **Password:** testpassword123
- Click "Add user"

### Admin Account
- **Email:** <EMAIL>
- **Password:** testpassword123
- Click "Add user"

## Step 3: Set up Firestore Database

1. Navigate to **Firestore Database**
2. Click "Create database"
3. Choose "Start in test mode" for now (we'll update security rules later)
4. Select a location (us-central1 is recommended)

## Step 4: Create User Profiles in Firestore

For each test account created above, create a corresponding document in Firestore:

### Collection: `users`

#### Document ID: [Use the UID from Authentication]

**Student Profile:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "student",
  "profilePictureURL": "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
  "university": "Stanford University",
  "major": "Computer Science",
  "graduationYear": 2025,
  "verified": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

**Merchant Profile:**
```json
{
  "name": "Campus Store",
  "email": "<EMAIL>",
  "role": "merchant",
  "profilePictureURL": "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  "businessName": "Campus Store",
  "businessType": "retail",
  "businessAddress": "123 Campus Way, Stanford, CA 94305",
  "verified": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

**Admin Profile:**
```json
{
  "name": "Admin User",
  "email": "<EMAIL>",
  "role": "admin",
  "profilePictureURL": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
  "adminLevel": "super",
  "permissions": ["user_management", "content_moderation", "analytics", "system_settings"],
  "verified": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

## Step 5: Enable Firebase Analytics (Optional)

1. Navigate to **Analytics**
2. Click "Enable Google Analytics"
3. Follow the setup wizard

## Step 6: Test the Application

After completing the above steps:

1. Start your development server: `npm run dev`
2. Navigate to the login page
3. Click "Show Test Accounts" button
4. Select a test account to auto-fill the login form
5. Click "Sign In" to authenticate

## Security Rules (For Production)

Update Firestore security rules in **Firestore Database** > **Rules**:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read other users' public profiles
    match /users/{userId} {
      allow read: if request.auth != null;
    }
    
    // Additional rules for listings, orders, etc.
    // Add more specific rules based on your app's requirements
  }
}
```

## Microsoft Authentication Setup

### Step 1: Create Microsoft App Registration

1. Go to [Azure Portal](https://portal.azure.com/)
2. Sign in with your Microsoft account
3. Search for "App registrations" and click "New registration"
4. Configure the registration:
   - **Name:** `Hive Campus`
   - **Supported account types:** "Accounts in any organizational directory and personal Microsoft accounts"
   - **Redirect URI:** 
     - Platform: `Web`
     - URL: `https://h1c1-798a8.firebaseapp.com/__/auth/handler`
5. Click "Register"

### Step 2: Get Application ID and Secret

1. **Copy the Application (client) ID** from the overview page
2. Go to **"Certificates & secrets"** → **"New client secret"**
3. Add description: `Firebase Authentication Secret`
4. Set expiration: `24 months`
5. Click "Add" and **copy the secret value immediately**

### Step 3: Complete Firebase Configuration

1. In Firebase Console: **Authentication** → **Sign-in method** → **Microsoft**
2. Enter the **Application ID** from Step 2
3. Enter the **Application Secret** from Step 2
4. Click **"Save"**

### Step 4: Configure Additional Redirect URI (if needed)

1. In Azure Portal, go to your app's **"Authentication"** section
2. Click **"Add a platform"** → **"Web"**
3. Add redirect URI: `https://h1c1-798a8.firebaseapp.com/__/auth/handler`
4. Click **"Configure"**

## Next Steps

1. Complete the Firebase setup steps above
2. Set up Microsoft authentication (if desired)
3. Test all three user roles (student, merchant, admin)
4. Verify that role-based navigation works correctly
5. Test all major features with each account type