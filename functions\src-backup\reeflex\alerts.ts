import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { sendCriticalError<PERSON>lert, sendPerformanceAlert, sendPaymentFailureAlert, sendFeedbackAlert } from '../utils/slack';
import { sendCriticalErrorEmail, sendPerformanceEmail, sendPaymentFailureEmail, sendFeedbackEmail } from '../utils/email';

// Collection references
const REEFLEX_ACTIVITY_COLLECTION = 'reeflex_activity';
const REEFLEX_FEEDBACK_COLLECTION = 'reeflex_feedback';
const REEFLEX_ALERTS_COLLECTION = 'reeflex_alerts';

// Alert thresholds
const ERROR_THRESHOLD = 5; // 5+ errors of the same type on the same route
const PERFORMANCE_THRESHOLD = 3; // 3+ slow page loads on the same route
const PAYMENT_FAILURE_THRESHOLD = 3; // 3+ payment failures
const FEEDBACK_THRESHOLD = 3; // 3+ negative feedback on the same route/category

// Alert cooldown period (in hours)
const ALERT_COOLDOWN = 4; // Don't send the same alert more than once every 4 hours

/**
 * Function to check for critical errors and send alerts
 * Runs every 15 minutes
 */
export const checkForCriticalErrors = functions.pubsub
  .schedule('every 15 minutes')
  .timeZone('America/New_York')
  .onRun(async () => {
    const db = admin.firestore();
    
    try {
      // Calculate time range for the check (last hour)
      const endTime = admin.firestore.Timestamp.now();
      const startTime = new admin.firestore.Timestamp(
        endTime.seconds - 60 * 60, // 1 hour
        endTime.seconds
      );
      
      // Fetch error events from the last hour
      const errorSnapshot = await db
        .collection(REEFLEX_ACTIVITY_COLLECTION)
        .where('eventType', 'in', ['javascript_error', 'promise_rejection', 'api_error', 'api_failed'])
        .where('timestamp', '>=', startTime)
        .where('timestamp', '<=', endTime)
        .get();
      
      if (errorSnapshot.empty) {
        functions.logger.info('No errors found in the last hour');
        return null;
      }
      
      // Group errors by type and route
      const errorGroups: Record<string, FirebaseFirestore.DocumentData[]> = {};
      
      errorSnapshot.forEach(doc => {
        const error = doc.data();
        const key = `${error.eventType}:${error.route}`;
        
        if (!errorGroups[key]) {
          errorGroups[key] = [];
        }
        
        errorGroups[key].push(error);
      });
      
      // Check each group against the threshold
      for (const [key, errors] of Object.entries(errorGroups)) {
        if (errors.length >= ERROR_THRESHOLD) {
          const [errorType, route] = key.split(':');
          
          // Check if we've already sent an alert for this recently
          const alertKey = `error:${key}`;
          const shouldSendAlert = await checkAlertCooldown(alertKey);
          
          if (shouldSendAlert) {
            // Get a sample error for details
            const sampleError = errors[0];
            const sentryId = sampleError.sentryEventId;
            const errorMessage = sampleError.data?.message || sampleError.data?.error || 'Unknown error';
            
            // Send alerts
            await Promise.all([
              sendCriticalErrorAlert(errorType, errors.length, route, sentryId, errorMessage),
              sendCriticalErrorEmail(errorType, errors.length, route, sentryId, errorMessage)
            ]);
            
            // Record that we sent this alert
            await recordAlert(alertKey);
            
            functions.logger.info(`Sent critical error alert for ${errorType} on ${route}`);
          }
        }
      }
      
      return null;
    } catch (error) {
      functions.logger.error('Error checking for critical errors:', error);
      throw error;
    }
  });

/**
 * Function to check for performance issues and send alerts
 * Runs every 30 minutes
 */
export const checkForPerformanceIssues = functions.pubsub
  .schedule('every 30 minutes')
  .timeZone('America/New_York')
  .onRun(async () => {
    const db = admin.firestore();
    
    try {
      // Calculate time range for the check (last hour)
      const endTime = admin.firestore.Timestamp.now();
      const startTime = new admin.firestore.Timestamp(
        endTime.seconds - 60 * 60, // 1 hour
        endTime.seconds
      );
      
      // Fetch performance events from the last hour
      const performanceSnapshot = await db
        .collection(REEFLEX_ACTIVITY_COLLECTION)
        .where('eventType', 'in', ['slow_navigation', 'slow_resource', 'long_task', 'api_slow'])
        .where('timestamp', '>=', startTime)
        .where('timestamp', '<=', endTime)
        .get();
      
      if (performanceSnapshot.empty) {
        functions.logger.info('No performance issues found in the last hour');
        return null;
      }
      
      // Group performance issues by route
      const performanceGroups: Record<string, FirebaseFirestore.DocumentData[]> = {};
      
      performanceSnapshot.forEach(doc => {
        const issue = doc.data();
        const route = issue.route;
        
        if (!performanceGroups[route]) {
          performanceGroups[route] = [];
        }
        
        performanceGroups[route].push(issue);
      });
      
      // Check each group against the threshold
      for (const [route, issues] of Object.entries(performanceGroups)) {
        if (issues.length >= PERFORMANCE_THRESHOLD) {
          // Check if we've already sent an alert for this recently
          const alertKey = `performance:${route}`;
          const shouldSendAlert = await checkAlertCooldown(alertKey);
          
          if (shouldSendAlert) {
            // Calculate average duration
            const totalDuration = issues.reduce((sum, issue) => {
              return sum + (issue.data?.duration_ms || 0);
            }, 0);
            const avgDuration = Math.round(totalDuration / issues.length);
            
            // Send alerts
            await Promise.all([
              sendPerformanceAlert(route, avgDuration, issues.length),
              sendPerformanceEmail(route, avgDuration, issues.length)
            ]);
            
            // Record that we sent this alert
            await recordAlert(alertKey);
            
            functions.logger.info(`Sent performance alert for ${route}`);
          }
        }
      }
      
      return null;
    } catch (error) {
      functions.logger.error('Error checking for performance issues:', error);
      throw error;
    }
  });

/**
 * Function to check for payment failures and send alerts
 * Runs every 15 minutes
 */
export const checkForPaymentFailures = functions.pubsub
  .schedule('every 15 minutes')
  .timeZone('America/New_York')
  .onRun(async () => {
    const db = admin.firestore();
    
    try {
      // Calculate time range for the check (last hour)
      const endTime = admin.firestore.Timestamp.now();
      const startTime = new admin.firestore.Timestamp(
        endTime.seconds - 60 * 60, // 1 hour
        endTime.seconds
      );
      
      // Fetch payment failure events from the last hour
      const paymentSnapshot = await db
        .collection(REEFLEX_ACTIVITY_COLLECTION)
        .where('eventType', 'in', ['payment_timeout', 'checkout_failed'])
        .where('timestamp', '>=', startTime)
        .where('timestamp', '<=', endTime)
        .get();
      
      if (paymentSnapshot.empty) {
        functions.logger.info('No payment failures found in the last hour');
        return null;
      }
      
      const paymentFailures = paymentSnapshot.docs.map(doc => doc.data());
      
      if (paymentFailures.length >= PAYMENT_FAILURE_THRESHOLD) {
        // Check if we've already sent an alert for this recently
        const alertKey = 'payment_failures';
        const shouldSendAlert = await checkAlertCooldown(alertKey);
        
        if (shouldSendAlert) {
          // Get details from the failures
          const details = paymentFailures
            .slice(0, 3)
            .map(failure => failure.data?.error || 'Unknown error')
            .join(', ');
          
          // Send alerts
          await Promise.all([
            sendPaymentFailureAlert(paymentFailures.length, details),
            sendPaymentFailureEmail(paymentFailures.length, details)
          ]);
          
          // Record that we sent this alert
          await recordAlert(alertKey);
          
          functions.logger.info(`Sent payment failure alert for ${paymentFailures.length} failures`);
        }
      }
      
      return null;
    } catch (error) {
      functions.logger.error('Error checking for payment failures:', error);
      throw error;
    }
  });

/**
 * Function to check for feedback patterns and send alerts
 * Runs every hour
 */
export const checkForFeedbackPatterns = functions.pubsub
  .schedule('every 60 minutes')
  .timeZone('America/New_York')
  .onRun(async () => {
    const db = admin.firestore();
    
    try {
      // Calculate time range for the check (last 4 hours)
      const endTime = admin.firestore.Timestamp.now();
      const startTime = new admin.firestore.Timestamp(
        endTime.seconds - 4 * 60 * 60, // 4 hours
        endTime.seconds
      );
      
      // Fetch negative feedback from the last 4 hours
      const feedbackSnapshot = await db
        .collection(REEFLEX_FEEDBACK_COLLECTION)
        .where('feedbackType', '==', 'negative')
        .where('timestamp', '>=', startTime)
        .where('timestamp', '<=', endTime)
        .get();
      
      if (feedbackSnapshot.empty) {
        functions.logger.info('No negative feedback found in the last 4 hours');
        return null;
      }
      
      // Group feedback by route and category
      const feedbackGroups: Record<string, FirebaseFirestore.DocumentData[]> = {};
      
      feedbackSnapshot.forEach(doc => {
        const feedback = doc.data();
        const route = feedback.route || 'unknown';
        const category = feedback.category || 'unknown';
        const key = `${route}:${category}`;
        
        if (!feedbackGroups[key]) {
          feedbackGroups[key] = [];
        }
        
        feedbackGroups[key].push(feedback);
      });
      
      // Check each group against the threshold
      for (const [key, feedbacks] of Object.entries(feedbackGroups)) {
        if (feedbacks.length >= FEEDBACK_THRESHOLD) {
          const [route, category] = key.split(':');
          
          // Check if we've already sent an alert for this recently
          const alertKey = `feedback:${key}`;
          const shouldSendAlert = await checkAlertCooldown(alertKey);
          
          if (shouldSendAlert) {
            // Get sample quotes
            const quotes = feedbacks
              .filter(f => f.message && f.message.trim().length > 0)
              .slice(0, 3)
              .map(f => f.message);
            
            // Send alerts
            await Promise.all([
              sendFeedbackAlert(route, category, feedbacks.length, quotes),
              sendFeedbackEmail(route, category, feedbacks.length, quotes)
            ]);
            
            // Record that we sent this alert
            await recordAlert(alertKey);
            
            functions.logger.info(`Sent feedback alert for ${category} on ${route}`);
          }
        }
      }
      
      return null;
    } catch (error) {
      functions.logger.error('Error checking for feedback patterns:', error);
      throw error;
    }
  });

/**
 * Check if an alert is within the cooldown period
 * 
 * @param alertKey The unique key for the alert
 * @returns True if the alert should be sent, false if it's within the cooldown period
 */
async function checkAlertCooldown(alertKey: string): Promise<boolean> {
  const db = admin.firestore();
  
  try {
    // Check if we've sent this alert recently
    const alertRef = db.collection(REEFLEX_ALERTS_COLLECTION).doc(alertKey);
    const alertDoc = await alertRef.get();
    
    if (alertDoc.exists) {
      const alertData = alertDoc.data();
      if (alertData) {
        const lastSent = alertData.timestamp.toDate();
        const now = new Date();
        const hoursSinceLastAlert = (now.getTime() - lastSent.getTime()) / (1000 * 60 * 60);
        
        // If we've sent this alert within the cooldown period, don't send it again
        if (hoursSinceLastAlert < ALERT_COOLDOWN) {
          functions.logger.info(`Alert ${alertKey} is within cooldown period (${hoursSinceLastAlert.toFixed(1)} hours since last alert)`);
          return false;
        }
      }
    }
    
    return true;
  } catch (error) {
    functions.logger.error(`Error checking alert cooldown for ${alertKey}:`, error);
    // If there's an error checking the cooldown, we'll send the alert anyway
    return true;
  }
}

/**
 * Record that an alert was sent
 * 
 * @param alertKey The unique key for the alert
 */
async function recordAlert(alertKey: string): Promise<void> {
  const db = admin.firestore();
  
  try {
    await db.collection(REEFLEX_ALERTS_COLLECTION).doc(alertKey).set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      alertType: alertKey.split(':')[0]
    });
  } catch (error) {
    functions.logger.error(`Error recording alert for ${alertKey}:`, error);
    // Non-critical error, so we won't throw
  }
}