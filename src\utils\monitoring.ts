import * as Sentry from '@sentry/react';

// Monitoring configuration
export interface MonitoringConfig {
  enableErrorTracking: boolean;
  enablePerformanceTracking: boolean;
  enableUserTracking: boolean;
  enableAPIMonitoring: boolean;
  enableCustomMetrics: boolean;
  sampleRate: number;
  environment: string;
}

// Default monitoring configuration
const defaultConfig: MonitoringConfig = {
  enableErrorTracking: true,
  enablePerformanceTracking: true,
  enableUserTracking: true,
  enableAPIMonitoring: true,
  enableCustomMetrics: true,
  sampleRate: 1.0,
  environment: import.meta.env.MODE || 'development',
};

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Custom metrics types
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  TIMER = 'timer',
}

// Alert thresholds
export interface AlertThreshold {
  metric: string;
  condition: 'above' | 'below' | 'equals';
  value: number;
  severity: ErrorSeverity;
  description: string;
}

// Monitoring service class
export class MonitoringService {
  private static instance: MonitoringService;
  private config: MonitoringConfig;
  private metrics: Map<string, unknown> = new Map();
  private alerts: AlertThreshold[] = [];

  private constructor(config: MonitoringConfig = defaultConfig) {
    this.config = config;
    this.setupDefaultAlerts();
  }

  static getInstance(config?: MonitoringConfig): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService(config);
    }
    return MonitoringService.instance;
  }

  private setupDefaultAlerts() {
    this.alerts = [
      {
        metric: 'error_rate',
        condition: 'above',
        value: 5, // 5% error rate
        severity: ErrorSeverity.HIGH,
        description: 'High error rate detected',
      },
      {
        metric: 'response_time',
        condition: 'above',
        value: 2000, // 2 seconds
        severity: ErrorSeverity.MEDIUM,
        description: 'High response time detected',
      },
      {
        metric: 'memory_usage',
        condition: 'above',
        value: 80, // 80% memory usage
        severity: ErrorSeverity.HIGH,
        description: 'High memory usage detected',
      },
      {
        metric: 'lcp',
        condition: 'above',
        value: 2.5, // 2.5 seconds LCP
        severity: ErrorSeverity.MEDIUM,
        description: 'Poor Largest Contentful Paint performance',
      },
      {
        metric: 'cls',
        condition: 'above',
        value: 0.1, // CLS score above 0.1
        severity: ErrorSeverity.MEDIUM,
        description: 'Poor Cumulative Layout Shift score',
      },
      {
        metric: 'fid',
        condition: 'above',
        value: 100, // 100ms First Input Delay
        severity: ErrorSeverity.MEDIUM,
        description: 'Poor First Input Delay performance',
      },
    ];
  }

  // Error tracking
  trackError(error: Error, context?: Record<string, unknown>, severity: ErrorSeverity = ErrorSeverity.MEDIUM) {
    if (!this.config.enableErrorTracking) return;

    const errorInfo = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      severity,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // Send to Sentry
    Sentry.withScope((scope) => {
      scope.setLevel(this.mapSeverityToSentryLevel(severity));
      if (context) {
        Object.entries(context).forEach(([key, value]) => {
          scope.setContext(key, value);
        });
      }
      Sentry.captureException(error);
    });

    // Log to console in development
    if (this.config.environment === 'development') {
      console.error('Tracked Error:', errorInfo);
    }

    // Check for alerts
    this.checkErrorRateAlert();
  }

  // Performance tracking
  trackPerformance(metric: string, value: number, tags?: Record<string, string>) {
    if (!this.config.enablePerformanceTracking) return;

    const performanceData = {
      metric,
      value,
      tags,
      timestamp: new Date().toISOString(),
    };

    // Store metric
    this.metrics.set(metric, value);

    // Send to Sentry
    Sentry.setMeasurement(metric, value, tags?.unit || 'millisecond');

    // Add breadcrumb
    Sentry.addBreadcrumb({
      category: 'performance',
      message: `${metric}: ${value}${tags?.unit || 'ms'}`,
      level: 'info',
      data: tags,
    });

    // Check for performance alerts
    this.checkPerformanceAlerts(metric, value);

    if (this.config.environment === 'development') {
      console.log('Performance Metric:', performanceData);
    }
  }

  // User behavior tracking
  trackUserAction(action: string, properties?: Record<string, unknown>) {
    if (!this.config.enableUserTracking) return;

    const actionData = {
      action,
      properties,
      timestamp: new Date().toISOString(),
      url: window.location.href,
    };

    // Send to Sentry as breadcrumb
    Sentry.addBreadcrumb({
      category: 'user',
      message: action,
      level: 'info',
      data: properties,
    });

    if (this.config.environment === 'development') {
      console.log('User Action:', actionData);
    }
  }

  // API monitoring
  trackAPICall(endpoint: string, method: string, status: number, duration: number, error?: Error) {
    if (!this.config.enableAPIMonitoring) return;

    const apiData = {
      endpoint,
      method,
      status,
      duration,
      error: error?.message,
      timestamp: new Date().toISOString(),
    };

    // Track performance
    this.trackPerformance(`api_response_time_${method}_${endpoint}`, duration, {
      endpoint,
      method,
      status: status.toString(),
    });

    // Track errors
    if (error || status >= 400) {
      this.trackError(
        error || new Error(`API Error: ${status} ${method} ${endpoint}`),
        apiData,
        status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM
      );
    }

    if (this.config.environment === 'development') {
      console.log('API Call:', apiData);
    }
  }

  // Custom metrics
  recordMetric(name: string, value: number, type: MetricType = MetricType.GAUGE, tags?: Record<string, string>) {
    if (!this.config.enableCustomMetrics) return;

    const metricData = {
      name,
      value,
      type,
      tags,
      timestamp: new Date().toISOString(),
    };

    // Store metric
    this.metrics.set(name, value);

    // Send to Sentry
    Sentry.setMeasurement(name, value, tags?.unit || 'number');

    if (this.config.environment === 'development') {
      console.log('Custom Metric:', metricData);
    }
  }

  // Alert checking
  private checkErrorRateAlert() {
    // Calculate error rate from recent errors
    // This is a simplified implementation
    const recentErrors = 5; // Mock data
    const totalRequests = 100; // Mock data
    const errorRate = (recentErrors / totalRequests) * 100;

    const alert = this.alerts.find(a => a.metric === 'error_rate');
    if (alert && errorRate > alert.value) {
      this.triggerAlert(alert, errorRate);
    }
  }

  private checkPerformanceAlerts(metric: string, value: number) {
    const alert = this.alerts.find(a => a.metric === metric);
    if (!alert) return;

    const shouldAlert = this.evaluateCondition(value, alert.condition, alert.value);
    if (shouldAlert) {
      this.triggerAlert(alert, value);
    }
  }

  private evaluateCondition(value: number, condition: 'above' | 'below' | 'equals', threshold: number): boolean {
    switch (condition) {
      case 'above':
        return value > threshold;
      case 'below':
        return value < threshold;
      case 'equals':
        return value === threshold;
      default:
        return false;
    }
  }

  private triggerAlert(alert: AlertThreshold, actualValue: number) {
    const alertData = {
      metric: alert.metric,
      severity: alert.severity,
      description: alert.description,
      threshold: alert.value,
      actualValue,
      timestamp: new Date().toISOString(),
    };

    // Send to Sentry as error
    Sentry.captureMessage(
      `Alert: ${alert.description} (${alert.metric}: ${actualValue} ${alert.condition} ${alert.value})`,
      this.mapSeverityToSentryLevel(alert.severity)
    );

    // In production, you might want to send to external alerting systems
    // like PagerDuty, Slack, email, etc.
    if (this.config.environment === 'production') {
      this.sendExternalAlert(alertData);
    }

    console.warn('Alert Triggered:', alertData);
  }

  private sendExternalAlert(alertData: unknown) {
    // Implementation for external alerting systems
    // This could include:
    // - Webhook to Slack
    // - Email notifications
    // - PagerDuty integration
    // - Discord notifications
    
    // Example: Send to webhook
    if (import.meta.env.VITE_ALERT_WEBHOOK_URL) {
      fetch(import.meta.env.VITE_ALERT_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: `🚨 Alert: ${alertData.description}`,
          attachments: [
            {
              color: this.getSeverityColor(alertData.severity),
              fields: [
                { title: 'Metric', value: alertData.metric, short: true },
                { title: 'Actual Value', value: alertData.actualValue.toString(), short: true },
                { title: 'Threshold', value: alertData.threshold.toString(), short: true },
                { title: 'Severity', value: alertData.severity, short: true },
              ],
              timestamp: alertData.timestamp,
            },
          ],
        }),
      }).catch(error => {
        console.error('Failed to send external alert:', error);
      });
    }
  }

  private getSeverityColor(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return '#ff0000';
      case ErrorSeverity.HIGH:
        return '#ff8000';
      case ErrorSeverity.MEDIUM:
        return '#ffff00';
      case ErrorSeverity.LOW:
        return '#00ff00';
      default:
        return '#808080';
    }
  }

  private mapSeverityToSentryLevel(severity: ErrorSeverity): Sentry.SeverityLevel {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 'fatal';
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warning';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'info';
    }
  }

  // Health check
  getHealthStatus() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      metrics: Object.fromEntries(this.metrics),
      config: this.config,
      alertsCount: this.alerts.length,
      environment: this.config.environment,
    };
  }

  // Configuration updates
  updateConfig(newConfig: Partial<MonitoringConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  // Add custom alert
  addAlert(alert: AlertThreshold) {
    this.alerts.push(alert);
  }

  // Remove alert
  removeAlert(metric: string) {
    this.alerts = this.alerts.filter(a => a.metric !== metric);
  }

  // Get all metrics
  getAllMetrics() {
    return Object.fromEntries(this.metrics);
  }

  // Clear metrics
  clearMetrics() {
    this.metrics.clear();
  }
}

// Convenience functions for easy usage
export const monitoring = MonitoringService.getInstance();

export const trackError = (error: Error, context?: Record<string, unknown>, severity?: ErrorSeverity) => {
  monitoring.trackError(error, context, severity);
};

export const trackPerformance = (metric: string, value: number, tags?: Record<string, string>) => {
  monitoring.trackPerformance(metric, value, tags);
};

export const trackUserAction = (action: string, properties?: Record<string, unknown>) => {
  monitoring.trackUserAction(action, properties);
};

export const trackAPICall = (endpoint: string, method: string, status: number, duration: number, error?: Error) => {
  monitoring.trackAPICall(endpoint, method, status, duration, error);
};

export const recordMetric = (name: string, value: number, type?: MetricType, tags?: Record<string, string>) => {
  monitoring.recordMetric(name, value, type, tags);
};

// Initialize monitoring
export const initializeMonitoring = (config?: Partial<MonitoringConfig>) => {
  if (config) {
    monitoring.updateConfig(config);
  }
  
  // Set up global error handler
  window.addEventListener('error', (event) => {
    monitoring.trackError(
      new Error(event.message),
      {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      },
      ErrorSeverity.HIGH
    );
  });

  // Set up unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    monitoring.trackError(
      new Error(event.reason),
      { type: 'unhandledPromiseRejection' },
      ErrorSeverity.HIGH
    );
  });

  console.log('Monitoring initialized:', monitoring.getHealthStatus());
};

export default MonitoringService;