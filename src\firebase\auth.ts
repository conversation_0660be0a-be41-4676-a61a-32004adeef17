import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  OAuthProvider,
  updateProfile,
  signOut,
  sendPasswordResetEmail
} from 'firebase/auth';
import { auth, db } from './config';
import { doc, getDoc, updateDoc } from 'firebase/firestore';

// Sign up with email and password
export const signUpWithEmail = async (email: string, password: string, name: string) => {
  try {
    // Validate .edu email
    if (!email.endsWith('.edu') &&
        !email.includes('@outlook.') &&
        !email.includes('@hotmail.') &&
        !email.includes('@live.') &&
        !email.includes('@student.')) {
      throw new Error('Only .edu email addresses are allowed to register');
    }

    // Create user
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);

    // Update display name
    await updateProfile(userCredential.user, {
      displayName: name
    });

    // Email verification removed - all accounts are verified by default via Firebase Functions

    return userCredential.user;
  } catch (error: any) {
    console.error('Error signing up:', error);

    // Handle specific Firebase Auth errors
    if (error.code === 'auth/too-many-requests') {
      // Clear any cached auth state and suggest solutions
      await auth.signOut().catch(() => {});
      throw new Error('Too many failed attempts. Please wait 15 minutes before trying again, or try: 1) Using a different network/device, 2) Clearing browser cache and cookies, 3) Using incognito/private browsing mode.');
    } else if (error.code === 'auth/email-already-in-use') {
      throw new Error('An account with this email already exists. Please sign in instead.');
    } else if (error.code === 'auth/weak-password') {
      throw new Error('Password is too weak. Please use at least 6 characters.');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Please enter a valid email address.');
    } else if (error.code === 'auth/network-request-failed') {
      throw new Error('Network error. Please check your internet connection and try again.');
    }

    throw error;
  }
};

// Sign in with email and password
export const signInWithEmail = async (email: string, password: string) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error: any) {
    console.error('Error signing in:', error);

    // Handle specific Firebase Auth errors
    if (error.code === 'auth/too-many-requests') {
      // Clear any cached auth state and suggest solutions
      await auth.signOut().catch(() => {});
      throw new Error('Too many failed attempts. Please wait 15 minutes before trying again, or try: 1) Using a different network/device, 2) Clearing browser cache and cookies, 3) Using incognito/private browsing mode.');
    } else if (error.code === 'auth/user-not-found') {
      throw new Error('No account found with this email. Please check your email or sign up.');
    } else if (error.code === 'auth/wrong-password') {
      throw new Error('Incorrect password. Please try again.');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Please enter a valid email address.');
    } else if (error.code === 'auth/user-disabled') {
      throw new Error('This account has been disabled. Please contact support.');
    } else if (error.code === 'auth/network-request-failed') {
      throw new Error('Network error. Please check your internet connection and try again.');
    }

    throw error;
  }
};

// Sign in with Microsoft (for .edu accounts)
export const signInWithMicrosoft = async () => {
  try {
    const provider = new OAuthProvider('microsoft.com');
    provider.setCustomParameters({
      prompt: 'select_account'
    });
    
    const userCredential = await signInWithPopup(auth, provider);
    return userCredential.user;
  } catch (error: unknown) {
    console.error('Error signing in with Microsoft:', error);
    throw error;
  }
};

// Sign out
export const logOut = async () => {
  try {
    await signOut(auth);
  } catch (error: unknown) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Reset password
export const resetPassword = async (email: string) => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error: unknown) {
    console.error('Error resetting password:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (data: {
  name?: string,
  university?: string,
  profilePictureURL?: string,
  bio?: string,
  graduationYear?: number,
  major?: string
}) => {
  try {
    if (!auth.currentUser) {
      throw new Error('No authenticated user');
    }

    // Update Firestore document
    const userDoc = doc(db, 'users', auth.currentUser.uid);
    await updateDoc(userDoc, {
      ...data,
      updatedAt: new Date()
    });

    // Update display name in Auth if provided
    if (data.name && auth.currentUser) {
      await updateProfile(auth.currentUser, {
        displayName: data.name
      });
    }

    return {
      success: true,
      message: 'Profile updated successfully'
    };
  } catch (error: unknown) {
    console.error('Error updating profile:', error);
    throw error;
  }
};

// Get current user profile
export const getUserProfile = async () => {
  try {
    if (!auth.currentUser) {
      throw new Error('No authenticated user');
    }

    const userDoc = doc(db, 'users', auth.currentUser.uid);
    const docSnap = await getDoc(userDoc);
    
    if (docSnap.exists()) {
      return {
        success: true,
        data: { uid: auth.currentUser.uid, ...docSnap.data() }
      };
    } else {
      // Don't automatically create profiles - let the signup process handle this
      // This prevents admin users from being auto-assigned 'student' role
      return {
        success: false,
        error: 'User profile not found'
      };
    }
  } catch (error: unknown) {
    console.error('Error getting profile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Get user by ID (for public profiles)
export const getUserById = async (userId: string) => {
  try {
    const userDoc = doc(db, 'users', userId);
    const docSnap = await getDoc(userDoc);
    
    if (docSnap.exists()) {
      return {
        success: true,
        data: { uid: userId, ...docSnap.data() }
      };
    } else {
      return {
        success: false,
        error: 'User not found'
      };
    }
  } catch (error: unknown) {
    console.error('Error getting user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};