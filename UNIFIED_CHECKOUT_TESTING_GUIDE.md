# Unified Checkout and Escrow Flow Testing Guide

## Overview
This guide covers comprehensive testing for the newly implemented unified checkout and escrow flow for Hive Campus, supporting Buy Now, Rent Now, and Place a Bid actions.

## Pre-Testing Setup

### 1. Environment Configuration
- Ensure Firebase config is properly set up
- Verify Stripe test API keys are configured
- Confirm Shippo test API keys are available
- Check email service configuration

### 2. Test Data Preparation
- Create test user accounts (buyer and seller)
- Create test listings for each type:
  - Regular items (10% platform fee)
  - Textbooks/course materials (8% platform fee)
  - Rental items with weekly/monthly pricing
  - Auction items with starting bids

## Testing Scenarios

### A. Address Management Testing

#### Test Case A1: Add New Address
1. Navigate to checkout or profile settings
2. Click "Add New Address"
3. Fill in all required fields
4. Verify validation for:
   - Empty required fields
   - Invalid ZIP code format
   - Phone number format
5. Save address and verify it appears in list
6. Test setting as default address

#### Test Case A2: Address Limits
1. Add first address - should succeed
2. Add second address - should succeed
3. Attempt to add third address - should show error message
4. Delete one address, then add new one - should succeed

#### Test Case A3: Address Validation
1. Test with invalid ZIP codes (letters, wrong length)
2. Test with empty required fields
3. Test with very long address lines
4. Verify state dropdown works correctly

### B. Unified Checkout Flow Testing

#### Test Case B1: Buy Now Flow
1. Navigate to a regular listing
2. Click "Buy Now"
3. Verify 4-step checkout process:
   - Step 1: Review Item (correct price, description)
   - Step 2: Shipping Address (select/add address)
   - Step 3: Order Confirmation (correct totals with 10% fee)
   - Step 4: Stripe Checkout
4. Complete payment with test card
5. Verify redirect to success page
6. Check order creation in Firestore

#### Test Case B2: Rent Now Flow
1. Navigate to a rental listing
2. Test both "Rent Weekly" and "Rent Monthly" buttons
3. Verify correct pricing for each period
4. Complete checkout flow
5. Verify order type is set to 'rent' with correct period

#### Test Case B3: Place a Bid Flow
1. Navigate to an auction listing
2. Click "Place a Bid"
3. Verify bid amount is higher than current bid
4. Complete checkout flow
5. Verify order type is set to 'bid' with bid amount

#### Test Case B4: Textbook Platform Fee
1. Create a textbook listing
2. Complete purchase
3. Verify 8% platform fee is applied instead of 10%
4. Check commission calculation in order data

### C. Payment and Escrow Testing

#### Test Case C1: Successful Payment
1. Complete checkout with valid test card
2. Verify payment intent succeeds
3. Check order status updates to 'payment_succeeded'
4. Verify escrow release date is set (3 days from payment)
5. Confirm buyer receives confirmation email

#### Test Case C2: Failed Payment
1. Use declined test card (****************)
2. Verify error handling in Stripe Checkout
3. Ensure order is not created for failed payments
4. Test user can retry with different payment method

#### Test Case C3: Stripe Tax Integration
1. Complete checkout with different shipping addresses
2. Verify tax calculation appears in Stripe Checkout
3. Check that tax is properly collected
4. Confirm tax reporting in Stripe dashboard

### D. Shipping and Tracking Testing

#### Test Case D1: Automatic Shipping Label Generation
1. Complete a purchase
2. Verify shipping label is automatically generated
3. Check order status updates to 'shipped_pending_code'
4. Verify tracking information is stored
5. Test both Shippo integration and fallback mock system

#### Test Case D2: Order Tracking
1. Navigate to order tracking page
2. Verify all order information displays correctly
3. Test real-time updates when order status changes
4. Check tracking steps show proper progression
5. Verify order type-specific information (rent period, bid amount)

### E. Delivery Confirmation Testing

#### Test Case E1: Secret Code Generation
1. After shipping label generation
2. Verify 6-digit secret code is created
3. Check code is stored in Firestore with expiration
4. Confirm buyer receives delivery confirmation email

#### Test Case E2: Manual Delivery Confirmation
1. Navigate to order tracking page
2. Enter correct secret code
3. Verify funds are released to seller
4. Check order status updates to 'completed'
5. Verify escrow release timestamp

#### Test Case E3: Auto-Release After 3 Days
1. Create test order with past escrow release date
2. Run scheduled function manually or wait for cron
3. Verify funds are automatically released
4. Check order status updates appropriately

### F. Email Notification Testing

#### Test Case F1: Checkout Confirmation Email
1. Complete purchase
2. Verify buyer receives confirmation email
3. Check email contains correct order details
4. Verify seller receives shipping reminder

#### Test Case F2: Shipping Notification Email
1. When order ships
2. Verify buyer receives shipping notification
3. Check tracking number is included
4. Verify delivery confirmation instructions

#### Test Case F3: Delivery Confirmation Email
1. When package is delivered
2. Verify buyer receives email with secret code
3. Check auto-release warning is included
4. Test email links work correctly

### G. Error Handling Testing

#### Test Case G1: Invalid Checkout Data
1. Test with missing shipping address
2. Test with invalid price amounts
3. Test with unavailable rental periods
4. Test with insufficient bid amounts
5. Verify appropriate error messages

#### Test Case G2: Network Failures
1. Simulate network interruption during checkout
2. Test Stripe webhook failures
3. Test Shippo API failures
4. Verify graceful degradation and error recovery

#### Test Case G3: Edge Cases
1. Test purchasing unavailable listings
2. Test self-purchase prevention
3. Test concurrent purchases of same item
4. Test with expired payment sessions

## Performance Testing

### Load Testing
1. Test multiple concurrent checkouts
2. Verify database performance with many orders
3. Test email sending under load
4. Check Stripe API rate limits

### Mobile Testing
1. Test checkout flow on mobile devices
2. Verify responsive design works correctly
3. Test address entry on mobile keyboards
4. Check payment flow on mobile browsers

## Security Testing

### Payment Security
1. Verify PCI compliance through Stripe
2. Test that sensitive data is not logged
3. Verify webhook signature validation
4. Test payment intent metadata security

### Access Control
1. Test users can only access their own orders
2. Verify address data is properly isolated
3. Test seller can't access buyer payment info
4. Check admin access controls

## Monitoring and Analytics

### Test Monitoring Setup
1. Verify order events are logged
2. Check error tracking is working
3. Test performance monitoring
4. Verify business metrics collection

## Post-Testing Checklist

- [ ] All test cases pass
- [ ] Error handling works correctly
- [ ] Email notifications are sent
- [ ] Payment processing is secure
- [ ] Mobile experience is smooth
- [ ] Performance is acceptable
- [ ] Monitoring is in place
- [ ] Documentation is updated

## Known Issues and Limitations

Document any known issues discovered during testing:
1. [Issue description and workaround]
2. [Performance considerations]
3. [Browser compatibility notes]

## Test Environment Cleanup

After testing:
1. Clear test orders from Firestore
2. Remove test addresses
3. Clear test payment intents from Stripe
4. Reset test user accounts
5. Clear email logs

## Production Deployment Checklist

Before going live:
- [ ] Switch to production Stripe keys
- [ ] Update email templates with production URLs
- [ ] Configure production Shippo account
- [ ] Set up production monitoring
- [ ] Test with real payment methods (small amounts)
- [ ] Verify tax calculation in production
- [ ] Test email delivery to real addresses
- [ ] Monitor initial transactions closely
