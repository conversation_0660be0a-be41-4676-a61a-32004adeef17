import React from 'react';
import { ShoppingCart, MessageCircle, Calendar, Gavel, Clock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { Listing } from '../firebase/types';

interface BuyAndChatActionsProps {
  listing: Listing;
  sellerId: string;
  available?: boolean;
  className?: string;
}

const BuyAndChatActions: React.FC<BuyAndChatActionsProps> = ({
  listing,
  sellerId,
  available = true,
  className = ""
}) => {
  const navigate = useNavigate();
  const { userProfile } = useAuth();

  const checkUserAuth = () => {
    if (!userProfile) {
      navigate('/login', { state: { redirectTo: `/listing/${listing.id}` } });
      return false;
    }

    if (userProfile.uid === sellerId) {
      alert("You cannot interact with your own listing");
      return false;
    }

    return true;
  };

  const handleBuyNow = () => {
    if (!checkUserAuth()) return;

    navigate(`/checkout/${listing.id}`, {
      state: {
        action: {
          type: 'buy',
          price: listing.price
        }
      }
    });
  };

  const handleRentNow = (period: 'weekly' | 'monthly') => {
    if (!checkUserAuth()) return;

    const price = period === 'weekly' ? listing.weeklyPrice : listing.monthlyPrice;

    navigate(`/checkout/${listing.id}`, {
      state: {
        action: {
          type: 'rent',
          price: price,
          period: period
        }
      }
    });
  };

  const handlePlaceBid = () => {
    if (!checkUserAuth()) return;

    // For now, redirect to checkout with current bid amount
    // In a full implementation, this would open a bid modal first
    const bidAmount = listing.currentBid ? listing.currentBid + 1 : listing.startingBid || listing.price;

    navigate(`/checkout/${listing.id}`, {
      state: {
        action: {
          type: 'bid',
          price: bidAmount,
          bidAmount: bidAmount
        }
      }
    });
  };

  const handleChatSeller = () => {
    if (!checkUserAuth()) return;
    navigate('/messages', { state: { sellerId, listingId: listing.id } });
  };

  const renderActionButtons = () => {
    const buttons = [];

    // Buy Now button for sell listings
    if (listing.type === 'sell') {
      buttons.push(
        <button
          key="buy"
          onClick={handleBuyNow}
          disabled={!available}
          className="w-full bg-gradient-to-r from-success-600 to-success-700 text-white py-4 rounded-xl font-bold text-lg hover:from-success-700 hover:to-success-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          <ShoppingCart className="w-5 h-5" />
          <span>Buy Now - ${listing.price}</span>
        </button>
      );
    }

    // Rent buttons for rent listings
    if (listing.type === 'rent') {
      if (listing.weeklyPrice) {
        buttons.push(
          <button
            key="rent-weekly"
            onClick={() => handleRentNow('weekly')}
            disabled={!available}
            className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 rounded-xl font-bold text-lg hover:from-blue-700 hover:to-blue-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            <Calendar className="w-5 h-5" />
            <span>Rent Weekly - ${listing.weeklyPrice}</span>
          </button>
        );
      }

      if (listing.monthlyPrice) {
        buttons.push(
          <button
            key="rent-monthly"
            onClick={() => handleRentNow('monthly')}
            disabled={!available}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-4 rounded-xl font-bold text-lg hover:from-purple-700 hover:to-purple-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            <Calendar className="w-5 h-5" />
            <span>Rent Monthly - ${listing.monthlyPrice}</span>
          </button>
        );
      }
    }

    // Place Bid button for auction listings
    if (listing.type === 'auction') {
      const currentBid = listing.currentBid || listing.startingBid || listing.price;
      const nextBidAmount = listing.currentBid ? listing.currentBid + 1 : currentBid;

      buttons.push(
        <button
          key="bid"
          onClick={handlePlaceBid}
          disabled={!available}
          className="w-full bg-gradient-to-r from-orange-600 to-orange-700 text-white py-4 rounded-xl font-bold text-lg hover:from-orange-700 hover:to-orange-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          <Gavel className="w-5 h-5" />
          <span>Place Bid - ${nextBidAmount}</span>
        </button>
      );

      // Show current bid info
      if (listing.currentBid) {
        buttons.push(
          <div key="bid-info" className="text-center text-sm text-gray-600 dark:text-gray-400">
            <Clock className="w-4 h-4 inline mr-1" />
            Current bid: ${listing.currentBid}
            {listing.highestBidder && listing.highestBidder !== userProfile?.uid && (
              <span className="block">You are not the highest bidder</span>
            )}
          </div>
        );
      }
    }

    return buttons;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {renderActionButtons()}

      <button
        onClick={handleChatSeller}
        className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2"
      >
        <MessageCircle className="w-5 h-5" />
        <span>Message Seller</span>
      </button>
    </div>
  );
};

export default BuyAndChatActions;