# Admin Setup Guide for Hive Campus

This guide will help you set up the admin user and configure the 8-digit PIN authentication system for secure admin access.

## Current Status

✅ **Firebase Security Rules Updated** - Admin permissions are now properly configured
✅ **Admin PIN Authentication System Created** - 8-digit PIN system implemented
✅ **Error Handling Improved** - Better feedback for permission issues
⚠️ **Admin User Needs Setup** - Admin user role and permissions need to be configured

## Step 1: Fix Admin User Permissions

The admin user `<EMAIL>` needs proper role and custom claims set in Firebase.

### Option A: Using Firebase Console (Recommended)

1. **Go to Firebase Console**: https://console.firebase.google.com/project/h1c1-798a8
2. **Navigate to Authentication > Users**
3. **Find the user**: `<EMAIL>`
4. **Set Custom Claims**:
   - Click on the user
   - Go to "Custom claims" tab
   - Add the following JSON:
   ```json
   {
     "admin": true,
     "role": "admin"
   }
   ```

5. **Update Firestore Document**:
   - Go to Firestore Database
   - Navigate to `users` collection
   - Find the document with the user's UID
   - Update/add these fields:
   ```json
   {
     "role": "admin",
     "status": "active",
     "adminLevel": "super",
     "permissions": ["user_management", "content_moderation", "analytics", "system_settings", "super_admin"],
     "university": "Hive Campus Admin",
     "updatedAt": "2024-12-07T12:00:00.000Z"
   }
   ```

### Option B: Using Firebase CLI (Alternative)

1. **Open Terminal** in the project directory
2. **Run Firebase Functions Shell**:
   ```bash
   cd functions
   firebase functions:shell
   ```
3. **Execute the fix function**:
   ```javascript
   fixAdminUser({email: '<EMAIL>'})
   ```

## Step 2: Deploy Firebase Functions (If Not Already Done)

```bash
# Build functions
cd functions
npm run build

# Deploy admin functions
firebase deploy --only functions
```

## Step 3: Test Admin Login

1. **Login to the app** with `<EMAIL>`
2. **Navigate to admin dashboard** - should redirect automatically
3. **Verify access** - all admin sections should now work without permission errors

## Step 4: Set Up 8-Digit PIN (Currently Disabled for Setup)

The PIN authentication is temporarily disabled to allow initial setup. Once the admin user is properly configured:

1. **Re-enable PIN authentication** by uncommenting lines in `src/components/admin/AdminPanel.tsx`:
   ```typescript
   // Change this:
   // if (!pinVerified) {
   //   return <AdminPinAuth onPinVerified={handlePinVerified} />;
   // }
   
   // To this:
   if (!pinVerified) {
     return <AdminPinAuth onPinVerified={handlePinVerified} />;
   }
   ```

2. **Login as admin** - you'll be prompted to set up your 8-digit PIN
3. **Create a secure PIN**:
   - Use 8 unique digits
   - Avoid sequential numbers (12345678)
   - Avoid repeated digits (11111111)
   - Keep it secure and don't share it

4. **PIN Features**:
   - Required after email/password login
   - Session expires after 4 hours
   - Stored securely (hashed) in Firebase
   - Can be changed by admin users

## Step 5: Verify All Admin Functions

After setup, test these admin dashboard sections:

- ✅ **Overview** - Platform metrics and analytics
- ✅ **Users** - User management and moderation
- ✅ **Universities** - University data and analytics
- ✅ **Listings** - Listing management and moderation
- ✅ **Transactions** - Order and payment management
- ✅ **Chat & Messaging** - Message moderation
- ✅ **Shipping Management** - Shipping labels and tracking
- ✅ **Analytics** - Platform analytics and insights
- ✅ **Reports** - Admin reports and moderation
- ✅ **ReeFlex** - AI-powered insights and monitoring
- ✅ **Wallet Reports** - Wallet transaction analytics
- ✅ **Settings** - System configuration

## Troubleshooting

### Permission Denied Errors
- Verify admin user has `role: "admin"` in Firestore
- Check custom claims include `{"admin": true, "role": "admin"}`
- Ensure Firebase security rules are deployed

### PIN Setup Issues
- Make sure admin functions are deployed
- Check browser console for detailed error messages
- Verify admin user permissions are set correctly

### Function Deployment Issues
- Run `npm run build` in functions directory
- Check for TypeScript compilation errors
- Deploy with `firebase deploy --only functions`

## Security Notes

- The 8-digit PIN adds an extra layer of security after email/password authentication
- PIN sessions expire automatically after 4 hours of inactivity
- All admin actions are logged for audit purposes
- Admin access is restricted by Firebase security rules

## Support

If you encounter issues:
1. Check the browser console for detailed error messages
2. Verify all steps in this guide have been completed
3. Ensure Firebase project permissions are correctly configured
4. Contact support with specific error messages if needed

---

**Next Steps**: Once admin access is working, you can proceed with testing all admin dashboard functionality and setting up the PIN authentication system.
