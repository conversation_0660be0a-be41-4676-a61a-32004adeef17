# ReeFlex Implementation Guide

ReeFlex is an intelligent, in-app AI observability agent for Hive Campus that passively monitors app activity and provides actionable insights to improve user experience.

## Overview

ReeFlex consists of several components:

1. **ReeFlexTracker**: A React component that monitors user interactions, performance, and errors
2. **FloatingFeedback**: A UI component that allows users to submit structured feedback
3. **logReeFlexEvent()**: A utility function for logging events across the application
4. **Firestore Data Schema**: Collections for storing activity and feedback data
5. **sendReeFlexReport**: A Firebase function that generates daily reports with AI-powered insights

## Components

### 1. ReeFlexTracker

The `ReeFlexTracker` component is a passive monitoring system that tracks:

- Route changes and navigation times
- Performance issues (slow pages, resources, long tasks)
- User interactions (clicks, form submissions)
- API calls and errors
- JavaScript errors and unhandled promise rejections
- Checkout flow steps

It's added to the main App component and doesn't render any UI elements.

### 2. FloatingFeedback

The `FloatingFeedback` component provides a floating button that expands into a feedback form when clicked. It allows users to:

- Submit positive or negative feedback
- Select a feedback category
- Provide detailed comments
- Submit feedback with context (route, user role, timestamp)

### 3. logReeFlexEvent() Utility

The `logReeFlexEvent()` function is used throughout the application to log structured events to Firestore. It handles:

- User and route context
- Event batching for high-frequency events
- Error deduplication
- Device information collection
- Integration with Sentry for error tracking

### 4. Firestore Data Schema

ReeFlex uses two main Firestore collections:

- **reeflex_activity**: Stores all events tracked by the system
- **reeflex_feedback**: Stores user feedback submissions

Each document includes user context, route information, timestamps, and event-specific data.

### 5. sendReeFlexReport Function

The `sendReeFlexReport` Firebase function:

- Runs daily via a cron schedule
- Aggregates the last 24 hours of activity and feedback
- Generates statistics on errors, performance, user behavior, and feedback
- Uses AI to create an intelligent summary with actionable insights
- Sends a formatted email report to the app owner

## Integration Points

ReeFlex integrates with several existing systems:

- **Sentry**: Uses Sentry event IDs to correlate ReeFlex events with Sentry error reports
- **Firebase Auth**: Captures user context for all events
- **React Router**: Tracks route changes and navigation performance
- **Firestore**: Stores all event and feedback data

## Setup Instructions

1. **Add Components to App**:
   ```jsx
   // In App.tsx
   import ReeFlexTracker from './components/ReeFlexTracker';
   import FloatingFeedback from './components/FloatingFeedback';
   
   // Inside your App component
   <ReeFlexTracker />
   <FloatingFeedback position="bottom-right" />
   ```

2. **Configure Environment Variables**:
   - Add `REEFLEX_OWNER_EMAIL` to your Firebase functions environment
   - Set up OpenAI API keys (for future AI integration)

3. **Deploy Firebase Functions**:
   ```bash
   firebase deploy --only functions:sendReeFlexReport
   ```

4. **Add Event Logging to Key Components**:
   ```jsx
   import { logReeFlexEvent } from '../utils/reeflex';
   
   // Example: Log when a listing is created
   const handleCreateListing = async () => {
     try {
       const result = await createListing(listingData);
       logReeFlexEvent('listing_created', { 
         listing_id: result.id,
         category: listingData.category
       });
     } catch (error) {
       // Error handling
     }
   };
   ```

## Scaling Considerations

As usage grows, consider the following optimizations:

1. **Adjust Batch Sizes**: Modify the batch size and interval in `reeflex.ts` based on traffic volume
2. **Implement Sampling**: For very high traffic, implement sampling for common events
3. **Optimize Firestore Queries**: Add indexes for common query patterns
4. **Set Up TTL**: Implement time-to-live for older events to manage database size
5. **Increase AI Capabilities**: Integrate with more advanced AI models for deeper insights

## Privacy Considerations

ReeFlex is designed with privacy in mind:

- No PII is collected unless the user is authenticated
- Device information is only included for error and performance events
- Sensitive data is never logged (passwords, payment details, etc.)
- All data is stored in your Firebase project, not shared with third parties

## Future Enhancements

Planned for future versions:

1. **Anomaly Detection**: Automatically detect unusual patterns in user behavior or errors
2. **User Session Replay**: Reconstruct user sessions for debugging complex issues
3. **Predictive Analytics**: Forecast usage patterns and potential issues
4. **Auto-Fix Suggestions**: Generate code suggestions to fix common issues
5. **Integration with CI/CD**: Correlate issues with recent deployments

## Troubleshooting

Common issues and solutions:

- **High Firestore Costs**: Adjust batch sizes and implement sampling
- **Missing Events**: Check for errors in the browser console
- **Report Emails Not Sending**: Verify email configuration and permissions
- **Performance Impact**: Adjust the tracking sensitivity in ReeFlexTracker

## Conclusion

ReeFlex provides comprehensive observability for Hive Campus, helping identify and resolve issues before they impact users. The AI-powered insights make it easier to prioritize improvements and understand user behavior.