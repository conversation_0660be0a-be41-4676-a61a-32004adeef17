import React from 'react';
import { GraduationCap, Store, ArrowRight, Users, TrendingUp, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';
import AnimatedCard from '../components/AnimatedCard';
import AnimatedButton from '../components/AnimatedButton';

const LoginTypeSelection: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-6">
            <img
              src="/hive-campus-logo.svg"
              alt="Hive Campus Logo"
              className="w-12 h-12"
            />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">Hive Campus</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Choose Your Account Type</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400">
            Select the option that best describes you
          </p>
        </div>

        {/* Account Type Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Student Account */}
          <AnimatedCard variant="gradient" className="hover:shadow-2xl transform hover:scale-[1.02] transition-all duration-300">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                <GraduationCap className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Student Account</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Buy and sell with fellow students in your campus community
              </p>

              {/* Features */}
              <div className="space-y-3 mb-8">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Access to campus marketplace</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Verified student community</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Campus pickup & delivery</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Student-friendly pricing</span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">50K+</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Active Students</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">200+</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Universities</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <Link to="/signup" className="block">
                  <AnimatedButton className="w-full py-3 font-semibold">
                    <span className="flex items-center justify-center space-x-2">
                      <span>Sign Up as Student</span>
                      <ArrowRight className="w-4 h-4" />
                    </span>
                  </AnimatedButton>
                </Link>
                <Link
                  to="/login"
                  className="block w-full border-2 border-primary-600 text-primary-600 dark:text-primary-400 py-3 rounded-xl font-semibold hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-200 text-center"
                >
                  Student Sign In
                </Link>
              </div>
            </div>
          </AnimatedCard>

          {/* Merchant Partner Account */}
          <AnimatedCard variant="layered" className="hover:shadow-2xl transform hover:scale-[1.02] transition-all duration-300">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-accent-500 to-accent-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                <Store className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Merchant Partner</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Reach thousands of students across university campuses
              </p>

              {/* Features */}
              <div className="space-y-3 mb-8">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-accent-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Business dashboard & analytics</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-accent-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Bulk listing management</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-accent-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Campus advertising tools</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-accent-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Priority customer support</span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-accent-600 dark:text-accent-400">1M+</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Student Reach</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-accent-600 dark:text-accent-400">500+</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Partner Brands</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <Link to="/merchant-signup" className="block">
                  <AnimatedButton className="w-full py-3 font-semibold">
                    <span className="flex items-center justify-center space-x-2">
                      <span>Become a Partner</span>
                      <ArrowRight className="w-4 h-4" />
                    </span>
                  </AnimatedButton>
                </Link>
                <Link
                  to="/merchant-login"
                  className="block w-full border-2 border-accent-600 text-accent-600 dark:text-accent-400 py-3 rounded-xl font-semibold hover:bg-accent-50 dark:hover:bg-accent-900/20 transition-all duration-200 text-center"
                >
                  Partner Sign In
                </Link>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Trust Indicators */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-10 h-10 bg-success-100 dark:bg-success-900/20 rounded-full flex items-center justify-center">
                <Shield className="w-5 h-5 text-success-600 dark:text-success-400" />
              </div>
              <div>
                <p className="font-semibold text-gray-900 dark:text-white">Secure & Verified</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">All users verified</p>
              </div>
            </div>
            <div className="flex items-center justify-center space-x-3">
              <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                <Users className="w-5 h-5 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <p className="font-semibold text-gray-900 dark:text-white">Trusted Community</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">4.9★ average rating</p>
              </div>
            </div>
            <div className="flex items-center justify-center space-x-3">
              <div className="w-10 h-10 bg-accent-100 dark:bg-accent-900/20 rounded-full flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-accent-600 dark:text-accent-400" />
              </div>
              <div>
                <p className="font-semibold text-gray-900 dark:text-white">Growing Fast</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Join 50K+ students</p>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Landing */}
        <div className="text-center mt-8">
          <Link
            to="/"
            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LoginTypeSelection;