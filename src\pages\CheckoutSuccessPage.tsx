import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { CheckCircle, Loader, ArrowRight } from 'lucide-react';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';
import { Order } from '../firebase/types';

const CheckoutSuccessPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrderFromSessionId = async () => {
      try {
        // Get the session_id from the URL query parameters
        const params = new URLSearchParams(location.search);
        const sessionId = params.get('session_id');
        
        if (!sessionId) {
          setError('No session ID found');
          setIsLoading(false);
          return;
        }
        
        // Query Firestore for the order with this session ID
        const ordersQuery = query(
          collection(db, 'orders'),
          where('stripeSessionId', '==', sessionId)
        );
        
        const ordersSnapshot = await getDocs(ordersQuery);
        
        if (ordersSnapshot.empty) {
          setError('Order not found');
          setIsLoading(false);
          return;
        }
        
        // Get the first matching order
        const orderDoc = ordersSnapshot.docs[0];
        setOrder({ id: orderDoc.id, ...orderDoc.data() } as Order);
        
        // Redirect to the order page after 5 seconds
        setTimeout(() => {
          navigate(`/order/${orderDoc.id}`);
        }, 5000);
      } catch (error: unknown) {
        console.error('Error fetching order:', error);
        setError(error instanceof Error ? error.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchOrderFromSessionId();
  }, [location, navigate]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-2 sm:p-4 overflow-x-hidden">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
        {isLoading ? (
          <div className="text-center py-12">
            <Loader className="w-12 h-12 text-primary-600 dark:text-primary-400 animate-spin mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Processing Your Order</h2>
            <p className="text-gray-600 dark:text-gray-400">
              Please wait while we confirm your payment...
            </p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-10 h-10 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Something Went Wrong</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {error}
            </p>
            <button
              onClick={() => navigate('/listings')}
              className="inline-flex items-center justify-center space-x-2 px-6 py-3 bg-primary-600 text-white rounded-xl font-semibold hover:bg-primary-700 transition-colors"
            >
              <span>Continue Shopping</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Order Successful!</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Your payment has been processed successfully. You'll be redirected to your order details in a moment.
            </p>
            <div className="mb-6">
              <p className="text-sm text-gray-500 dark:text-gray-400">Order ID</p>
              <p className="font-mono font-medium text-gray-900 dark:text-white">{order?.id}</p>
            </div>
            <button
              onClick={() => navigate(`/order/${order?.id}`)}
              className="inline-flex items-center justify-center space-x-2 px-6 py-3 bg-primary-600 text-white rounded-xl font-semibold hover:bg-primary-700 transition-colors"
            >
              <span>View Order Details</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CheckoutSuccessPage;