# 📧 Admin Notifications Setup Guide

This guide helps you configure email and Slack notifications for webhook events.

## 📧 Email Notifications Setup

### Option 1: Gmail (Recommended)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password:**
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
   - Copy the 16-character password

3. **Configure Firebase Functions:**
```bash
firebase functions:config:set \
  email.host="smtp.gmail.com" \
  email.port="587" \
  email.user="<EMAIL>" \
  email.pass="your-16-char-app-password" \
  email.from="<EMAIL>" \
  email.admin="<EMAIL>"
```

### Option 2: SendGrid (Production Recommended)

1. **Create SendGrid Account:** https://sendgrid.com/
2. **Get API Key:** Settings → API Keys → Create API Key
3. **Configure Firebase Functions:**
```bash
firebase functions:config:set \
  email.service="sendgrid" \
  email.api_key="your-sendgrid-api-key" \
  email.from="<EMAIL>" \
  email.admin="<EMAIL>"
```

### Option 3: Custom SMTP

```bash
firebase functions:config:set \
  email.host="your-smtp-host.com" \
  email.port="587" \
  email.user="your-smtp-username" \
  email.pass="your-smtp-password" \
  email.from="<EMAIL>" \
  email.admin="<EMAIL>"
```

## 💬 Slack Notifications Setup

### 1. Create Slack Webhook

1. **Go to Slack API:** https://api.slack.com/apps
2. **Create New App** → From scratch
3. **Add Incoming Webhooks:**
   - Features → Incoming Webhooks → Activate
   - Add New Webhook to Workspace
   - Select channel (e.g., #alerts, #admin)
   - Copy webhook URL

### 2. Configure Firebase Functions

```bash
firebase functions:config:set \
  slack.webhook_url="https://hooks.slack.com/services/YOUR/WEBHOOK/URL" \
  slack.channel="#alerts" \
  slack.username="Hive Campus Bot"
```

## 🚀 Deploy Configuration

After setting up notifications, deploy the functions:

```bash
firebase deploy --only functions
```

## 🧪 Test Notifications

### Test Email Notifications

```bash
# Test with a webhook event that triggers admin notification
stripe trigger transfer.failed --add transfer:failure_code=account_closed
```

### Test Slack Notifications

```bash
# Test with capability deactivation (triggers Slack alert)
# Use Stripe Dashboard to send test capability.updated with status=inactive
```

## 📊 Notification Events

### Email Notifications Sent For:
- ✅ Transfer failures (all types)
- ✅ Capability deactivations
- ✅ Past due compliance issues
- ✅ Critical webhook processing errors

### Slack Notifications Sent For:
- ✅ Transfer failures
- ✅ High-priority capability issues
- ✅ System errors requiring immediate attention

### In-App Notifications (Firestore) For:
- ✅ All capability status changes
- ✅ Transfer failures (to affected sellers)
- ✅ Compliance requirement updates
- ✅ Account status changes

## 🔧 Troubleshooting

### Email Issues:
- **Gmail "Less secure apps"**: Use App Passwords instead
- **SMTP errors**: Check host, port, and credentials
- **Rate limiting**: Consider SendGrid for production

### Slack Issues:
- **Webhook URL**: Ensure it starts with `https://hooks.slack.com/`
- **Channel permissions**: Bot needs permission to post
- **Message format**: Check Slack webhook payload format

### Firebase Config Issues:
```bash
# View current config
firebase functions:config:get

# Update specific values
firebase functions:config:set email.user="<EMAIL>"

# Deploy after changes
firebase deploy --only functions
```

## 📋 Configuration Checklist

### ✅ Email Setup:
- [ ] SMTP credentials configured
- [ ] Admin email address set
- [ ] Test email sent successfully
- [ ] Functions deployed with new config

### ✅ Slack Setup:
- [ ] Webhook URL configured
- [ ] Channel permissions verified
- [ ] Test message sent successfully
- [ ] Functions deployed with new config

### ✅ Testing:
- [ ] Transfer failure notifications work
- [ ] Capability update notifications work
- [ ] Error notifications work
- [ ] Both email and Slack receive alerts

## 🎯 Production Recommendations

1. **Use SendGrid** for email (better deliverability)
2. **Set up dedicated Slack channel** for alerts
3. **Configure email filtering** to prevent spam
4. **Test notification flow** before going live
5. **Monitor notification delivery** regularly

## 📞 Support

If you need help with notification setup:
1. Check Firebase Functions logs for errors
2. Verify SMTP/Slack credentials
3. Test with simple webhook events first
4. Ensure proper Firebase config deployment
