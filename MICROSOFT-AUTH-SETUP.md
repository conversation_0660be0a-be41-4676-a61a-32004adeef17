# Microsoft Authentication Setup - Quick Reference

## What You Need:

Firebase is asking for:
- **Application ID** (also called Client ID)
- **Application Secret** (also called Client Secret)
- **Redirect URL:** `https://h1c1-798a8.firebaseapp.com/__/auth/handler`

## Step-by-Step Instructions:

### 1. Go to Azure Portal
🔗 **URL:** https://portal.azure.com/
- Sign in with your Microsoft account
- Search for "App registrations"
- Click "New registration"

### 2. Register Your App
- **Name:** `Hive Campus` 
- **Account types:** Select "Accounts in any organizational directory and personal Microsoft accounts"
- **Redirect URI:** 
  - Type: `Web`
  - URL: `https://h1c1-798a8.firebaseapp.com/__/auth/handler`
- Click "Register"

### 3. Get Application ID
- After registration, you'll see the app overview
- **Copy the "Application (client) ID"** → This is your **Application ID** for Firebase

### 4. Create Application Secret
- Click "Certificates & secrets" in left menu
- Click "New client secret"
- Description: `Firebase Auth Secret`
- Expiration: `24 months`
- Click "Add"
- **Copy the "Value" immediately** (it disappears after you leave the page) → This is your **Application Secret** for Firebase

### 5. Complete Firebase Setup
- Go back to Firebase Console
- Authentication → Sign-in method → Microsoft
- Paste **Application ID**
- Paste **Application Secret**  
- Click "Save"

## Important Notes:

⚠️ **Copy the secret value immediately** - it's only shown once!

✅ **Your redirect URL is already correct:** `https://h1c1-798a8.firebaseapp.com/__/auth/handler`

🎯 **After setup, users can sign in with:**
- Personal Microsoft accounts (outlook.com, hotmail.com, live.com)
- Work/school accounts (.edu domains)
- Azure AD accounts

## Testing Microsoft Login:

1. Complete the setup above
2. Go to your app's login page
3. Click "Continue with Microsoft" button
4. You'll be redirected to Microsoft's login page
5. After successful login, you'll be redirected back to your app

The Microsoft authentication is already coded in your app - you just need to complete the Azure setup!