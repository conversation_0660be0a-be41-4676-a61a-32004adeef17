import { describe, it, expect } from 'vitest';
import { 
  roundPrice, 
  formatPrice, 
  calculateCheckoutTotal, 
  isTextbookOrCourseMaterial, 
  getPlatformFeeRate 
} from '../priceUtils';

describe('priceUtils', () => {
  describe('roundPrice', () => {
    it('should round prices to 2 decimal places', () => {
      expect(roundPrice(99.999)).toBe(100);
      expect(roundPrice(99.994)).toBe(99.99);
      expect(roundPrice(100.001)).toBe(100);
      expect(roundPrice(100.006)).toBe(100.01);
    });

    it('should handle floating point precision issues', () => {
      expect(roundPrice(0.1 + 0.2)).toBe(0.3); // 0.1 + 0.2 = 0.30000000000000004
      expect(roundPrice(99.99999999999999)).toBe(100);
    });
  });

  describe('formatPrice', () => {
    it('should format prices with 2 decimal places', () => {
      expect(formatPrice(100)).toBe('100.00');
      expect(formatPrice(99.9)).toBe('99.90');
      expect(formatPrice(99.99)).toBe('99.99');
      expect(formatPrice(0.1)).toBe('0.10');
    });
  });

  describe('isTextbookOrCourseMaterial', () => {
    it('should identify textbook categories', () => {
      expect(isTextbookOrCourseMaterial('textbooks')).toBe(true);
      expect(isTextbookOrCourseMaterial('course-materials')).toBe(true);
      expect(isTextbookOrCourseMaterial('Textbooks')).toBe(true);
      expect(isTextbookOrCourseMaterial('COURSE-MATERIALS')).toBe(true);
    });

    it('should not identify non-textbook categories', () => {
      expect(isTextbookOrCourseMaterial('electronics')).toBe(false);
      expect(isTextbookOrCourseMaterial('clothing')).toBe(false);
      expect(isTextbookOrCourseMaterial('furniture')).toBe(false);
    });
  });

  describe('getPlatformFeeRate', () => {
    it('should return 8% for textbooks and course materials', () => {
      expect(getPlatformFeeRate('textbooks')).toBe(0.08);
      expect(getPlatformFeeRate('course-materials')).toBe(0.08);
    });

    it('should return 10% for other categories', () => {
      expect(getPlatformFeeRate('electronics')).toBe(0.10);
      expect(getPlatformFeeRate('clothing')).toBe(0.10);
      expect(getPlatformFeeRate('furniture')).toBe(0.10);
    });
  });

  describe('calculateCheckoutTotal', () => {
    it('should calculate total correctly for textbooks (8% platform fee)', () => {
      const result = calculateCheckoutTotal(100, 5.99, 0.08);
      
      expect(result.basePrice).toBe(100);
      expect(result.shippingFee).toBe(5.99);
      expect(result.platformFee).toBe(8); // 100 * 0.08
      expect(result.platformFeeRate).toBe(0.08);
      expect(result.total).toBe(113.99); // 100 + 5.99 + 8
    });

    it('should calculate total correctly for other items (10% platform fee)', () => {
      const result = calculateCheckoutTotal(100, 5.99, 0.10);
      
      expect(result.basePrice).toBe(100);
      expect(result.shippingFee).toBe(5.99);
      expect(result.platformFee).toBe(10); // 100 * 0.10
      expect(result.platformFeeRate).toBe(0.10);
      expect(result.total).toBe(115.99); // 100 + 5.99 + 10
    });

    it('should handle floating point precision issues', () => {
      const result = calculateCheckoutTotal(99.99, 5.99, 0.08);
      
      expect(result.basePrice).toBe(99.99);
      expect(result.shippingFee).toBe(5.99);
      expect(result.platformFee).toBe(8); // 99.99 * 0.08 = 7.9992, rounded to 8.00
      expect(result.total).toBe(113.98); // 99.99 + 5.99 + 8.00
    });

    it('should handle the original issue: 99.99 + 5.99 should not be 105.97999999999999', () => {
      const result = calculateCheckoutTotal(99.99, 5.99, 0.08);
      
      // This should be properly rounded, not 105.97999999999999
      expect(result.total).toBe(113.98);
      expect(result.total.toString()).not.toContain('999999');
    });
  });
});
