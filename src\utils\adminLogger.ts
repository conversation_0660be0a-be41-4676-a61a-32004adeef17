import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { firestore } from '../firebase/config';

export type AdminLogType = 
  | 'order_created'
  | 'payment_succeeded'
  | 'code_generated'
  | 'code_confirmed'
  | 'code_resent'
  | 'shipping_label_generated'
  | 'order_shipped'
  | 'order_delivered'
  | 'auto_released'
  | 'delivery_confirmed'
  | 'manual_override'
  | 'refund_issued';

export interface AdminLogEntry {
  id?: string;
  type: AdminLogType;
  orderId: string;
  userId: string;
  action: string;
  details: Record<string, any>;
  timestamp: Timestamp;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Log admin actions for audit trail
 */
export const logAdminAction = async (
  type: AdminLogType,
  orderId: string,
  userId: string,
  action: string,
  details: Record<string, any> = {}
): Promise<void> => {
  try {
    const logEntry: AdminLogEntry = {
      type,
      orderId,
      userId,
      action,
      details,
      timestamp: Timestamp.now(),
      ipAddress: await getClientIP(),
      userAgent: navigator.userAgent
    };

    await addDoc(collection(firestore, 'adminLogs'), logEntry);
    console.log(`Admin action logged: ${type} for order ${orderId}`);
  } catch (error) {
    console.error('Failed to log admin action:', error);
    // Don't throw error to avoid breaking the main flow
  }
};

/**
 * Get client IP address (simplified for demo)
 */
const getClientIP = async (): Promise<string> => {
  try {
    // In a real app, you'd get this from your backend
    return 'unknown';
  } catch {
    return 'unknown';
  }
};

/**
 * Log order creation
 */
export const logOrderCreated = (orderId: string, buyerId: string, details: Record<string, any>) => {
  return logAdminAction('order_created', orderId, buyerId, 'Order created', details);
};

/**
 * Log payment success
 */
export const logPaymentSucceeded = (orderId: string, buyerId: string, details: Record<string, any>) => {
  return logAdminAction('payment_succeeded', orderId, buyerId, 'Payment succeeded', details);
};

/**
 * Log secret code generation
 */
export const logCodeGenerated = (orderId: string, sellerId: string, details: Record<string, any>) => {
  return logAdminAction('code_generated', orderId, sellerId, 'Secret code generated', details);
};

/**
 * Log secret code confirmation
 */
export const logCodeConfirmed = (orderId: string, buyerId: string, details: Record<string, any>) => {
  return logAdminAction('code_confirmed', orderId, buyerId, 'Secret code confirmed', details);
};

/**
 * Log shipping label generation
 */
export const logShippingLabelGenerated = (orderId: string, sellerId: string, details: Record<string, any>) => {
  return logAdminAction('shipping_label_generated', orderId, sellerId, 'Shipping label generated', details);
};

/**
 * Log order delivery
 */
export const logOrderDelivered = (orderId: string, buyerId: string, details: Record<string, any>) => {
  return logAdminAction('order_delivered', orderId, buyerId, 'Order delivered', details);
};

/**
 * Log automatic fund release
 */
export const logAutoReleased = (orderId: string, systemUserId: string, details: Record<string, any>) => {
  return logAdminAction('auto_released', orderId, systemUserId, 'Funds auto-released after 72 hours', details);
};
