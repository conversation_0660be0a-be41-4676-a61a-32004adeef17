import * as Sentry from '@sentry/react';
import React from 'react';
import { 
  useLocation, 
  useNavigationType, 
  createRoutesFromChildren, 
  matchRoutes 
} from 'react-router-dom';
import { auth } from '../firebase/config';
import { User } from 'firebase/auth';

// Environment variables
const SENTRY_DSN = import.meta.env.VITE_SENTRY_DSN || "SENTRY_DSN_PLACEHOLDER";
const ENVIRONMENT = import.meta.env.MODE || 'development';
const RELEASE = import.meta.env.VITE_APP_VERSION || 'unknown';

/**
 * Initialize Sentry with configuration
 */
export const initSentry = () => {
  // In development with placeholder DSN, don't initialize Sentry
  if (SENTRY_DSN === "SENTRY_DSN_PLACEHOLDER" && ENVIRONMENT !== 'production') {
    console.log(`Sentry disabled in ${ENVIRONMENT} environment (using placeholder DSN)`);
    return;
  }

  Sentry.init({
    dsn: SENTRY_DSN,
    environment: ENVIRONMENT,
    release: RELEASE,
    
    // Enhanced integrations for better monitoring
    integrations: [
      Sentry.reactRouterV6BrowserTracingIntegration({
        useEffect: React.useEffect,
        useLocation,
        useNavigationType,
        createRoutesFromChildren,
        matchRoutes,
      }),
      Sentry.replayIntegration({
        sessionSampleRate: ENVIRONMENT === 'production' ? 0.0 : 0.1,
        errorSampleRate: ENVIRONMENT === 'production' ? 1.0 : 1.0,
        maskAllText: ENVIRONMENT === 'production',
        maskAllInputs: true,
        blockAllMedia: false,
      }),
      Sentry.feedbackIntegration({
        colorScheme: 'system',
        showBranding: false,
        isNameRequired: false,
        isEmailRequired: false,
      }),
    ],
    
    // Performance monitoring
    tracesSampleRate: ENVIRONMENT === 'production' ? 0.2 : 1.0,
    profilesSampleRate: ENVIRONMENT === 'production' ? 0.1 : 1.0,
    
    // Enhanced error filtering
    beforeSend(event, hint) {
      const errorMessage = event.exception?.values?.[0]?.value || '';
      
      // Common browser/library errors to ignore
      const ignoredErrors = [
        'ResizeObserver loop',
        'Non-Error promise rejection captured',
        'Script error',
        'Network Error',
        'ChunkLoadError',
        'Loading chunk',
        'Loading CSS chunk',
        'AbortError',
        'NotFoundError',
        'QuotaExceededError'
      ];
      
      if (ignoredErrors.some(ignored => errorMessage.includes(ignored))) {
        return null;
      }
      
      // Filter out extension errors
      if (event.request?.url?.includes('chrome-extension://') || 
          event.request?.url?.includes('moz-extension://') ||
          event.request?.url?.includes('safari-extension://')) {
        return null;
      }
      
      // Add additional context
      event.extra = event.extra || {};
      event.extra.userAgent = navigator.userAgent;
      event.extra.timestamp = new Date().toISOString();
      
      if (hint.originalException) {
        event.extra.originalException = hint.originalException;
      }
      
      return event;
    },
    
    // Performance monitoring configuration
    beforeSendTransaction(event) {
      if (event.start_timestamp && event.timestamp) {
        const duration = event.timestamp - event.start_timestamp;
        if (duration < 0.001) {
          return null;
        }
      }
      
      event.extra = event.extra || {};
      event.extra.connectionType = (navigator as { connection?: { effectiveType: string } }).connection?.effectiveType;
      event.extra.deviceMemory = (navigator as { deviceMemory?: number }).deviceMemory;
      
      return event;
    },
    
    // Initial scope
    initialScope: {
      tags: {
        component: 'hive-campus-frontend',
        version: RELEASE,
      },
    },
  });
  
  // Set release version if available
  if (import.meta.env.VITE_APP_VERSION) {
    Sentry.setTag('release', import.meta.env.VITE_APP_VERSION);
  }
  
  console.log(`Sentry initialized in ${ENVIRONMENT} environment`);
};

/**
 * Set user context in Sentry when user logs in
 */
export const setSentryUser = (user: User | null, userRole?: string) => {
  if (user) {
    Sentry.setUser({
      id: user.uid,
      email: user.email || undefined,
      username: user.displayName || undefined,
      role: userRole || 'unknown',
    });
    
    if (userRole) {
      Sentry.setTag('user.role', userRole);
    }
    
    if (user.metadata?.creationTime) {
      Sentry.setTag('user.created_at', user.metadata.creationTime);
    }
  } else {
    Sentry.setUser(null);
  }
};

/**
 * Set current route context
 */
export const setRouteContext = (routeName: string) => {
  Sentry.addBreadcrumb({
    category: 'navigation',
    message: `Navigated to ${routeName}`,
    level: 'info',
  });
  
  Sentry.setTag('route', routeName);
};

/**
 * Capture custom events with context
 */
export const captureEvent = (
  eventName: string, 
  data?: Record<string, unknown>,
  level: Sentry.SeverityLevel = 'info'
) => {
  Sentry.addBreadcrumb({
    category: 'custom',
    message: eventName,
    data,
    level,
  });
  
  Sentry.captureMessage(eventName, {
    level,
    tags: {
      event_type: eventName,
    },
    extra: data,
  });
  
  if (ENVIRONMENT !== 'production') {
    console.log(`[Sentry Event] ${eventName}`, data);
  }
};

/**
 * Predefined event types for type safety
 */
export enum SentryEventType {
  SIGN_UP = 'sign_up',
  LOGIN = 'login',
  LOGOUT = 'logout',
  CHECKOUT_STARTED = 'checkout_started',
  CHECKOUT_COMPLETED = 'checkout_completed',
  CHECKOUT_FAILED = 'checkout_failed',
  LISTING_CREATED = 'listing_created',
  LISTING_UPDATED = 'listing_updated',
  LISTING_DELETED = 'listing_deleted',
  ORDER_PLACED = 'order_placed',
  SHIPPING_LABEL_GENERATED = 'shipping_label_generated',
  FEEDBACK_SUBMITTED = 'feedback_submitted',
  PROFILE_UPDATED = 'profile_updated',
  SEARCH_PERFORMED = 'search_performed',
  FEATURE_USED = 'feature_used',
  ERROR_BOUNDARY_TRIGGERED = 'error_boundary_triggered',
}

/**
 * Capture a typed event with context
 */
export const captureTypedEvent = (
  eventType: SentryEventType,
  data?: Record<string, unknown>,
  level: Sentry.SeverityLevel = 'info'
) => {
  captureEvent(eventType, data, level);
};

/**
 * Capture exception with additional context
 */
export const captureException = (
  error: Error, 
  context?: Record<string, unknown>
) => {
  Sentry.withScope((scope) => {
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        scope.setExtra(key, value);
      });
    }
    
    const currentUser = auth.currentUser;
    if (currentUser) {
      scope.setExtra('user_id', currentUser.uid);
      scope.setExtra('user_email', currentUser.email);
    }
    
    Sentry.captureException(error);
  });
  
  if (ENVIRONMENT !== 'production') {
    console.error('[Sentry Exception]', error, context);
  }
};

/**
 * Start performance monitoring for a specific operation
 */
export const startPerformanceTracking = (operationName: string) => {
  const transaction = Sentry.startSpan(
    {
      name: operationName,
      op: 'operation'
    },
    (span) => {
      return span;
    }
  );
  
  return transaction;
};

/**
 * Batch multiple events to reduce noise
 */
export class EventBatcher {
  private events: Map<string, { count: number, data: unknown[] }> = new Map();
  private timer: NodeJS.Timeout | null = null;
  private batchPeriod: number;
  
  constructor(batchPeriodMs = 10000) {
    this.batchPeriod = batchPeriodMs;
  }
  
  public addEvent(eventName: string, data?: unknown) {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, { count: 0, data: [] });
    }
    
    const eventData = this.events.get(eventName)!;
    eventData.count++;
    if (data) {
      eventData.data.push(data);
    }
    
    this.scheduleSend();
  }
  
  private scheduleSend() {
    if (!this.timer) {
      this.timer = setTimeout(() => this.sendBatchedEvents(), this.batchPeriod);
    }
  }
  
  private sendBatchedEvents() {
    this.events.forEach((eventData, eventName) => {
      captureEvent(`${eventName}_batch`, {
        count: eventData.count,
        samples: eventData.data.slice(0, 5),
        total_items: eventData.data.length,
      });
    });
    
    this.events.clear();
    this.timer = null;
  }
  
  public flush() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.sendBatchedEvents();
  }
}

// Export a singleton instance
export const eventBatcher = new EventBatcher();

// Export Sentry for direct access
export { Sentry };