# Sentry Integration Guide

This guide explains how Sentry is integrated into the Hive Campus application for error tracking, performance monitoring, and custom event tracking.

## Overview

Sentry is used to:
1. Capture frontend crashes with stack traces and route context
2. Tag reports with user information (ID, email, role)
3. Track custom events (sign up, checkout, listing creation, etc.)
4. Monitor performance metrics
5. Provide fallback UI for errors

## Configuration

### DSN Setup

The Sentry DSN is configured in `src/utils/sentry.ts`. Replace the placeholder with your actual DSN:

```typescript
// Environment variables
const SENTRY_DSN = "SENTRY_DSN_PLACEHOLDER"; // Replace with your actual DSN
```

### Environment Configuration

Sentry automatically detects the environment based on the Vite mode:

```typescript
const ENVIRONMENT = import.meta.env.MODE || 'development'; // 'development', 'staging', or 'production'
```

You can also set environment-specific configuration in your `.env` files:

```
# .env.development
VITE_SENTRY_ENVIRONMENT=development
VITE_SENTRY_TRACES_SAMPLE_RATE=1.0

# .env.production
VITE_SENTRY_ENVIRONMENT=production
VITE_SENTRY_TRACES_SAMPLE_RATE=0.2
```

## Features

### Error Tracking

Errors are automatically captured by the ErrorBoundary component in `src/components/ErrorBoundary.tsx`. This component:

1. Catches React component errors
2. Displays a user-friendly fallback UI
3. Reports the error to Sentry with context

### User Context

User information is automatically added to Sentry when a user logs in:

```typescript
// In AuthContext.tsx
setSentryUser(user, profile.role);
```

This includes:
- User ID
- Email
- Display name
- User role

### Route Tracking

Routes are tracked using the `SentryRouteTracker` component in `src/components/SentryRouteTracker.tsx`:

```typescript
// In App.tsx
<Router>
  <AuthProvider>
    <SentryRouteTracker />
    <AppRoutes />
  </AuthProvider>
</Router>
```

### Custom Events

You can track custom events using the `captureTypedEvent` function:

```typescript
import { captureTypedEvent, SentryEventType } from '../utils/sentry';

// Track a custom event
captureTypedEvent(SentryEventType.LISTING_CREATED, {
  listing_id: result.data.id,
  title: formData.title,
  price: parseFloat(formData.price),
  category: formData.category
});
```

Available event types are defined in the `SentryEventType` enum in `src/utils/sentry.ts`.

### Performance Monitoring

Performance is tracked using transactions:

```typescript
import { startPerformanceTracking } from '../utils/sentry';

// Start performance tracking
const transaction = startPerformanceTracking('checkout_process');

// ... perform operations ...

// Finish tracking
transaction.finish();
```

### Event Batching

For high-frequency events, you can use the `eventBatcher` to reduce noise:

```typescript
import { eventBatcher } from '../utils/sentry';

// Add events to the batch
eventBatcher.addEvent('search_result_viewed', { query: 'iphone', results: 42 });

// Manually flush events if needed
eventBatcher.flush();
```

## Filtering Events

You can filter out unwanted events in the `beforeSend` function in `src/utils/sentry.ts`:

```typescript
beforeSend(event) {
  // Don't send events in development if DSN is the placeholder
  if (SENTRY_DSN === "SENTRY_DSN_PLACEHOLDER" && ENVIRONMENT !== 'production') {
    console.warn('Sentry event not sent (using placeholder DSN):', event);
    return null;
  }
  
  // Filter out specific errors you don't want to track
  if (event.exception?.values?.[0]?.value?.includes('ResizeObserver loop')) {
    return null; // Ignore ResizeObserver errors (common and usually harmless)
  }
  
  return event;
}
```

## Best Practices

1. **Don't include PII**: Avoid sending personally identifiable information in events
2. **Use typed events**: Use the `SentryEventType` enum for consistency
3. **Set appropriate sampling rates**: Use lower rates in production
4. **Add context to errors**: Include relevant data when capturing exceptions
5. **Use transactions for performance**: Track important user flows with transactions

## Troubleshooting

If events aren't showing up in Sentry:

1. Check that the DSN is correctly configured
2. Verify that the environment is properly set
3. Check the browser console for Sentry initialization messages
4. Ensure the `beforeSend` function isn't filtering out your events
5. Check your Sentry project settings for any rate limiting

## Additional Resources

- [Sentry React Documentation](https://docs.sentry.io/platforms/javascript/guides/react/)
- [Performance Monitoring](https://docs.sentry.io/product/performance/)
- [Sentry TypeScript SDK](https://docs.sentry.io/platforms/javascript/guides/typescript/)