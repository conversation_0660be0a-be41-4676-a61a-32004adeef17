import * as functions from 'firebase-functions';
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { Stripe } from 'stripe';
import {
  createCheckoutSession,
  createConnectAccount,
  createStripeAccount,
  getStripeOnboardingLink,
  getStripeAccountStatus,
  getPendingPayouts,
  processPendingPayouts,
  handleStripeWebhook,
  releaseFundsWithCode,
  autoReleaseFunds,
  generateShippingLabel,
  getWalletBalance
} from './services/stripe.service';
import {
  CreateCheckoutSessionRequest,
  CreateConnectAccountRequest,
  ReleaseFundsWithCodeRequest,
  GenerateShippingLabelRequest
} from './types';
import { db } from './config/firebase';
import * as admin from 'firebase-admin';

// Initialize Express app
const app = express();

// Middleware
app.use(cors({ origin: true }));

// Create a raw body parser for Stripe webhooks
const rawBodyParser = bodyParser.raw({ type: 'application/json' });

// Regular JSON parser for other routes
const jsonParser = bodyParser.json();

// Stripe webhook endpoint
app.post('/webhook', rawBodyParser, async (req, res) => {
  try {
    const stripe = new Stripe(process.env.STRIPE_API_KEY!, {
      apiVersion: '2025-05-28.basil',
    });
    
    const signature = req.headers['stripe-signature'] as string;
    
    if (!signature) {
      res.status(400).send('Webhook Error: No Stripe signature provided');
      return;
    }
    
    const event = stripe.webhooks.constructEvent(
      req.body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
    
    await handleStripeWebhook(event);
    
    res.status(200).send({ received: true });
  } catch (error) {
    console.error('Webhook Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(400).send(`Webhook Error: ${errorMessage}`);
  }
});

// Create checkout session endpoint
app.post('/create-checkout-session', jsonParser, async (req, res) => {
  try {
    const { listingId, useWalletBalance, orderDetails } = req.body as CreateCheckoutSessionRequest;

    console.log('Received request body:', { listingId, useWalletBalance });
    console.log('Authorization header:', req.headers.authorization);

    if (!listingId) {
      res.status(400).send({ error: 'Listing ID is required' });
      return;
    }

    if (!req.headers.authorization) {
      res.status(401).send({ error: 'Unauthorized - No authorization header' });
      return;
    }

    const authHeader = req.headers.authorization;
    const userId = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;

    console.log('Extracted userId:', userId);

    if (!userId || userId.trim() === '') {
      res.status(401).send({ error: 'Unauthorized - Invalid user ID' });
      return;
    }

    const session = await createCheckoutSession(
      listingId,
      userId.trim(),
      useWalletBalance || false,
      orderDetails
    );

    res.status(200).send(session);
  } catch (error) {
    console.error('Error creating checkout session:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).send({ error: errorMessage });
  }
});

// Create Connect account endpoint
app.post('/connect-account', jsonParser, async (req, res) => {
  try {
    const { accountType } = req.body as CreateConnectAccountRequest;
    
    if (!accountType) {
      res.status(400).send({ error: 'Account type is required' });
      return;
    }
    
    if (!req.headers.authorization) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const userId = req.headers.authorization.split('Bearer ')[1];
    
    if (!userId) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const account = await createConnectAccount(userId, accountType);
    
    res.status(200).send(account);
  } catch (error) {
    console.error('Error creating Connect account:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).send({ error: errorMessage });
  }
});

// Release funds with code endpoint
app.post('/release-funds', jsonParser, async (req, res) => {
  try {
    const { orderId, code } = req.body as ReleaseFundsWithCodeRequest;
    
    if (!orderId || !code) {
      res.status(400).send({ error: 'Order ID and code are required' });
      return;
    }
    
    if (!req.headers.authorization) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const userId = req.headers.authorization.split('Bearer ')[1];
    
    if (!userId) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    // Check if the user is the buyer of the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    
    if (!orderDoc.exists) {
      res.status(404).send({ error: 'Order not found' });
      return;
    }
    
    const order = orderDoc.data();
    
    if (!order || order.buyerId !== userId) {
      res.status(403).send({ error: 'Forbidden' });
      return;
    }
    
    const success = await releaseFundsWithCode(orderId, code);
    
    res.status(200).send({ success });
  } catch (error) {
    console.error('Error releasing funds with code:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).send({ error: errorMessage });
  }
});

// Generate shipping label endpoint
app.post('/shipping-label', jsonParser, async (req, res) => {
  try {
    const { orderId } = req.body as GenerateShippingLabelRequest;
    
    if (!orderId) {
      res.status(400).send({ error: 'Order ID is required' });
      return;
    }
    
    if (!req.headers.authorization) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const userId = req.headers.authorization.split('Bearer ')[1];
    
    if (!userId) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    // Check if the user is the seller of the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    
    if (!orderDoc.exists) {
      res.status(404).send({ error: 'Order not found' });
      return;
    }
    
    const order = orderDoc.data();
    
    if (!order || order.sellerId !== userId) {
      res.status(403).send({ error: 'Forbidden' });
      return;
    }
    
    const label = await generateShippingLabel(orderId);
    
    res.status(200).send(label);
  } catch (error) {
    console.error('Error generating shipping label:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).send({ error: errorMessage });
  }
});

// Get wallet balance endpoint
app.get('/wallet-balance', async (req, res) => {
  try {
    if (!req.headers.authorization) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const userId = req.headers.authorization.split('Bearer ')[1];
    
    if (!userId) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const balance = await getWalletBalance(userId);
    
    res.status(200).send({ balance });
  } catch (error) {
    console.error('Error getting wallet balance:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).send({ error: errorMessage });
  }
});

// Get order by ID endpoint
app.get('/order/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    if (!orderId) {
      res.status(400).send({ error: 'Order ID is required' });
      return;
    }
    
    if (!req.headers.authorization) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const userId = req.headers.authorization.split('Bearer ')[1];
    
    if (!userId) {
      res.status(401).send({ error: 'Unauthorized' });
      return;
    }
    
    const orderDoc = await db.collection('orders').doc(orderId).get();
    
    if (!orderDoc.exists) {
      res.status(404).send({ error: 'Order not found' });
      return;
    }
    
    const order = orderDoc.data();
    
    // Check if the user is the buyer or seller of the order
    if (!order || (order.buyerId !== userId && order.sellerId !== userId)) {
      res.status(403).send({ error: 'Forbidden' });
      return;
    }
    
    res.status(200).send(order);
  } catch (error) {
    console.error('Error getting order:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).send({ error: errorMessage });
  }
});

// Enhanced Firebase Callable Functions for Stripe Connect

// Create Stripe Connect account
export const createStripeConnectAccount = functions.https.onCall(async (data, context) => {
  try {
    // Check authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { accountType } = data;
    const userId = context.auth.uid;

    if (!accountType || !['student', 'merchant'].includes(accountType)) {
      throw new functions.https.HttpsError('invalid-argument', 'Valid account type is required');
    }

    const result = await createStripeAccount(userId, accountType);
    return result;
  } catch (error) {
    console.error('Error in createStripeConnectAccount:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new functions.https.HttpsError('internal', errorMessage);
  }
});

// Get Stripe onboarding link
export const getStripeConnectOnboardingLink = functions.https.onCall(async (data, context) => {
  try {
    // Check authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { refreshUrl, returnUrl } = data;
    const userId = context.auth.uid;

    const result = await getStripeOnboardingLink(userId, refreshUrl, returnUrl);
    return result;
  } catch (error) {
    console.error('Error in getStripeConnectOnboardingLink:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new functions.https.HttpsError('internal', errorMessage);
  }
});

// Get Stripe account status
export const getStripeConnectAccountStatus = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      const result = await getStripeAccountStatus(userId);
      return result;
    } catch (error) {
      console.error('Error in getStripeConnectAccountStatus:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get pending payouts for seller
export const getSellerPendingPayouts = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      const result = await getPendingPayouts(userId);
      return result;
    } catch (error) {
      console.error('Error in getSellerPendingPayouts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Process pending payouts (admin function)
export const processSellerPendingPayouts = functions.https.onCall(async (data, context) => {
  try {
    // Check authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { sellerId } = data;

    if (!sellerId) {
      throw new functions.https.HttpsError('invalid-argument', 'Seller ID is required');
    }

    // For now, allow any authenticated user to trigger this (in production, add admin check)
    await processPendingPayouts(sellerId);
    return { success: true, message: 'Pending payouts processed successfully' };
  } catch (error) {
    console.error('Error in processSellerPendingPayouts:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new functions.https.HttpsError('internal', errorMessage);
  }
});

// Manual trigger for testing payout release (admin function)
export const triggerPayoutRelease = functions.https.onCall(async (data, context) => {
  try {
    // Check authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
    }

    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found');
    }

    const order = orderDoc.data();
    if (!order || !order.pendingPayout) {
      throw new functions.https.HttpsError('failed-precondition', 'Order does not have pending payout');
    }

    // Import the release function - use autoReleaseFunds which is exported
    const { autoReleaseFunds } = await import('./services/stripe.service');
    await autoReleaseFunds(orderId);

    return { success: true, message: 'Payout released successfully' };
  } catch (error) {
    console.error('Error in triggerPayoutRelease:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new functions.https.HttpsError('internal', errorMessage);
  }
});

// Export the Express app as a Firebase Function
export const stripeApi = functions.https.onRequest(app);

// Scheduled function to send onboarding reminders
export const sendOnboardingReminders = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async () => {
    try {
      console.log('Starting onboarding reminder check...');

      // Get all users with incomplete onboarding who have pending payouts
      const connectAccountsSnapshot = await db
        .collection('connectAccounts')
        .where('isOnboarded', '==', false)
        .get();

      if (connectAccountsSnapshot.empty) {
        console.log('No incomplete onboarding accounts found');
        return null;
      }

      const reminderPromises = connectAccountsSnapshot.docs.map(async (accountDoc) => {
        try {
          const connectAccount = accountDoc.data();
          const userId = connectAccount.userId;

          // Check if user has pending payouts
          const pendingOrdersSnapshot = await db
            .collection('orders')
            .where('sellerId', '==', userId)
            .where('pendingPayout', '==', true)
            .where('status', '==', 'payment_succeeded')
            .get();

          if (pendingOrdersSnapshot.empty) {
            console.log(`No pending payouts for user ${userId}, skipping reminder`);
            return;
          }

          // Check if reminder was already sent recently (within 24 hours)
          const lastReminderCheck = connectAccount.lastReminderSent;
          const now = new Date();
          const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

          if (lastReminderCheck && lastReminderCheck.toDate() > twentyFourHoursAgo) {
            console.log(`Reminder already sent recently for user ${userId}, skipping`);
            return;
          }

          // Get user details
          const userDoc = await db.collection('users').doc(userId).get();
          if (!userDoc.exists) {
            console.log(`User document not found for ${userId}`);
            return;
          }

          const user = userDoc.data();

          // Check if user data exists and has required fields
          if (!user || !user.email) {
            console.log(`User data incomplete for ${userId}, skipping reminder`);
            return;
          }

          // Calculate pending amount
          let totalPendingAmount = 0;
          pendingOrdersSnapshot.docs.forEach((orderDoc) => {
            const order = orderDoc.data();
            totalPendingAmount += order.sellerAmount || 0;
          });

          // Get or create onboarding URL
          let onboardingUrl = connectAccount.onboardingUrl;
          if (!onboardingUrl) {
            try {
              const result = await getStripeOnboardingLink(userId);
              onboardingUrl = result.onboardingUrl;
            } catch (error) {
              console.error(`Error getting onboarding URL for user ${userId}:`, error);
              return;
            }
          }

          // Send reminder email
          const { sendBatchOnboardingReminders } = await import('./email');
          await sendBatchOnboardingReminders([{
            email: user.email,
            name: user.name || user.displayName || 'User',
            pendingAmount: Math.round(totalPendingAmount * 100) / 100,
            orderCount: pendingOrdersSnapshot.docs.length,
            onboardingUrl: onboardingUrl,
          }]);

          // Update reminder tracking
          await db.collection('connectAccounts').doc(userId).update({
            lastReminderSent: admin.firestore.Timestamp.now(),
            reminderCount: admin.firestore.FieldValue.increment(1),
          });

          console.log(`Sent onboarding reminder to ${user.email} for $${totalPendingAmount.toFixed(2)}`);
        } catch (error) {
          console.error(`Error processing reminder for account ${accountDoc.id}:`, error);
        }
      });

      await Promise.all(reminderPromises);
      console.log('Completed onboarding reminder check');

      return null;
    } catch (error) {
      console.error('Error in sendOnboardingReminders:', error);
      return null;
    }
  });

// Scheduled function to auto-release funds after escrow period
export const checkEscrowPeriods = functions.pubsub
  .schedule('every 6 hours') // Check more frequently for better user experience
  .onRun(async () => {
    try {
      const now = admin.firestore.Timestamp.now();

      // Get all orders that are past their escrow release date
      const ordersSnapshot = await db
        .collection('orders')
        .where('status', 'in', ['shipped_pending_code', 'payment_succeeded'])
        .where('escrowReleaseDate', '<=', now)
        .limit(50) // Process in batches
        .get();

      if (ordersSnapshot.empty) {
        console.log('No orders ready for auto-release');
        return null;
      }

      console.log(`Processing ${ordersSnapshot.docs.length} orders for auto-release`);

      const promises = ordersSnapshot.docs.map(async (doc) => {
        try {
          await autoReleaseFunds(doc.id);
          console.log(`Successfully auto-released funds for order ${doc.id}`);
        } catch (error) {
          console.error(`Error auto-releasing funds for order ${doc.id}:`, error);
        }
      });

      await Promise.all(promises);

      return null;
    } catch (error) {
      console.error('Error checking escrow periods:', error);
      return null;
    }
  });