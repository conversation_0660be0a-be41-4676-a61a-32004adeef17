import * as functions from 'firebase-functions';
import * as nodemailer from 'nodemailer';

// Define interfaces for email attachments and options
interface EmailAttachment {
  filename: string;
  content?: string | Buffer;
  path?: string;
  contentType?: string;
}

interface EmailOptions {
  from?: string;
  text?: string;
  attachments?: EmailAttachment[];
}

// Define interfaces for report data
interface ReportError {
  type?: string;
  message: string;
  route?: string;
  url?: string;
  timestamp?: string;
}

interface ReportPage {
  url?: string;
  route?: string;
  loadTime?: number;
  duration?: number;
  visits?: number;
  count?: number;
  title?: string;
}

interface ReportQuote {
  text?: string;
  message?: string;
  type?: string;
  category?: string;
  route?: string;
  rating?: number;
  user?: string;
}

interface ReportData {
  summary?: {
    totalSessions?: number;
    totalUsers?: number;
    totalPageViews?: number;
    totalInteractions?: number;
  };
  errors: {
    count?: number;
    details: ReportError[];
  };
  performance: {
    slowestPages: ReportPage[];
  };
  userBehavior: {
    topPages: ReportPage[];
  };
  feedback: {
    userQuotes: ReportQuote[];
  };
}

// Email configuration from environment variables
const EMAIL_HOST = process.env.EMAIL_HOST || 'smtp.gmail.com';
const EMAIL_PORT = parseInt(process.env.EMAIL_PORT || '587', 10);
const EMAIL_USER = process.env.EMAIL_USER;
const EMAIL_PASS = process.env.EMAIL_PASS;
const EMAIL_FROM = process.env.EMAIL_FROM || '<EMAIL>';
const OWNER_EMAIL = process.env.VITE_APP_OWNER_EMAIL || '<EMAIL>';

// Create a transporter for sending emails
let transporter: nodemailer.Transporter | null = null;

/**
 * Initialize the email transporter
 */
function getTransporter(): nodemailer.Transporter {
  if (!transporter) {
    // Check if email credentials are configured
    if (!EMAIL_USER || !EMAIL_PASS) {
      throw new Error('Email credentials are not configured. Set the EMAIL_USER and EMAIL_PASS environment variables.');
    }
    
    // Create the transporter
    transporter = nodemailer.createTransport({
      host: EMAIL_HOST,
      port: EMAIL_PORT,
      secure: EMAIL_PORT === 465, // true for 465, false for other ports
      auth: {
        user: EMAIL_USER,
        pass: EMAIL_PASS
      }
    });
  }
  
  return transporter;
}

/**
 * Send an email
 * 
 * @param to Recipient email address
 * @param subject Email subject
 * @param html Email content in HTML format
 * @param options Additional email options
 * @returns A promise that resolves when the email is sent
 */
export async function sendEmail(
  to: string,
  subject: string,
  html: string,
  options: EmailOptions = {}
): Promise<boolean> {
  try {
    // Try to get the transporter
    let emailTransporter: nodemailer.Transporter;
    
    try {
      emailTransporter = getTransporter();
    } catch (error) {
      // Log the error but don't fail - we'll use a fallback
      functions.logger.warn('Email transporter initialization failed:', error);
      
      // Log the email content for development/testing
      functions.logger.info(`Would send email to ${to} with subject "${subject}"`);
      functions.logger.debug('Email content:', html);
      
      return false;
    }
    
    // Send the email
    const info = await emailTransporter.sendMail({
      from: options.from || EMAIL_FROM,
      to,
      subject,
      text: options.text || '',
      html,
      attachments: options.attachments
    });
    
    functions.logger.info(`Email sent: ${info.messageId}`);
    return true;
  } catch (error) {
    functions.logger.error('Error sending email:', error);
    return false;
  }
}

/**
 * Send a critical error alert email
 * 
 * @param errorType The type of error
 * @param count The number of occurrences
 * @param route The route where the error occurred
 * @param sentryId Optional Sentry event ID
 * @param details Additional details about the error
 */
export async function sendCriticalErrorEmail(
  errorType: string,
  count: number,
  route: string,
  sentryId?: string,
  details?: string
): Promise<boolean> {
  const subject = `🔥 ALERT: ${count}+ ${errorType} on ${route}`;
  
  const html = `
    <h1 style="color: #e53e3e;">Critical Error Alert</h1>
    <p><strong>${count}+ ${errorType}</strong> detected on <code>${route}</code> in the last hour.</p>
    ${sentryId ? `<p><strong>Sentry ID:</strong> ${sentryId}</p>` : ''}
    ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_activity">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send a performance alert email
 * 
 * @param route The route with performance issues
 * @param duration The duration in milliseconds
 * @param count The number of occurrences
 */
export async function sendPerformanceEmail(
  route: string,
  duration: number,
  count: number
): Promise<boolean> {
  const subject = `⏱ ALERT: ${count}+ slow page loads on ${route}`;
  
  const html = `
    <h1 style="color: #dd6b20;">Performance Alert</h1>
    <p><strong>${count}+ slow page loads</strong> detected on <code>${route}</code> in the last hour.</p>
    <p><strong>Average Duration:</strong> ${duration}ms</p>
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_activity">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send a payment failure alert email
 * 
 * @param count The number of payment failures
 * @param details Additional details about the failures
 */
export async function sendPaymentFailureEmail(
  count: number,
  details?: string
): Promise<boolean> {
  const subject = `💰 ALERT: ${count}+ payment failures`;
  
  const html = `
    <h1 style="color: #e53e3e;">Payment Failure Alert</h1>
    <p><strong>${count}+ payment failures</strong> detected in the last hour.</p>
    ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_activity">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send a feedback alert email when multiple users report the same issue
 * 
 * @param route The route with multiple feedback reports
 * @param category The feedback category
 * @param count The number of feedback reports
 * @param quotes Sample user quotes
 */
export async function sendFeedbackEmail(
  route: string,
  category: string,
  count: number,
  quotes: string[] = []
): Promise<boolean> {
  const subject = `🧩 ALERT: ${count}+ users reported issues with ${category}`;
  
  let quotesHtml = '';
  if (quotes.length > 0) {
    quotesHtml = `
      <h3>Sample Feedback:</h3>
      <ul>
        ${quotes.map(q => `<li>"${q}"</li>`).join('')}
      </ul>
    `;
  }
  
  const html = `
    <h1 style="color: #3182ce;">User Feedback Alert</h1>
    <p><strong>${count}+ users reported issues</strong> with <strong>${category}</strong> on <code>${route}</code>.</p>
    ${quotesHtml}
    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    <p>
      <a href="https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_feedback">
        View in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send the daily ReeFlex report email
 * 
 * @param report The report data
 * @param aiSummary The AI-generated summary
 */
export async function sendReportEmail(report: ReportData, aiSummary: string): Promise<boolean> {
  const subject = `📊 ReeFlex Daily Report: ${new Date().toLocaleDateString()}`;
  
  // Format the email content
  const html = `
    <h1>Daily ReeFlex Report</h1>
    <p>Report period: ${(report as unknown as any).timeRange?.start || 'Unknown'} to ${(report as unknown as any).timeRange?.end || 'Unknown'}</p>
    
    <h2>AI Summary</h2>
    <div style="padding: 15px; background-color: #f8f9fa; border-radius: 5px; margin-bottom: 20px;">
      ${aiSummary.replace(/\n/g, '<br>')}
    </div>
    
    <h2>Overview</h2>
    <ul>
      <li>Total Events: ${(report.summary as Record<string, unknown>)?.totalEvents || 0}</li>
      <li>Total Errors: ${(report.summary as Record<string, unknown>)?.totalErrors || 0}</li>
      <li>Total Feedback: ${(report.summary as Record<string, unknown>)?.totalFeedback || 0}</li>
      <li>Page Views: ${report.summary?.totalPageViews || 0}</li>
      <li>User Interactions: ${report.summary?.totalInteractions || 0}</li>
    </ul>
    
    <h2>Top Issues</h2>
    <h3>Errors (${report.errors.count})</h3>
    ${report.errors.details.length > 0 ? `
      <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
        <tr>
          <th>Type</th>
          <th>Message</th>
          <th>Route</th>
          <th>Time</th>
        </tr>
        ${report.errors.details.slice(0, 5).map((error: ReportError) => `
          <tr>
            <td>${error.type}</td>
            <td>${error.message}</td>
            <td>${error.route}</td>
            <td>${error.timestamp ? new Date(error.timestamp).toLocaleString() : 'N/A'}</td>
          </tr>
        `).join('')}
      </table>
    ` : '<p>No errors reported in this period.</p>'}
    
    <h3>Performance Issues</h3>
    ${report.performance.slowestPages.length > 0 ? `
      <h4>Slowest Pages</h4>
      <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
        <tr>
          <th>Route</th>
          <th>Duration (ms)</th>
        </tr>
        ${report.performance.slowestPages.map((page: ReportPage) => `
          <tr>
            <td>${page.route}</td>
            <td>${page.duration}</td>
          </tr>
        `).join('')}
      </table>
    ` : '<p>No slow page loads reported in this period.</p>'}
    
    <h2>User Behavior</h2>
    <h3>Most Visited Pages</h3>
    <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
      <tr>
        <th>Route</th>
        <th>Views</th>
      </tr>
      ${report.userBehavior.topPages.map((page: ReportPage) => `
        <tr>
          <td>${page.route}</td>
          <td>${page.count}</td>
        </tr>
      `).join('')}
    </table>
    
    <h3>Checkout Funnel</h3>
    <ul>
      <li>Started: ${(report.userBehavior as Record<string, any>)?.checkoutFunnel?.started || 0}</li>
      <li>Completed: ${(report.userBehavior as Record<string, any>)?.checkoutFunnel?.completed || 0}</li>
      <li>Failed: ${(report.userBehavior as Record<string, any>)?.checkoutFunnel?.failed || 0}</li>
      <li>Drop-off Rate: ${(report.userBehavior as Record<string, any>)?.checkoutFunnel?.dropOffRate || 0}</li>
    </ul>
    
    <h2>User Feedback</h2>
    <p>
      Positive: ${(report.feedback as Record<string, unknown>)?.positive || 0} |
      Negative: ${(report.feedback as Record<string, unknown>)?.negative || 0} |
      Neutral: ${(report.feedback as Record<string, unknown>)?.neutral || 0}
    </p>
    
    <h3>User Quotes</h3>
    ${report.feedback.userQuotes.length > 0 ? `
      <ul>
        ${report.feedback.userQuotes.map((quote: ReportQuote) => `
          <li>
            <strong>${quote.type} (${quote.category}):</strong> "${quote.message}"
            <em>- on ${quote.route}</em>
          </li>
        `).join('')}
      </ul>
    ` : '<p>No user quotes in this period.</p>'}
    
    <p>
      <a href="https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/~2Freeflex_activity">
        View full data in Firebase Console
      </a>
    </p>
  `;
  
  return sendEmail(OWNER_EMAIL, subject, html);
}

/**
 * Send order-related emails
 */
export const sendOrderEmail = async (
  type: 'checkout_confirmation' | 'shipping_reminder' | 'shipped_notification' | 'delivery_confirmation',
  recipientEmail: string,
  orderData: {
    orderId: string;
    buyerName: string;
    sellerName: string;
    itemTitle: string;
    amount: number;
    trackingNumber?: string;
    secretCode?: string;
    escrowReleaseDate?: string;
  }
): Promise<void> => {
  try {
    const transporter = getTransporter();

    let subject: string;
    let html: string;

    switch (type) {
      case 'checkout_confirmation':
        subject = `Order Confirmation - ${orderData.itemTitle}`;
        html = `
          <h1>🎉 Order Confirmed!</h1>
          <p>Hi ${orderData.buyerName},</p>
          <p>Your order has been confirmed and payment has been processed.</p>
          <h3>Order #${orderData.orderId}</h3>
          <p><strong>Item:</strong> ${orderData.itemTitle}</p>
          <p><strong>Amount:</strong> $${orderData.amount}</p>
          <p><strong>Seller:</strong> ${orderData.sellerName}</p>
          <p>The seller will be notified to ship your item within 48 hours.</p>
          <p><a href="https://hivecampus.app/order/${orderData.orderId}">Track Your Order</a></p>
        `;
        break;

      case 'shipping_reminder':
        subject = `Shipping Reminder - Order #${orderData.orderId}`;
        html = `
          <h1>📦 Time to Ship!</h1>
          <p>Hi ${orderData.sellerName},</p>
          <p><strong>⏰ Reminder:</strong> Please ship this order within 48 hours.</p>
          <h3>Order #${orderData.orderId}</h3>
          <p><strong>Item:</strong> ${orderData.itemTitle}</p>
          <p><strong>Amount:</strong> $${orderData.amount}</p>
          <p><strong>Buyer:</strong> ${orderData.buyerName}</p>
          <p><a href="https://hivecampus.app/order/${orderData.orderId}">View Order & Print Label</a></p>
        `;
        break;

      case 'shipped_notification':
        subject = `Your Order Has Shipped - ${orderData.itemTitle}`;
        html = `
          <h1>🚚 Your Order is On the Way!</h1>
          <p>Hi ${orderData.buyerName},</p>
          <p>Your order has been shipped and is on its way to you.</p>
          <h3>Tracking Information</h3>
          <p><strong>Order #:</strong> ${orderData.orderId}</p>
          <p><strong>Item:</strong> ${orderData.itemTitle}</p>
          <p><strong>Tracking Number:</strong> ${orderData.trackingNumber}</p>
          <p>When you receive your package, you'll need to enter a 6-digit secret code to confirm delivery.</p>
          <p><a href="https://hivecampus.app/order/${orderData.orderId}">Track Your Package</a></p>
        `;
        break;

      case 'delivery_confirmation':
        subject = `Delivery Confirmation Required - ${orderData.itemTitle}`;
        html = `
          <h1>📦 Package Delivered!</h1>
          <p>Hi ${orderData.buyerName},</p>
          <p>Your package has been delivered! Please confirm receipt to release payment to the seller.</p>
          <h3>Your Secret Code: ${orderData.secretCode}</h3>
          <p>Enter this code on the order tracking page to confirm delivery.</p>
          <p><strong>⏰ Important:</strong> If you don't confirm delivery by ${orderData.escrowReleaseDate}, payment will be automatically released.</p>
          <p><a href="https://hivecampus.app/order/${orderData.orderId}">Confirm Delivery Now</a></p>
        `;
        break;

      default:
        throw new Error(`Unknown email type: ${type}`);
    }

    const mailOptions = {
      from: `"Hive Campus" <${functions.config().email?.from || '<EMAIL>'}>`,
      to: recipientEmail,
      subject,
      html
    };

    await transporter.sendMail(mailOptions);
    console.log(`${type} email sent successfully to ${recipientEmail}`);
  } catch (error) {
    console.error(`Error sending ${type} email:`, error);
    throw error;
  }
};