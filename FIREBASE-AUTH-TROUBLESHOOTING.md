# 🔥 Firebase Auth Rate Limiting - Troubleshooting Guide

## ⚠️ Current Issue
- **Error:** `Firebase: Error (auth/too-many-requests)`
- **Status:** 400 errors on authentication endpoints
- **Cause:** Too many failed authentication attempts from your IP address

## 🚀 Immediate Solutions (Try in Order)

### 1. **Clear Browser Data** ⭐ (Most Effective)
1. Open your browser's Developer Tools (F12)
2. Go to **Application** tab (Chrome) or **Storage** tab (Firefox)
3. Clear all data for your domain:
   - **Local Storage** - Delete all entries
   - **Session Storage** - Delete all entries
   - **Cookies** - Delete all cookies for your domain
   - **Cache** - Clear cache
4. **Hard refresh** the page (Ctrl+Shift+R or Cmd+Shift+R)

### 2. **Use Incognito/Private Mode**
- Open your app in an incognito/private browser window
- This bypasses cached authentication state

### 3. **Try Different Network**
- Use mobile hotspot or different WiFi network
- Rate limiting is often IP-based

### 4. **Wait for Reset**
- Rate limits typically reset after **1-24 hours**
- Firebase automatically lifts the restriction

## 🔧 Advanced Solutions

### 5. **Check Firebase Console**
Visit these Firebase Console pages to check for issues:

1. **Authentication Overview:**
   https://console.firebase.google.com/project/h1c1-798a8/authentication

2. **Authentication Settings:**
   https://console.firebase.google.com/project/h1c1-798a8/authentication/settings

3. **Usage & Billing:**
   https://console.firebase.google.com/project/h1c1-798a8/usage

### 6. **Verify Email Domain Settings**
In Firebase Console > Authentication > Settings:
- Check **Authorized domains** includes your domain
- Verify **Email verification** is properly configured

### 7. **Check for Security Alerts**
In Firebase Console, look for any security alerts or unusual activity notifications.

## 🛠️ Code-Level Fixes

### Add Better Error Handling
The current auth code should handle rate limiting gracefully:

```typescript
// In your auth functions, add specific rate limit handling
catch (error: any) {
  if (error.code === 'auth/too-many-requests') {
    throw new Error('Too many failed attempts. Please try again later or use a different network.');
  }
  throw error;
}
```

### Add Retry Logic with Exponential Backoff
```typescript
const retryWithBackoff = async (fn: () => Promise<any>, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error: any) {
      if (error.code === 'auth/too-many-requests' && i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        continue;
      }
      throw error;
    }
  }
};
```

## 🔍 Debugging Steps

### Check Network Tab
1. Open Developer Tools > Network tab
2. Try to create an account
3. Look for failed requests to:
   - `identitytoolkit.googleapis.com/v1/accounts:signUp`
   - `identitytoolkit.googleapis.com/v1/accounts:sendOobCode`

### Check Console Errors
Look for additional error details in the browser console.

## 📞 If Nothing Works

### Contact Firebase Support
If the issue persists after 24 hours:
1. Go to Firebase Console > Support
2. Report the rate limiting issue
3. Provide your project ID: `h1c1-798a8`

### Temporary Workaround
- Use Firebase Admin SDK to create test accounts
- Deploy a temporary admin function to create users server-side

## ✅ Prevention for Future

1. **Implement proper error handling** for auth failures
2. **Add user feedback** for rate limiting scenarios
3. **Use exponential backoff** for retries
4. **Monitor authentication metrics** in Firebase Console
5. **Set up alerts** for unusual authentication activity

## 🎯 Quick Test

After trying the solutions above, test with:
1. A valid .edu email address
2. Strong password (8+ characters)
3. In incognito mode
4. From a different network if possible

The rate limiting should resolve within 24 hours maximum.
