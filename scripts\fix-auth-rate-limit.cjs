const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp({
  projectId: 'h1c1-798a8'
});

async function fixAuthRateLimit() {
  console.log('🔧 Attempting to resolve Firebase Auth rate limiting...');
  
  try {
    // Get current project configuration
    const auth = admin.auth();
    
    // List current users to check if auth is working
    console.log('📊 Checking current authentication status...');
    const listUsersResult = await auth.listUsers(1);
    console.log(`✅ Authentication service is responding. Found ${listUsersResult.users.length} users.`);
    
    console.log('\n🔍 Rate limiting troubleshooting steps:');
    console.log('1. ✅ Firebase Admin SDK is working correctly');
    console.log('2. ⚠️  Rate limiting is likely from client-side requests');
    console.log('3. 💡 Try the following solutions:');
    console.log('   - Wait 1-24 hours for rate limit to reset');
    console.log('   - Try from a different IP address/network');
    console.log('   - Use incognito/private browsing mode');
    console.log('   - Clear browser cache and cookies');
    console.log('   - Check Firebase Console for any security alerts');
    
    console.log('\n🌐 Firebase Console Links:');
    console.log('   - Authentication: https://console.firebase.google.com/project/h1c1-798a8/authentication');
    console.log('   - Settings: https://console.firebase.google.com/project/h1c1-798a8/authentication/settings');
    console.log('   - Usage: https://console.firebase.google.com/project/h1c1-798a8/usage');
    
  } catch (error) {
    console.error('❌ Error checking authentication:', error.message);
    
    if (error.code === 'auth/project-not-found') {
      console.log('💡 Make sure you are logged into Firebase CLI with the correct account');
      console.log('   Run: firebase login');
    }
  }
}

fixAuthRateLimit();
