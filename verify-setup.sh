#!/bin/bash

# Hive Campus Setup Verification Script
set -e

echo "🔍 Verifying Hive Campus setup..."

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

ERRORS=0

# Check Node.js version
echo -e "${BLUE}Checking Node.js version...${NC}"
if command -v node &> /dev/null; then
  NODE_VERSION=$(node --version)
  echo -e "${GREEN}✅ Node.js version: $NODE_VERSION${NC}"
else
  echo -e "${RED}❌ Node.js is not installed${NC}"
  ERRORS=$((ERRORS + 1))
fi

# Check npm
echo -e "${BLUE}Checking npm...${NC}"
if command -v npm &> /dev/null; then
  NPM_VERSION=$(npm --version)
  echo -e "${GREEN}✅ npm version: $NPM_VERSION${NC}"
else
  echo -e "${RED}❌ npm is not available${NC}"
  ERRORS=$((ERRORS + 1))
fi

# Check Firebase CLI
echo -e "${BLUE}Checking Firebase CLI...${NC}"
if command -v firebase &> /dev/null; then
  FIREBASE_VERSION=$(firebase --version)
  echo -e "${GREEN}✅ Firebase CLI: $FIREBASE_VERSION${NC}"
else
  echo -e "${RED}❌ Firebase CLI is not installed${NC}"
  echo "Install with: npm install -g firebase-tools"
  ERRORS=$((ERRORS + 1))
fi

# Check dependencies
echo -e "${BLUE}Checking project dependencies...${NC}"
if [ -f "package.json" ] && [ -d "node_modules" ]; then
  echo -e "${GREEN}✅ Frontend dependencies installed${NC}"
else
  echo -e "${RED}❌ Frontend dependencies missing${NC}"
  echo "Run: npm install"
  ERRORS=$((ERRORS + 1))
fi

if [ -f "functions/package.json" ] && [ -d "functions/node_modules" ]; then
  echo -e "${GREEN}✅ Functions dependencies installed${NC}"
else
  echo -e "${RED}❌ Functions dependencies missing${NC}"
  echo "Run: cd functions && npm install"
  ERRORS=$((ERRORS + 1))
fi

# Check TypeScript compilation
echo -e "${BLUE}Checking TypeScript compilation...${NC}"
cd functions
if npm run build > /dev/null 2>&1; then
  echo -e "${GREEN}✅ Functions TypeScript compilation successful${NC}"
else
  echo -e "${RED}❌ Functions TypeScript compilation failed${NC}"
  ERRORS=$((ERRORS + 1))
fi
cd ..

# Check frontend build
echo -e "${BLUE}Checking frontend build...${NC}"
if npm run build > /dev/null 2>&1; then
  echo -e "${GREEN}✅ Frontend build successful${NC}"
else
  echo -e "${RED}❌ Frontend build failed${NC}"
  ERRORS=$((ERRORS + 1))
fi

# Check configuration files
echo -e "${BLUE}Checking configuration files...${NC}"

CONFIG_FILES=(
  "firebase.json"
  "firestore.rules"
  "storage.rules"
  "functions/src/config/environment.ts"
  ".github/workflows/deploy.yml"
  ".github/workflows/staging.yml"
)

for file in "${CONFIG_FILES[@]}"; do
  if [ -f "$file" ]; then
    echo -e "${GREEN}✅ $file exists${NC}"
  else
    echo -e "${RED}❌ $file missing${NC}"
    ERRORS=$((ERRORS + 1))
  fi
done

# Check environment configuration
echo -e "${BLUE}Checking environment configuration...${NC}"
if [ -f "functions/.env.example" ]; then
  echo -e "${GREEN}✅ Environment template exists${NC}"
else
  echo -e "${RED}❌ Environment template missing${NC}"
  ERRORS=$((ERRORS + 1))
fi

# Summary
echo ""
echo "======================="
echo "  VERIFICATION SUMMARY"
echo "======================="

if [ $ERRORS -eq 0 ]; then
  echo -e "${GREEN}🎉 All checks passed! Your setup is ready for deployment.${NC}"
  echo ""
  echo -e "${BLUE}Next steps:${NC}"
  echo "1. Set up your Firebase project: ./setup-production.sh"
  echo "2. Configure environment variables"
  echo "3. Deploy: ./deploy.sh --environment production"
else
  echo -e "${RED}❌ Found $ERRORS error(s). Please fix the issues above before deploying.${NC}"
  exit 1
fi

echo ""
echo -e "${YELLOW}🚀 Ready for production deployment!${NC}"