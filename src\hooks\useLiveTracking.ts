import { useState, useEffect } from 'react';
import { doc, onSnapshot, updateDoc, Timestamp } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { Order } from '../firebase/types';
import { logOrderDelivered } from '../utils/adminLogger';

export type TrackingStatus = 
  | 'label_created'
  | 'in_transit'
  | 'out_for_delivery'
  | 'delivered'
  | 'exception';

export interface TrackingUpdate {
  status: TrackingStatus;
  location?: string;
  timestamp: Date;
  description: string;
}

export interface UseLiveTrackingReturn {
  order: Order | null;
  trackingUpdates: TrackingUpdate[];
  isLoading: boolean;
  error: string | null;
  simulateDelivery: () => Promise<void>;
  updateTrackingStatus: (status: TrackingStatus, description: string, location?: string) => Promise<void>;
}

/**
 * Hook for live order tracking with real-time updates
 */
export const useLiveTracking = (orderId: string): UseLiveTrackingReturn => {
  const [order, setOrder] = useState<Order | null>(null);
  const [trackingUpdates, setTrackingUpdates] = useState<TrackingUpdate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!orderId) {
      setIsLoading(false);
      return;
    }

    // Check if this is a mock order for testing
    if (orderId.startsWith('order_test_')) {
      const mockOrderData = localStorage.getItem(`mock_order_${orderId}`);
      if (mockOrderData) {
        try {
          const mockOrder = JSON.parse(mockOrderData);
          setOrder(mockOrder);

          // Generate mock tracking updates
          const mockUpdates: TrackingUpdate[] = [
            {
              status: 'label_created',
              timestamp: new Date(mockOrder.createdAt),
              description: 'Shipping label created',
              location: 'Origin Facility'
            },
            {
              status: 'in_transit',
              timestamp: new Date(Date.now() + 1000 * 60 * 30), // 30 minutes later
              description: 'Package in transit',
              location: 'Distribution Center'
            },
            {
              status: 'out_for_delivery',
              timestamp: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1 day later
              description: 'Out for delivery',
              location: 'Local Delivery Facility'
            }
          ];

          setTrackingUpdates(mockUpdates);
          setIsLoading(false);
          return;
        } catch (error) {
          console.error('Error parsing mock order:', error);
          setError('Invalid mock order data');
          setIsLoading(false);
          return;
        }
      } else {
        setError('Mock order not found');
        setIsLoading(false);
        return;
      }
    }

    const orderRef = doc(firestore, 'orders', orderId);

    const unsubscribe = onSnapshot(
      orderRef,
      (doc) => {
        if (doc.exists()) {
          const orderData = { id: doc.id, ...doc.data() } as Order;
          setOrder(orderData);
          
          // Generate tracking updates based on order status
          generateTrackingUpdates(orderData);
        } else {
          setError('Order not found');
        }
        setIsLoading(false);
      },
      (error) => {
        console.error('Error listening to order updates:', error);
        setError('Failed to load order tracking');
        setIsLoading(false);
      }
    );

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, [orderId]);

  /**
   * Generate tracking updates based on order status
   */
  const generateTrackingUpdates = (orderData: Order) => {
    const updates: TrackingUpdate[] = [];
    
    // Order created
    updates.push({
      status: 'label_created',
      timestamp: orderData.createdAt.toDate(),
      description: 'Order confirmed and shipping label created'
    });

    // If shipped
    if (orderData.status === 'shipped_pending_code' || 
        orderData.status === 'delivered' || 
        orderData.status === 'completed') {
      updates.push({
        status: 'in_transit',
        timestamp: orderData.trackingInfo?.shippedDate?.toDate() || new Date(),
        description: 'Package is in transit'
      });
    }

    // If delivered
    if (orderData.status === 'delivered' || orderData.status === 'completed') {
      updates.push({
        status: 'delivered',
        timestamp: orderData.deliveredAt?.toDate() || new Date(),
        description: 'Package has been delivered'
      });
    }

    setTrackingUpdates(updates);
  };

  /**
   * Update tracking status (for testing/simulation)
   */
  const updateTrackingStatus = async (
    status: TrackingStatus, 
    description: string, 
    location?: string
  ): Promise<void> => {
    if (!order) return;

    try {
      const orderRef = doc(firestore, 'orders', orderId);
      const updateData: any = {
        updatedAt: Timestamp.now()
      };

      // Update tracking info
      updateData.trackingInfo = {
        ...order.trackingInfo,
        status,
        lastUpdate: Timestamp.now()
      };

      // If delivered, update order status and set auto-release date
      if (status === 'delivered') {
        updateData.status = 'delivered';
        updateData.deliveredAt = Timestamp.now();
        updateData.autoReleaseDate = Timestamp.fromDate(
          new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 72 hours
        );

        // Log delivery
        await logOrderDelivered(orderId, order.buyerId, {
          trackingNumber: order.trackingInfo?.trackingNumber,
          carrier: order.trackingInfo?.carrier,
          location
        });
      }

      await updateDoc(orderRef, updateData);
    } catch (error) {
      console.error('Error updating tracking status:', error);
      setError('Failed to update tracking status');
    }
  };

  /**
   * Simulate delivery for testing
   */
  const simulateDelivery = async (): Promise<void> => {
    if (!order) return;

    // Handle mock orders differently
    if (order.id.startsWith('order_test_')) {
      // Update mock order in localStorage
      const updatedOrder = {
        ...order,
        status: 'delivered',
        deliveredAt: new Date().toISOString(),
        autoReleaseDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      };

      localStorage.setItem(`mock_order_${order.id}`, JSON.stringify(updatedOrder));
      setOrder(updatedOrder);

      // Add delivery tracking update
      const deliveryUpdate: TrackingUpdate = {
        status: 'delivered',
        timestamp: new Date(),
        description: 'Package delivered to recipient',
        location: 'Front door'
      };

      setTrackingUpdates(prev => [...prev, deliveryUpdate]);
      return;
    }

    // For real orders, use the existing function
    await updateTrackingStatus(
      'delivered',
      'Package delivered to recipient',
      'Front door'
    );
  };

  return {
    order,
    trackingUpdates,
    isLoading,
    error,
    simulateDelivery,
    updateTrackingStatus
  };
};
