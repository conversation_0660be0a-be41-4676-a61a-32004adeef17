import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { 
  canEditProfile, 
  canEditListing, 
  canDeleteListing, 
  canViewOrder,
  canUpdateOrder,
  canViewChat,
  canSendMessage,
  isAdmin,
  isMerchant,
  isStudent,
  getPermissionErrorMessage
} from '../utils/permissions';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission: 'editProfile' | 'editListing' | 'deleteListing' | 'viewOrder' | 'updateOrder' | 'viewChat' | 'sendMessage' | 'admin' | 'merchant' | 'student';
  targetUserId?: string;
  resourceOwnerId?: string;
  buyerId?: string;
  sellerId?: string;
  participants?: string[];
  fallback?: React.ReactNode;
  showError?: boolean;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  targetUserId,
  resourceOwnerId,
  buyerId,
  sellerId,
  participants,
  fallback,
  showError = true
}) => {
  const { currentUser } = useAuth();

  const checkPermission = (): boolean => {
    if (!currentUser) {
      return false;
    }

    const context = {
      currentUser,
      targetUserId,
      resourceOwnerId,
      buyerId,
      sellerId,
      participants
    };

    switch (permission) {
      case 'editProfile':
        return canEditProfile(context);
      case 'editListing':
        return canEditListing(context);
      case 'deleteListing':
        return canDeleteListing(context);
      case 'viewOrder':
        return canViewOrder(context);
      case 'updateOrder':
        return canUpdateOrder(context);
      case 'viewChat':
        return canViewChat(context);
      case 'sendMessage':
        return canSendMessage(context);
      case 'admin':
        return isAdmin(currentUser);
      case 'merchant':
        return isMerchant(currentUser);
      case 'student':
        return isStudent(currentUser);
      default:
        return false;
    }
  };

  const hasPermission = checkPermission();

  if (!hasPermission) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showError) {
      return (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 text-red-600 dark:text-red-400">⚠️</div>
            <p className="text-sm text-red-800 dark:text-red-200">
              {getPermissionErrorMessage(`perform this action`)}
            </p>
          </div>
        </div>
      );
    }

    return null;
  }

  return <>{children}</>;
};

export default PermissionGuard;


