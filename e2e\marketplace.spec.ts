import { test, expect } from '@playwright/test'

test.describe('Marketplace Core Features', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display homepage with proper structure', async ({ page }) => {
    // Navigate to home page (might need auth first)
    if (await page.locator('text=Sign In').isVisible()) {
      // Skip auth for now and test basic structure
      await page.goto('/home')
    }
    
    // Should have main content areas
    await expect(page.locator('main')).toBeVisible()
    
    // Should have navigation
    await expect(page.locator('nav')).toBeVisible()
  })

  test('should handle search functionality', async ({ page }) => {
    await page.goto('/home')
    
    // Look for search input
    const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]').first()
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('laptop')
      await page.keyboard.press('Enter')
      
      // Should show search results or no results message
      await expect(page.locator('text=results').or(page.locator('text=No results'))).toBeVisible()
    }
  })

  test('should navigate to add listing page', async ({ page }) => {
    // Try to navigate to add listing
    await page.goto('/add-listing')
    
    // Should either show the form or redirect to login
    await expect(page.locator('text=Sign In').or(page.locator('form'))).toBeVisible()
  })

  test('should handle category filtering', async ({ page }) => {
    await page.goto('/home')
    
    // Look for category filters
    const categoryFilter = page.locator('button:has-text("Electronics"), button:has-text("Books"), button:has-text("Furniture")').first()
    
    if (await categoryFilter.isVisible()) {
      await categoryFilter.click()
      
      // Should filter listings
      await page.waitForTimeout(1000) // Wait for filter to apply
    }
  })
})

test.describe('Listing Management', () => {
  test('should show validation errors for empty listing form', async ({ page }) => {
    await page.goto('/add-listing')
    
    // If redirected to login, just test the redirect
    if (await page.locator('text=Sign In').isVisible()) {
      expect(page.url()).toContain('login')
      return
    }
    
    // Try to submit empty form
    const submitButton = page.locator('button[type="submit"], button:has-text("Create Listing"), button:has-text("Post Listing")').first()
    
    if (await submitButton.isVisible()) {
      await submitButton.click()
      
      // Should show validation errors
      await expect(page.locator('text=required').or(page.locator('text=Please enter'))).toBeVisible()
    }
  })

  test('should handle image upload validation', async ({ page }) => {
    await page.goto('/add-listing')
    
    // If form is available, test image upload area
    const fileInput = page.locator('input[type="file"]')
    
    if (await fileInput.isVisible()) {
      // Should show upload area
      await expect(page.locator('text=upload').or(page.locator('text=drag'))).toBeVisible()
    }
  })
})

test.describe('User Profile Features', () => {
  test('should navigate to profile page', async ({ page }) => {
    await page.goto('/profile')
    
    // Should either show profile or redirect to login
    await expect(page.locator('text=Sign In').or(page.locator('text=Profile'))).toBeVisible()
  })

  test('should handle wallet page access', async ({ page }) => {
    await page.goto('/wallet')
    
    // Should either show wallet or redirect to login
    await expect(page.locator('text=Sign In').or(page.locator('text=Wallet'))).toBeVisible()
  })
})

test.describe('Messaging System', () => {
  test('should handle messages page access', async ({ page }) => {
    await page.goto('/messages')
    
    // Should either show messages or redirect to login
    await expect(page.locator('text=Sign In').or(page.locator('text=Messages'))).toBeVisible()
  })
})

test.describe('Admin Features', () => {
  test('should restrict admin dashboard access', async ({ page }) => {
    await page.goto('/admin/dashboard-new')
    
    // Should redirect to login or show access denied
    await expect(page.locator('text=Sign In').or(page.locator('text=Access Denied'))).toBeVisible()
  })

  test('should restrict admin user management', async ({ page }) => {
    await page.goto('/admin/users')
    
    // Should redirect to login or show access denied
    await expect(page.locator('text=Sign In').or(page.locator('text=Access Denied'))).toBeVisible()
  })
})

test.describe('Merchant Features', () => {
  test('should restrict merchant dashboard access', async ({ page }) => {
    await page.goto('/merchant/dashboard')
    
    // Should redirect to login or show access denied
    await expect(page.locator('text=Sign In').or(page.locator('text=Access Denied'))).toBeVisible()
  })

  test('should handle merchant directory', async ({ page }) => {
    await page.goto('/merchant/directory')
    
    // Merchant directory might be publicly accessible
    await expect(page.locator('body')).toBeVisible()
  })
})

test.describe('Performance and Loading', () => {
  test('should load pages within acceptable time', async ({ page }) => {
    const startTime = Date.now()
    await page.goto('/home')
    const loadTime = Date.now() - startTime
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000)
  })

  test('should show loading states appropriately', async ({ page }) => {
    await page.goto('/home')
    
    // Should handle loading states gracefully
    await page.waitForLoadState('networkidle')
    
    // Page should be fully loaded
    await expect(page.locator('body')).toBeVisible()
  })

  test('should handle slow network conditions', async ({ page }) => {
    // Simulate slow 3G
    await page.route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, 100))
      await route.continue()
    })
    
    await page.goto('/home')
    
    // Should still load successfully
    await expect(page.locator('main')).toBeVisible({ timeout: 10000 })
  })
})