import React from 'react';

interface AnimatedCardProps {
  children: React.ReactNode;
  variant?: 'gradient' | 'layered';
  className?: string;
}

const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  variant = 'gradient',
  className = ''
}) => {
  if (variant === 'layered') {
    return (
      <div 
        className={`animated-card-layered ${className}`}
        style={{
          background: 'linear-gradient(to right, #74ebd5 0%, #acb6e5 100%)',
          width: '100%',
          minHeight: '254px',
          padding: '5px',
          borderRadius: '1rem',
          overflow: 'visible',
          position: 'relative',
          zIndex: 1
        }}
      >
        <div 
          className="card-before"
          style={{
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            borderRadius: '1rem',
            zIndex: -1,
            background: 'linear-gradient(to bottom right, #f6d365 0%, #fda085 100%)',
            transform: 'rotate(2deg)'
          }}
        />
        <div 
          className="card-after"
          style={{
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            borderRadius: '1rem',
            zIndex: -1,
            background: 'linear-gradient(to top right, #84fab0 0%, #8fd3f4 100%)',
            transform: 'rotate(-2deg)'
          }}
        />
        <div 
          className="card-info"
          style={{
            background: '#292b2c',
            color: '#292b2c',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '100%',
            overflow: 'visible',
            borderRadius: '0.7rem',
            position: 'relative',
            zIndex: 2,
            transition: 'color 1s'
          }}
        >
          <div className="w-full h-full p-6">
            {children}
          </div>
        </div>
      </div>
    );
  }

  // Default gradient variant
  return (
    <div 
      className={`animated-card-gradient ${className}`}
      style={{
        width: '100%',
        minHeight: '254px',
        borderRadius: '1rem',
        backgroundColor: '#4158D0',
        backgroundImage: 'linear-gradient(43deg, #4158D0 0%, #C850C0 46%, #FFCC70 100%)',
        boxShadow: `
          rgba(0, 0, 0, 0.17) 0px -23px 25px 0px inset, 
          rgba(0, 0, 0, 0.15) 0px -36px 30px 0px inset, 
          rgba(0, 0, 0, 0.1) 0px -79px 40px 0px inset, 
          rgba(0, 0, 0, 0.06) 0px 2px 1px, 
          rgba(0, 0, 0, 0.09) 0px 4px 2px, 
          rgba(0, 0, 0, 0.09) 0px 8px 4px, 
          rgba(0, 0, 0, 0.09) 0px 16px 8px, 
          rgba(0, 0, 0, 0.09) 0px 32px 16px
        `,
        padding: '5px',
        position: 'relative'
      }}
    >
      <div 
        style={{
          background: 'white',
          borderRadius: '0.7rem',
          width: '100%',
          height: '100%',
          padding: '1.5rem',
          minHeight: '244px'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default AnimatedCard;
