import React, { useState, useEffect } from 'react';
import { Star, MapPin, Calendar, Grid, List, GraduationCap, BookOpen, MessageCircle } from 'lucide-react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { User } from '../firebase/types';
import { createOrGetChat } from '../firebase/messages';

const PublicProfile: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isStartingChat, setIsStartingChat] = useState(false);

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!userId) return;
      
      try {
        setIsLoading(true);
        const userDoc = await getDoc(doc(db, 'users', userId));
        
        if (userDoc.exists()) {
          setProfileUser({ uid: userId, ...userDoc.data() } as User);
        } else {
          setError('User not found');
        }
      } catch (err) {
        console.error('Error fetching user profile:', err);
        setError('Failed to load profile');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [userId]);

  // Handle starting a chat with this user
  const handleStartChat = async () => {
    if (!currentUser || !userId) return;

    setIsStartingChat(true);
    try {
      const result = await createOrGetChat(userId);
      if (result && result.success) {
        // Navigate to messages with the chat selected
        navigate('/messages', {
          state: {
            chatId: result.data.chatId,
            otherUserId: userId
          }
        });
      }
    } catch (error) {
      console.error('Error starting chat:', error);
      // You could show a toast notification here
    } finally {
      setIsStartingChat(false);
    }
  };

  // Redirect to own profile if viewing self
  if (currentUser && userId === currentUser.uid) {
    window.location.href = '/profile';
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-16 h-16 bg-primary-200 dark:bg-primary-700 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !profileUser) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Profile Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400">{error || 'This user profile does not exist.'}</p>
        </div>
      </div>
    );
  }

  const user = {
    name: profileUser.name || 'User',
    username: `@${profileUser.email?.split('@')[0] || 'user'}`,
    avatar: profileUser.profilePictureURL || '/placeholder-avatar.svg',
    rating: 4.9, // This would come from reviews/ratings system
    reviewCount: 127, // This would come from reviews/ratings system
    joinedDate: profileUser.createdAt ? new Date(profileUser.createdAt.toDate()).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Recently',
    location: profileUser.university || 'University',
    bio: profileUser.bio || 'No bio available',
    graduationYear: profileUser.graduationYear,
    major: profileUser.major,
  };

  // TODO: Fetch user's active listings from Firebase
  const listings: any[] = [];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Header */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="relative">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-32 h-32 rounded-2xl object-cover"
              />
            </div>

            {/* User Info */}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-4">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{user.name}</h1>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    <span className="font-semibold text-gray-900 dark:text-white">{user.rating}</span>
                    <span className="text-gray-500 dark:text-gray-400">({user.reviewCount} reviews)</span>
                  </div>
                  {/* Chat Button */}
                  {currentUser && userId !== currentUser.uid && (
                    <button
                      onClick={handleStartChat}
                      disabled={isStartingChat}
                      className="flex items-center space-x-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                    >
                      <MessageCircle className="w-4 h-4" />
                      <span>{isStartingChat ? 'Starting...' : 'Chat'}</span>
                    </button>
                  )}
                </div>
              </div>
              
              <p className="text-gray-600 dark:text-gray-400 mb-4">{user.username}</p>
              
              <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mb-4">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span>{user.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>Joined {user.joinedDate}</span>
                </div>
                {user.graduationYear && (
                  <div className="flex items-center space-x-2">
                    <GraduationCap className="w-4 h-4" />
                    <span>Class of {user.graduationYear}</span>
                  </div>
                )}
                {user.major && (
                  <div className="flex items-center space-x-2">
                    <BookOpen className="w-4 h-4" />
                    <span>{user.major}</span>
                  </div>
                )}
              </div>
              
              <p className="text-gray-700 dark:text-gray-300 mb-6">{user.bio}</p>
            </div>
          </div>
        </div>

        {/* Active Listings */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Active Listings</h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg ${
                  viewMode === 'grid' 
                    ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' 
                    : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg ${
                  viewMode === 'list' 
                    ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' 
                    : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>

          {listings.length > 0 ? (
            viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {listings.map((listing) => (
                  <Link key={listing.id} to={`/listing/${listing.id}`}>
                    <div className="group cursor-pointer">
                      <div className="relative aspect-square rounded-xl overflow-hidden mb-3">
                        <img
                          src={listing.image}
                          alt={listing.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                        <div className="absolute top-3 left-3">
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-700">
                            {listing.status}
                          </span>
                        </div>
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-1">{listing.title}</h3>
                      <p className="text-xl font-bold text-primary-600 dark:text-primary-400 mb-2">${listing.price}</p>
                      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                        <span>{listing.views} views</span>
                        <span>{listing.likes} likes</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {listings.map((listing) => (
                  <Link key={listing.id} to={`/listing/${listing.id}`}>
                    <div className="flex items-center space-x-4 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <img
                        src={listing.image}
                        alt={listing.title}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 dark:text-white">{listing.title}</h3>
                        <p className="text-lg font-bold text-primary-600 dark:text-primary-400">${listing.price}</p>
                      </div>
                      <div className="text-right text-sm text-gray-500 dark:text-gray-400">
                        <div>{listing.views} views</div>
                        <div>{listing.likes} likes</div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No active listings</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PublicProfile;
