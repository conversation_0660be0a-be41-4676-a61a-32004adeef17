# Hive Campus Wallet Credit System

## Overview

The Hive Campus Wallet Credit System is a comprehensive in-app credit system that allows users to earn and spend promotional credits within the platform. The system is designed with security and user experience in mind, ensuring that credits are non-withdrawable and can only be used for purchases.

## Key Features

### 🎁 Promotional Credits
- **Signup Bonus**: New users receive $5 upon account creation
- **Referral System**: Both referrer and new user earn $5 when referral code is used
- **Admin Grants**: Administrators can manually grant credits to users
- **Cashback**: Users earn cashback on purchases (configurable rates)

### 🔒 Security Features
- **Non-withdrawable**: Credits can only be used for purchases, not withdrawn as cash
- **Post-payment deduction**: Credits are only deducted after successful Stripe payment
- **Admin-only balance modification**: Users cannot directly modify their wallet balance
- **Transaction history**: Complete audit trail of all credit transactions

### 💳 Checkout Integration
- **Smart application**: Credits are applied first, remaining balance charged via Stripe
- **Real-time calculation**: Dynamic pricing updates based on credit usage
- **Minimum charge compliance**: Ensures Stripe minimum charge requirements are met
- **Transparent UI**: Clear display of credit application and savings

## Technical Implementation

### Database Structure

#### Firestore Collections

**`/wallets/{userId}`**
```typescript
{
  userId: string;
  balance: number;
  referralCode: string;
  usedReferral: boolean;
  history: WalletTransaction[];
  grantedBy: 'admin' | 'referral' | 'signup' | 'cashback';
  lastUpdated: Timestamp;
  createdAt: Timestamp;
}
```

**`/referralCodes/{code}`**
```typescript
{
  code: string;
  userId: string;
  usedBy: string[];
  totalRewards: number;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}
```

**Transaction History Structure**
```typescript
{
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  source: 'admin_grant' | 'referral_bonus' | 'signup_bonus' | 'cashback' | 'purchase_deduction';
  orderId?: string;
  grantedBy?: string;
  referralUserId?: string;
  createdAt: Timestamp;
}
```

### Security Rules

```javascript
// Wallets collection - users can only read, admins can write
match /wallets/{userId} {
  allow read: if isAuthenticated() && request.auth.uid == userId;
  allow write: if isAdmin();
}

// Referral codes - readable by all authenticated users
match /referralCodes/{codeId} {
  allow read: if isAuthenticated();
  allow write: if isAdmin();
}
```

### Cloud Functions

#### Core Functions
- `initializeWallet`: Creates wallet with signup bonus on user creation
- `processReferralCode`: Handles referral code validation and bonus distribution
- `grantWalletCredit`: Admin function to manually grant credits
- `getWalletData`: Retrieves wallet information for authenticated user
- `validateReferralCode`: Validates referral codes during signup

#### Integration Points
- **Stripe Webhook**: Deducts wallet credit after successful payment
- **User Creation**: Automatically initializes wallet with signup bonus
- **Admin Panel**: Provides wallet management interface

### Frontend Components

#### Signup Process
- Referral code input field with real-time validation
- Visual feedback for valid/invalid codes
- Automatic bonus processing after successful signup

#### Checkout Flow
- Wallet balance display
- Credit application toggle
- Dynamic price calculation
- Clear savings indication

#### Admin Panel
- User wallet management
- Credit granting interface
- Transaction history viewing
- Bulk operations support

## Usage Examples

### User Signup with Referral
```typescript
// User enters referral code during signup
const validation = await validateReferralCode('JOHN123');
if (validation.valid) {
  // Show bonus information
  // Process referral after successful signup
  await processReferralCode('JOHN123');
}
```

### Checkout with Wallet Credit
```typescript
// Calculate pricing with wallet credit
const pricing = {
  subtotal: 25.00,
  walletCredit: useWalletCredit ? Math.min(walletBalance, 25.00) : 0,
  total: 25.00 - walletCredit
};

// Create Stripe session with wallet credit flag
const session = await createCheckoutSession(listingId, useWalletCredit, orderDetails);
```

### Admin Credit Grant
```typescript
// Admin grants credit to user
await grantWalletCredit({
  userId: 'user123',
  amount: 10.00,
  description: 'Customer service credit'
});
```

## Testing Scenarios

### 1. Signup Bonus Test
- Create new user account
- Verify $5 signup bonus is added to wallet
- Check transaction history for signup bonus record

### 2. Referral System Test
- User A shares referral code
- User B signs up with referral code
- Verify both users receive $5 bonus
- Check referral code usage tracking

### 3. Checkout Integration Test
- User with $10 wallet balance
- Purchase $25 item with wallet credit enabled
- Verify $10 credit applied, $15 charged to Stripe
- Confirm wallet balance deducted only after successful payment

### 4. Admin Management Test
- Admin grants $20 credit to user
- Verify credit appears in user wallet
- Check admin action logging

### 5. Edge Cases
- Wallet covers entire purchase amount
- Failed payment scenarios
- Concurrent wallet operations
- Invalid referral codes

## Configuration

### Environment Variables
```
STRIPE_MINIMUM_CHARGE_AMOUNT=0.50
SIGNUP_BONUS_AMOUNT=5.00
REFERRAL_BONUS_AMOUNT=5.00
CASHBACK_RATE_TEXTBOOKS=0.02
CASHBACK_RATE_OTHER=0.02
```

### Commission Rates
- Textbooks/Course Materials: 8% (including Stripe fees)
- Other Categories: 10% (including Stripe fees)

## Monitoring and Analytics

### Key Metrics
- Total wallet credits issued
- Credit redemption rates
- Referral program effectiveness
- Average wallet balance per user
- Credit-to-purchase conversion rates

### Logging
- All wallet transactions logged with full audit trail
- Admin actions tracked and logged
- Failed operations logged for debugging
- Performance metrics for wallet operations

## Future Enhancements

### Planned Features
- Wallet credit expiration dates
- Tiered referral bonuses
- Seasonal promotional campaigns
- Wallet credit gifting between users
- Advanced analytics dashboard

### Scalability Considerations
- Batch processing for large credit operations
- Caching for frequently accessed wallet data
- Rate limiting for wallet operations
- Automated fraud detection

## Support and Troubleshooting

### Common Issues
1. **Wallet not initialized**: Check user creation trigger function
2. **Credit not applied**: Verify Stripe webhook processing
3. **Referral not working**: Check referral code validation logic
4. **Admin grants failing**: Verify admin permissions

### Debug Tools
- Wallet transaction history viewer
- Admin action logs
- Stripe webhook event logs
- Real-time wallet balance monitoring

## Security Best Practices

1. **Never expose wallet balance modification endpoints to users**
2. **Always validate admin permissions for credit grants**
3. **Use atomic transactions for wallet operations**
4. **Implement rate limiting on wallet functions**
5. **Log all wallet operations for audit purposes**
6. **Validate all input amounts and user IDs**
7. **Use secure random generation for referral codes**

---

For technical support or questions about the wallet system, contact the development team or refer to the Firebase Functions logs for detailed error information.
