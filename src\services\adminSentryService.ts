import * as Sentry from '@sentry/react';

export interface SentryError {
  id: string;
  title: string;
  message: string;
  level: 'error' | 'warning' | 'info' | 'debug';
  timestamp: Date;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
  tags?: Record<string, string>;
  extra?: Record<string, any>;
  fingerprint?: string[];
  platform?: string;
  environment?: string;
  release?: string;
  count?: number;
  firstSeen?: Date;
  lastSeen?: Date;
  status?: 'unresolved' | 'resolved' | 'ignored';
}

export interface SentryMetrics {
  totalErrors: number;
  newErrors: number;
  resolvedErrors: number;
  errorRate: number;
  affectedUsers: number;
  topErrors: SentryError[];
  errorsByLevel: Record<string, number>;
  errorsByEnvironment: Record<string, number>;
}

export interface SentryProject {
  id: string;
  name: string;
  slug: string;
  platform: string;
  status: string;
}

/**
 * Admin Sentry Service for error monitoring and management
 */
export class AdminSentryService {
  private static readonly SENTRY_API_BASE = 'https://sentry.io/api/0';
  private static readonly SENTRY_ORG = process.env.VITE_SENTRY_ORG || 'hive-campus';
  private static readonly SENTRY_PROJECT = process.env.VITE_SENTRY_PROJECT || 'hive-campus-web';
  private static readonly SENTRY_AUTH_TOKEN = process.env.VITE_SENTRY_AUTH_TOKEN;

  /**
   * Get Sentry error metrics for admin dashboard
   */
  static async getSentryMetrics(_days: number = 7): Promise<SentryMetrics> {
    try {
      // For now, return mock data since we need Sentry API integration
      // In production, this would make actual API calls to Sentry
      
      const mockMetrics: SentryMetrics = {
        totalErrors: 42,
        newErrors: 8,
        resolvedErrors: 15,
        errorRate: 0.02, // 2%
        affectedUsers: 12,
        topErrors: [
          {
            id: 'error-1',
            title: 'TypeError: Cannot read property of undefined',
            message: 'Cannot read property \'map\' of undefined',
            level: 'error',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            count: 15,
            status: 'unresolved',
            environment: 'production'
          },
          {
            id: 'error-2',
            title: 'Network Error: Failed to fetch',
            message: 'Failed to fetch data from API endpoint',
            level: 'error',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
            count: 8,
            status: 'unresolved',
            environment: 'production'
          },
          {
            id: 'error-3',
            title: 'Authentication Error',
            message: 'User session expired',
            level: 'warning',
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
            count: 23,
            status: 'resolved',
            environment: 'production'
          }
        ],
        errorsByLevel: {
          error: 28,
          warning: 12,
          info: 2,
          debug: 0
        },
        errorsByEnvironment: {
          production: 35,
          staging: 5,
          development: 2
        }
      };

      return mockMetrics;
    } catch (error) {
      console.error('Error fetching Sentry metrics:', error);
      throw error;
    }
  }

  /**
   * Get recent errors from Sentry
   */
  static async getRecentErrors(limit: number = 50): Promise<SentryError[]> {
    try {
      // Mock data for now
      const mockErrors: SentryError[] = [
        {
          id: 'error-1',
          title: 'TypeError in UserProfile component',
          message: 'Cannot read property \'profilePictureURL\' of undefined',
          level: 'error',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          user: { id: 'user123', email: '<EMAIL>' },
          tags: { component: 'UserProfile', page: '/profile' },
          environment: 'production',
          count: 3,
          status: 'unresolved'
        },
        {
          id: 'error-2',
          title: 'API Request Failed',
          message: 'Failed to load listings: Network timeout',
          level: 'error',
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
          user: { id: 'user456', email: '<EMAIL>' },
          tags: { api: 'listings', method: 'GET' },
          environment: 'production',
          count: 1,
          status: 'unresolved'
        },
        {
          id: 'error-3',
          title: 'Payment Processing Warning',
          message: 'Stripe webhook received duplicate event',
          level: 'warning',
          timestamp: new Date(Date.now() - 60 * 60 * 1000),
          tags: { service: 'stripe', webhook: 'payment_intent.succeeded' },
          environment: 'production',
          count: 2,
          status: 'resolved'
        }
      ];

      return mockErrors.slice(0, limit);
    } catch (error) {
      console.error('Error fetching recent errors:', error);
      throw error;
    }
  }

  /**
   * Get error details by ID
   */
  static async getErrorDetails(errorId: string): Promise<SentryError | null> {
    try {
      // Mock implementation
      const errors = await this.getRecentErrors();
      return errors.find(error => error.id === errorId) || null;
    } catch (error) {
      console.error('Error fetching error details:', error);
      throw error;
    }
  }

  /**
   * Resolve an error
   */
  static async resolveError(errorId: string): Promise<void> {
    try {
      // In production, this would make an API call to Sentry
      console.log(`Resolving error: ${errorId}`);
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error resolving error:', error);
      throw error;
    }
  }

  /**
   * Ignore an error
   */
  static async ignoreError(errorId: string): Promise<void> {
    try {
      // In production, this would make an API call to Sentry
      console.log(`Ignoring error: ${errorId}`);
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error ignoring error:', error);
      throw error;
    }
  }

  /**
   * Get error trends over time
   */
  static async getErrorTrends(days: number = 7): Promise<Array<{
    date: string;
    errors: number;
    users: number;
  }>> {
    try {
      // Mock data for error trends
      const trends = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        trends.push({
          date: date.toISOString().split('T')[0],
          errors: Math.floor(Math.random() * 20) + 5,
          users: Math.floor(Math.random() * 10) + 2
        });
      }
      
      return trends;
    } catch (error) {
      console.error('Error fetching error trends:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics
   */
  static async getPerformanceMetrics(): Promise<{
    averageResponseTime: number;
    slowestTransactions: Array<{
      name: string;
      duration: number;
      timestamp: Date;
    }>;
    throughput: number;
    errorRate: number;
  }> {
    try {
      // Mock performance data
      return {
        averageResponseTime: 245, // ms
        slowestTransactions: [
          {
            name: '/api/listings',
            duration: 1250,
            timestamp: new Date(Date.now() - 30 * 60 * 1000)
          },
          {
            name: '/api/users/profile',
            duration: 890,
            timestamp: new Date(Date.now() - 45 * 60 * 1000)
          },
          {
            name: '/api/orders/create',
            duration: 750,
            timestamp: new Date(Date.now() - 60 * 60 * 1000)
          }
        ],
        throughput: 150, // requests per minute
        errorRate: 0.02 // 2%
      };
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      throw error;
    }
  }

  /**
   * Create a new release in Sentry
   */
  static async createRelease(version: string, environment: string): Promise<void> {
    try {
      // In production, this would create a release in Sentry
      console.log(`Creating release: ${version} for ${environment}`);
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error creating release:', error);
      throw error;
    }
  }

  /**
   * Set up real-time error monitoring
   */
  static setupRealTimeMonitoring(callback: (error: SentryError) => void): () => void {
    // In production, this would set up a WebSocket connection to Sentry
    // For now, simulate with periodic checks
    
    const interval = setInterval(async () => {
      try {
        const recentErrors = await this.getRecentErrors(5);
        const latestError = recentErrors[0];
        
        if (latestError && Date.now() - latestError.timestamp.getTime() < 60000) {
          callback(latestError);
        }
      } catch (error) {
        console.error('Error in real-time monitoring:', error);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }

  /**
   * Capture custom admin event
   */
  static captureAdminEvent(
    message: string,
    level: 'info' | 'warning' | 'error' = 'info',
    extra?: Record<string, any>
  ): void {
    Sentry.addBreadcrumb({
      message,
      level,
      data: extra,
      category: 'admin'
    });

    if (level === 'error') {
      Sentry.captureException(new Error(message));
    } else {
      Sentry.captureMessage(message, level);
    }
  }
}
