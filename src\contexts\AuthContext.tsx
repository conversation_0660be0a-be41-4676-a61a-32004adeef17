import React, { createContext, useState, useEffect } from 'react';
import { User } from 'firebase/auth';
import { auth, getUserProfile } from '../firebase';
import { User as AppUser, UserRole } from '../firebase/types';
// import { setSentryUser, captureTypedEvent, SentryEventType } from '../utils/sentry';

interface AuthContextType {
  currentUser: User | null;
  userProfile: AppUser | null;
  userRole: UserRole | null;
  isLoading: boolean;
  isAdmin: boolean;
  isMerchant: boolean;
  isStudent: boolean;
  refreshProfile: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType>({
  currentUser: null,
  userProfile: null,
  userRole: null,
  isLoading: true,
  isAdmin: false,
  isMerchant: false,
  isStudent: false,
  refreshProfile: async () => {}
});



export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<AppUser | null>(null);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Listen for auth state changes
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      // Allow all authenticated users to access the app (removed email verification requirement)
      if (user) {
        setCurrentUser(user);

        try {
          // First check for custom claims (for admin role)
          const tokenResult = await user.getIdTokenResult(true); // Force refresh
          const customRole = tokenResult.claims.role as string;

          if (customRole) {
            console.log('Using custom claims role:', customRole);
          }

          // Fetch user profile from Firestore with retry mechanism for new users
          let profileResult = await getUserProfile();
          let retryCount = 0;
          const maxRetries = 3;

          // Retry if profile not found (common for new users due to timing)
          while ((!profileResult || !profileResult.success) && retryCount < maxRetries) {
            console.log(`Profile not found, retrying... (${retryCount + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
            profileResult = await getUserProfile();
            retryCount++;
          }

          if (profileResult && profileResult.success) {
            const profile = profileResult.data as AppUser;
            setUserProfile(profile);

            // Use custom claims role if available, otherwise use profile role
            const finalRole = customRole || profile.role;
            setUserRole(finalRole);

            // Set user information in Sentry
            // setSentryUser(user, finalRole);

            // Log login event
            // captureTypedEvent(SentryEventType.LOGIN, {
            //   user_id: user.uid,
            //   email: user.email,
            //   role: finalRole,
            //   display_name: user.displayName,
            // });
          } else {
            // If no profile found but we have custom claims, create a minimal profile for admin users
            if (customRole === 'admin') {
              const adminProfile = {
                uid: user.uid,
                name: user.displayName || user.email?.split('@')[0] || 'Admin',
                email: user.email || '',
                role: 'admin',
                university: 'Admin',
                emailVerified: true,
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
              };
              setUserProfile(adminProfile);
              setUserRole('admin');
              console.log('Created minimal admin profile from custom claims');
            } else if (customRole) {
              setUserProfile(null);
              setUserRole(customRole);
              console.log('No profile found but using custom claims role:', customRole);
            } else {
              console.log('No user profile found after retries, user may need to complete registration or have admin access');
              setUserProfile(null);
              setUserRole(null);
            }
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
          // Don't block the user if profile fetch fails
          setUserProfile(null);
          setUserRole(null);
        }
      } else {
        // Clear user information for logged out users
        setCurrentUser(null);
        // setSentryUser(null);

        // Log logout event only if user was previously logged in
        // if (currentUser) {
        //   captureTypedEvent(SentryEventType.LOGOUT);
        // }

        setUserProfile(null);
        setUserRole(null);
      }
      
      setIsLoading(false);
    });

    return unsubscribe;
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Function to refresh user profile data
  const refreshProfile = async () => {
    if (!currentUser) return;

    try {
      // Check for updated custom claims
      const tokenResult = await currentUser.getIdTokenResult(true); // Force refresh
      const customRole = tokenResult.claims.role as string;

      const profileResult = await getUserProfile();
      if (profileResult && profileResult.success) {
        const profile = profileResult.data as AppUser;
        setUserProfile(profile);

        // Use custom claims role if available, otherwise use profile role
        const finalRole = customRole || profile.role;
        setUserRole(finalRole);
      } else if (customRole === 'admin') {
        // Create minimal admin profile if none exists
        const adminProfile = {
          uid: currentUser.uid,
          name: currentUser.displayName || currentUser.email?.split('@')[0] || 'Admin',
          email: currentUser.email || '',
          role: 'admin',
          university: 'Admin',
          emailVerified: true,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        setUserProfile(adminProfile);
        setUserRole('admin');
      } else if (customRole) {
        // If no profile but we have custom claims, use the custom role
        setUserRole(customRole);
      }
    } catch (error) {
      console.error('Error refreshing profile:', error);
    }
  };

  // Derived role-based properties
  const isAdmin = userRole === 'admin';
  const isMerchant = userRole === 'merchant';
  const isStudent = userRole === 'student';

  const value = {
    currentUser,
    userProfile,
    userRole,
    isLoading,
    isAdmin,
    isMerchant,
    isStudent,
    refreshProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};