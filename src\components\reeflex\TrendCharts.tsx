import React, { useState, useEffect } from 'react';
import { collection, query, orderBy, getDocs, where, Timestamp } from 'firebase/firestore';
import { firestore } from '../../firebase/config';
import { <PERSON><PERSON><PERSON>, LineChart, PieChart, Calendar, Loader } from 'lucide-react';

// Types
interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
}

interface TrendChartsProps {
  timeRange?: 'day' | 'week' | 'month';
  className?: string;
}

const TrendCharts: React.FC<TrendChartsProps> = ({ timeRange = 'week', className = '' }) => {
  const [activityData, setActivityData] = useState<ChartData | null>(null);
  const [feedbackData, setFeedbackData] = useState<ChartData | null>(null);
  const [errorData, setErrorData] = useState<ChartData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChartData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Calculate date range based on timeRange
        const endDate = new Date();
        const startDate = new Date();
        
        switch (timeRange) {
          case 'day':
            startDate.setDate(startDate.getDate() - 1);
            break;
          case 'week':
            startDate.setDate(startDate.getDate() - 7);
            break;
          case 'month':
            startDate.setMonth(startDate.getMonth() - 1);
            break;
        }
        
        const startTimestamp = Timestamp.fromDate(startDate);
        const endTimestamp = Timestamp.fromDate(endDate);
        
        // Fetch activity data
        const activityQuery = query(
          collection(firestore, 'reeflex_activity'),
          where('timestamp', '>=', startTimestamp),
          where('timestamp', '<=', endTimestamp),
          orderBy('timestamp', 'asc')
        );
        
        const activitySnapshot = await getDocs(activityQuery);
        
        // Fetch feedback data
        const feedbackQuery = query(
          collection(firestore, 'reeflex_feedback'),
          where('timestamp', '>=', startTimestamp),
          where('timestamp', '<=', endTimestamp),
          orderBy('timestamp', 'asc')
        );
        
        const feedbackSnapshot = await getDocs(feedbackQuery);
        
        // Process activity data
        const activityByDate: Record<string, number> = {};
        const errorsByType: Record<string, number> = {};
        
        activitySnapshot.forEach(doc => {
          const data = doc.data();
          const date = data.timestamp.toDate().toLocaleDateString();
          
          // Count activities by date
          activityByDate[date] = (activityByDate[date] || 0) + 1;
          
          // Count errors by type
          if (['javascript_error', 'promise_rejection', 'api_error', 'api_failed'].includes(data.eventType)) {
            errorsByType[data.eventType] = (errorsByType[data.eventType] || 0) + 1;
          }
        });
        
        // Process feedback data
        const feedbackByType: Record<string, number> = {
          positive: 0,
          negative: 0,
          neutral: 0
        };
        
        feedbackSnapshot.forEach(doc => {
          const data = doc.data();
          const feedbackType = data.feedbackType || 'neutral';
          feedbackByType[feedbackType] = (feedbackByType[feedbackType] || 0) + 1;
        });
        
        // Prepare chart data
        const activityChartData: ChartData = {
          labels: Object.keys(activityByDate),
          datasets: [
            {
              label: 'Activity',
              data: Object.values(activityByDate),
              backgroundColor: 'rgba(59, 130, 246, 0.5)',
              borderColor: 'rgb(59, 130, 246)',
              borderWidth: 1
            }
          ]
        };
        
        const feedbackChartData: ChartData = {
          labels: ['Positive', 'Negative', 'Neutral'],
          datasets: [
            {
              label: 'Feedback',
              data: [
                feedbackByType.positive || 0,
                feedbackByType.negative || 0,
                feedbackByType.neutral || 0
              ],
              backgroundColor: [
                'rgba(34, 197, 94, 0.7)',
                'rgba(239, 68, 68, 0.7)',
                'rgba(107, 114, 128, 0.7)'
              ]
            }
          ]
        };
        
        const errorChartData: ChartData = {
          labels: Object.keys(errorsByType),
          datasets: [
            {
              label: 'Errors',
              data: Object.values(errorsByType),
              backgroundColor: [
                'rgba(239, 68, 68, 0.7)',
                'rgba(249, 115, 22, 0.7)',
                'rgba(234, 179, 8, 0.7)',
                'rgba(217, 119, 6, 0.7)'
              ]
            }
          ]
        };
        
        setActivityData(activityChartData);
        setFeedbackData(feedbackChartData);
        setErrorData(errorChartData);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching chart data:', err);
        setError('Failed to load chart data. Please try again later.');
        setIsLoading(false);
      }
    };
    
    fetchChartData();
  }, [timeRange]);
  
  if (isLoading) {
    return (
      <div className={`flex justify-center items-center p-8 ${className}`}>
        <Loader className="h-8 w-8 text-primary-500 animate-spin" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading charts...</span>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 ${className}`}>
        <p className="text-red-700 dark:text-red-400">{error}</p>
      </div>
    );
  }
  
  return (
    <div className={`grid grid-cols-1 lg:grid-cols-3 gap-6 ${className}`}>
      {/* Activity Trend Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <LineChart className="mr-2 h-5 w-5 text-blue-500" />
          Activity Trend
        </h3>
        
        <div className="h-64 flex items-center justify-center">
          {activityData && activityData.labels.length > 0 ? (
            <div className="w-full h-full">
              {/* This is where you would render the actual chart using a library like Chart.js or Recharts */}
              {/* For now, we'll show a placeholder */}
              <div className="w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Activity chart would render here with {activityData.labels.length} data points
                </p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 dark:text-gray-400 text-sm">No activity data available for the selected period</p>
          )}
        </div>
        
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            <span>
              {timeRange === 'day' ? 'Last 24 hours' : 
               timeRange === 'week' ? 'Last 7 days' : 'Last 30 days'}
            </span>
          </div>
        </div>
      </div>
      
      {/* Feedback Distribution Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <PieChart className="mr-2 h-5 w-5 text-green-500" />
          Feedback Distribution
        </h3>
        
        <div className="h-64 flex items-center justify-center">
          {feedbackData && feedbackData.datasets[0].data.some(val => val > 0) ? (
            <div className="w-full h-full">
              {/* This is where you would render the actual chart using a library like Chart.js or Recharts */}
              {/* For now, we'll show a placeholder */}
              <div className="w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Feedback pie chart would render here with data: 
                  Positive: {feedbackData.datasets[0].data[0]}, 
                  Negative: {feedbackData.datasets[0].data[1]}, 
                  Neutral: {feedbackData.datasets[0].data[2]}
                </p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 dark:text-gray-400 text-sm">No feedback data available for the selected period</p>
          )}
        </div>
        
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            <span>
              {timeRange === 'day' ? 'Last 24 hours' : 
               timeRange === 'week' ? 'Last 7 days' : 'Last 30 days'}
            </span>
          </div>
        </div>
      </div>
      
      {/* Error Types Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <BarChart className="mr-2 h-5 w-5 text-red-500" />
          Error Distribution
        </h3>
        
        <div className="h-64 flex items-center justify-center">
          {errorData && errorData.labels.length > 0 ? (
            <div className="w-full h-full">
              {/* This is where you would render the actual chart using a library like Chart.js or Recharts */}
              {/* For now, we'll show a placeholder */}
              <div className="w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Error bar chart would render here with {errorData.labels.length} error types
                </p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 dark:text-gray-400 text-sm">No error data available for the selected period</p>
          )}
        </div>
        
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            <span>
              {timeRange === 'day' ? 'Last 24 hours' : 
               timeRange === 'week' ? 'Last 7 days' : 'Last 30 days'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrendCharts;