// User related types
export interface User {
  uid: string;
  name: string;
  email: string;
  role: UserRole;
  university: string;
  profilePictureURL?: string;
  bio?: string;
  graduationYear?: number;
  major?: string;
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt?: FirebaseFirestore.Timestamp;
}

export type UserRole = 'student' | 'merchant' | 'admin';

// Listing related types
export interface Listing {
  id?: string;
  title: string;
  description: string;
  price: number;
  category: string;
  condition: ListingCondition;
  type: ListingType;
  ownerId: string;
  ownerName?: string;
  university: string;
  imageURLs: string[];
  status: ListingStatus;
  visibility: 'university' | 'public';
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt?: FirebaseFirestore.Timestamp;

  // Delivery method fields
  deliveryMethod: 'in_person' | 'mail';
  shippingOptions?: {
    model: 'shippo' | 'manual';
    paidBy: 'buyer' | 'seller';
    packageSize?: 'small' | 'medium' | 'large';
    estimatedCost?: number;
    allowBuyerChoice?: boolean;
  };

  // Rent-specific fields
  rentalPeriod?: 'weekly' | 'monthly';
  weeklyPrice?: number;
  monthlyPrice?: number;
  startDate?: string;
  endDate?: string;

  // Auction-specific fields
  startingBid?: number;
  currentBid?: number;
  auctionStartDate?: string;
  auctionStartTime?: string;
  auctionEndDate?: string;
  auctionEndTime?: string;
  auctionDuration?: number;
  bidders?: string[];
  highestBidder?: string;
}

export type ListingCondition = 'new' | 'like_new' | 'very_good' | 'good' | 'fair' | 'poor';
export type ListingType = 'sell' | 'rent' | 'auction';
export type ListingStatus = 'active' | 'sold' | 'pending' | 'deleted';

// Message related types
export interface Chat {
  id: string;
  participants: string[];
  lastMessage?: Message;
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt?: FirebaseFirestore.Timestamp;
}

export interface Message {
  id?: string;
  chatId: string;
  senderId: string;
  content: string;
  timestamp: FirebaseFirestore.Timestamp;
  read: boolean;
  listingContext?: {
    listingId: string;
    title: string;
    imageURL?: string;
  };
}

// Feedback related types
export interface Feedback {
  id?: string;
  userId: string;
  userName?: string;
  userEmail?: string;
  rating?: number | null;
  category: string;
  subject: string;
  message: string;
  attachments?: string[];
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt?: FirebaseFirestore.Timestamp;
}

export interface IssueReport {
  id?: string;
  userId: string;
  userName?: string;
  category: string;
  title: string;
  description: string;
  screenshotURL?: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt?: FirebaseFirestore.Timestamp;
}

export interface Complaint {
  id?: string;
  userId: string;
  userName?: string;
  userEmail?: string;
  category: 'seller_issue' | 'product_issue' | 'payment_issue' | 'security_issue' | 'platform_issue' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  subject: string;
  description: string;
  orderId?: string | null;
  attachments?: string[];
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  assignedTo?: string | null;
  resolution?: string | null;
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt?: FirebaseFirestore.Timestamp;
  updatedBy?: string;
}

// Wallet related types
export interface Wallet {
  userId: string;
  balance: number;
  referralCode: string;
  usedReferral: boolean;
  history: WalletTransaction[];
  grantedBy: 'admin' | 'referral' | 'signup' | 'cashback';
  lastUpdated: FirebaseFirestore.Timestamp;
  createdAt: FirebaseFirestore.Timestamp;
}

export interface WalletTransaction {
  id: string;
  userId: string;
  type: 'credit' | 'debit' | 'signup_bonus' | 'referral_bonus' | 'admin_grant' | 'purchase' | 'refund';
  amount: number;
  description: string;
  timestamp: FirebaseFirestore.Timestamp;
  grantedBy?: string; // For admin grants
  relatedUserId?: string; // For referrals
  orderId?: string; // For purchases/refunds
  expiresAt?: FirebaseFirestore.Timestamp; // For expiring credits
  source?: 'admin_grant' | 'referral_bonus' | 'signup_bonus' | 'cashback' | 'purchase_deduction';
  referralUserId?: string; // uid of user who used referral code
  createdAt?: FirebaseFirestore.Timestamp;
}

export interface ReferralCode {
  code: string;
  userId: string;
  usedBy: string[];
  totalRewards: number;
  isActive: boolean;
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt?: FirebaseFirestore.Timestamp;
}

export interface WalletCredit {
  id: string;
  userId: string;
  amount: number;
  type: 'signup_bonus' | 'referral_bonus' | 'admin_grant' | 'cashback';
  description: string;
  expiresAt: FirebaseFirestore.Timestamp;
  isExpired: boolean;
  grantedBy?: string;
  relatedUserId?: string;
  createdAt: FirebaseFirestore.Timestamp;
}

export interface WalletAnalytics {
  userId: string;
  totalCreditsEarned: number;
  totalCreditsUsed: number;
  referralCount: number;
  lastActivity: FirebaseFirestore.Timestamp;
  suspiciousActivity: boolean;
  activityScore: number;
}