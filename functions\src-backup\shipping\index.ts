import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { db } from '../config/firebase';
import { verifyAuth } from '../utils/helpers';
import { 
  getShippingRates, 
  createShippingLabel, 
  getTrackingInfo, 
  estimateShippingCost,
  validateAddress 
} from '../services/shippingService';

// CORS configuration
// eslint-disable-next-line @typescript-eslint/no-require-imports
const cors = require('cors')({
  origin: [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:5000'
  ],
  credentials: true
});

/**
 * Get shipping rates for a package
 */
export const getShippingRatesHttp = functions.https.onRequest(async (req, res) => {
  // Handle CORS
  cors(req, res, async () => {
    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Get the authorization token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const _decodedToken = await admin.auth().verifyIdToken(token);

      const { fromAddress, toAddress, packageInfo } = req.body;

      if (!fromAddress || !toAddress || !packageInfo) {
        res.status(400).json({ error: 'Missing required fields' });
        return;
      }

      const rates = await getShippingRates(fromAddress, toAddress, packageInfo);
      
      res.status(200).json({
        success: true,
        rates
      });
    } catch (error) {
      console.error('Error getting shipping rates:', error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  });
});

/**
 * Create shipping label for an order
 */
export const createShippingLabelHttp = functions.https.onRequest(async (req, res) => {
  // Handle CORS
  cors(req, res, async () => {
    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Get the authorization token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const userId = decodedToken.uid;

      const { orderId, rateId } = req.body;

      if (!orderId || !rateId) {
        res.status(400).json({ error: 'Missing orderId or rateId' });
        return;
      }

      // Verify user owns the order or is admin
      const orderDoc = await db.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        res.status(404).json({ error: 'Order not found' });
        return;
      }

      const orderData = orderDoc.data();
      const userDoc = await db.collection('users').doc(userId).get();
      const isAdmin = userDoc.exists && userDoc.data()?.role === 'admin';

      if (orderData?.sellerId !== userId && !isAdmin) {
        res.status(403).json({ error: 'Unauthorized to create label for this order' });
        return;
      }

      const label = await createShippingLabel(rateId, orderId);
      
      res.status(200).json({
        success: true,
        label
      });
    } catch (error) {
      console.error('Error creating shipping label:', error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  });
});

/**
 * Get tracking information
 */
export const getTrackingInfoHttp = functions.https.onRequest(async (req, res) => {
  // Handle CORS
  cors(req, res, async () => {
    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const { trackingNumber, carrier } = req.body;

      if (!trackingNumber || !carrier) {
        res.status(400).json({ error: 'Missing trackingNumber or carrier' });
        return;
      }

      const trackingInfo = await getTrackingInfo(trackingNumber, carrier);
      
      res.status(200).json({
        success: true,
        tracking: trackingInfo
      });
    } catch (error) {
      console.error('Error getting tracking info:', error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  });
});

/**
 * Estimate shipping cost
 */
export const estimateShippingCostHttp = functions.https.onRequest(async (req, res) => {
  // Handle CORS
  cors(req, res, async () => {
    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const { packageSize } = req.body;

      if (!packageSize) {
        res.status(400).json({ error: 'Missing packageSize' });
        return;
      }

      const estimate = estimateShippingCost(packageSize);
      
      res.status(200).json({
        success: true,
        estimate
      });
    } catch (error) {
      console.error('Error estimating shipping cost:', error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  });
});

/**
 * Validate shipping address
 */
export const validateAddressHttp = functions.https.onRequest(async (req, res) => {
  // Handle CORS
  cors(req, res, async () => {
    try {
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const { address } = req.body;

      if (!address) {
        res.status(400).json({ error: 'Missing address' });
        return;
      }

      const isValid = await validateAddress(address);
      
      res.status(200).json({
        success: true,
        valid: isValid
      });
    } catch (error) {
      console.error('Error validating address:', error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  });
});

/**
 * Update order shipping status based on tracking
 */
export const updateShippingStatus = functions.https.onCall(async (data, context) => {
  try {
    verifyAuth(context);

    const { orderId } = data;
    
    if (!orderId) {
      throw new Error('Missing orderId');
    }

    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }

    const orderData = orderDoc.data();
    const trackingNumber = orderData?.shipping?.trackingNumber;
    const carrier = orderData?.shipping?.carrier;

    if (!trackingNumber || !carrier) {
      throw new Error('No tracking information available');
    }

    const trackingInfo = await getTrackingInfo(trackingNumber, carrier);
    
    // Update order status based on tracking
    let newStatus = orderData.status;
    if (trackingInfo.tracking_status?.status === 'DELIVERED') {
      newStatus = 'delivered';
    } else if (trackingInfo.tracking_status?.status === 'TRANSIT') {
      newStatus = 'in_transit';
    }

    await db.collection('orders').doc(orderId).update({
      'shipping.trackingInfo': trackingInfo,
      'shipping.lastUpdated': admin.firestore.Timestamp.now(),
      status: newStatus
    });

    return {
      success: true,
      status: newStatus,
      tracking: trackingInfo
    };
  } catch (error) {
    console.error('Error updating shipping status:', error);
    throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
  }
});
