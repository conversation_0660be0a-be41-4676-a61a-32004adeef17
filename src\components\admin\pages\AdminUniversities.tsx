import React, { useState, useEffect } from 'react';
import {
  GraduationCap,
  Search,
  Plus,
  Users,
  Package,
  Settings,
  Eye,
  ToggleLeft,
  ToggleRight,
  Mail,
  Calendar,
  AlertCircle,
  CheckCircle,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { UniversityService, University, UniversityStats } from '../../../services/universityService';

const AdminUniversities: React.FC = () => {
  const [universities, setUniversities] = useState<University[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUniversity, setSelectedUniversity] = useState<University | null>(null);
  const [universityStats, _setUniversityStats] = useState<UniversityStats | null>(null);
  const [_showAddModal, _setShowAddModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedUniversities, setExpandedUniversities] = useState<Set<string>>(new Set());
  const [universityUsers, setUniversityUsers] = useState<Record<string, any[]>>({});
  const [loadingUsers, setLoadingUsers] = useState<Record<string, boolean>>({});

  useEffect(() => {
    fetchUniversities();
  }, []);

  const toggleUniversityExpansion = async (universityId: string) => {
    const newExpanded = new Set(expandedUniversities);

    if (expandedUniversities.has(universityId)) {
      newExpanded.delete(universityId);
    } else {
      newExpanded.add(universityId);
      // Load users for this university if not already loaded
      if (!universityUsers[universityId]) {
        await loadUniversityUsers(universityId);
      }
    }

    setExpandedUniversities(newExpanded);
  };

  const loadUniversityUsers = async (universityId: string) => {
    try {
      setLoadingUsers(prev => ({ ...prev, [universityId]: true }));

      // Get university domain to filter users
      const university = universities.find(u => u.id === universityId);
      if (!university) return;

      // Fetch users from this university
      const users = await UniversityService.getUsersByUniversity(university.domain);
      setUniversityUsers(prev => ({ ...prev, [universityId]: users }));
    } catch (err) {
      console.error('Error loading university users:', err);
      setUniversityUsers(prev => ({ ...prev, [universityId]: [] }));
    } finally {
      setLoadingUsers(prev => ({ ...prev, [universityId]: false }));
    }
  };

  const fetchUniversities = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await UniversityService.getUniversities();
      setUniversities(data);
    } catch (err) {
      console.error('Error fetching universities:', err);
      setError('Failed to load universities');
    } finally {
      setLoading(false);
    }
  };





  const handleToggleActive = async (university: University) => {
    try {
      if (university.isActive) {
        await UniversityService.deactivateUniversity(university.id);
      } else {
        await UniversityService.updateUniversity(university.id, { isActive: true });
      }
      await fetchUniversities();
    } catch (err) {
      console.error('Error toggling university status:', err);
    }
  };

  const filteredUniversities = universities.filter(uni =>
    uni.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    uni.domain.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading universities...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Universities
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Universities Management</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Manage university settings, domains, and access controls ({universities.length} universities)
          </p>
        </div>
        <button
          onClick={() => _setShowAddModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add University
        </button>
      </div>

      {/* Search */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search universities by name or domain..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Universities List */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                Universities ({filteredUniversities.length})
              </h3>

              {filteredUniversities.length === 0 ? (
                <div className="text-center py-12">
                  <GraduationCap className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No universities found</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {searchTerm ? 'Try adjusting your search terms.' : 'No universities are registered yet.'}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredUniversities.map((university) => (
                    <div key={university.id} className="border rounded-lg border-gray-200 dark:border-gray-700">
                      {/* University Header - Clickable to expand */}
                      <div
                        className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        onClick={() => {
                          toggleUniversityExpansion(university.id);
                          setSelectedUniversity(university);
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              {expandedUniversities.has(university.id) ? (
                                <ChevronDown className="h-5 w-5 text-gray-400" />
                              ) : (
                                <ChevronRight className="h-5 w-5 text-gray-400" />
                              )}
                            </div>
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                                <GraduationCap className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                              </div>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                                {university.name}
                              </h4>
                              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <Mail className="h-3 w-3 mr-1" />
                                {university.domain}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                <Users className="h-3 w-3 mr-1" />
                                {university.userCount} students
                              </div>
                              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                <Package className="h-3 w-3 mr-1" />
                                {university.listingCount} listings
                              </div>
                            </div>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleActive(university);
                              }}
                              className={`p-1 rounded ${
                                university.isActive
                                  ? 'text-green-600 hover:text-green-800'
                                  : 'text-gray-400 hover:text-gray-600'
                              }`}
                              title={university.isActive ? 'Deactivate' : 'Activate'}
                            >
                              {university.isActive ? (
                                <ToggleRight className="h-5 w-5" />
                              ) : (
                                <ToggleLeft className="h-5 w-5" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Expanded Student List */}
                      {expandedUniversities.has(university.id) && (
                        <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                                Students ({university.userCount})
                              </h5>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="text"
                                  placeholder="Search students..."
                                  className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                />
                                <Search className="h-4 w-4 text-gray-400" />
                              </div>
                            </div>

                            {loadingUsers[university.id] ? (
                              <div className="flex items-center justify-center py-4">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Loading students...</span>
                              </div>
                            ) : universityUsers[university.id]?.length > 0 ? (
                              <div className="space-y-2 max-h-64 overflow-y-auto">
                                {universityUsers[university.id].map((user) => (
                                  <div key={user.uid} className="flex items-center justify-between p-2 bg-white dark:bg-gray-700 rounded border">
                                    <div className="flex items-center space-x-3">
                                      <img
                                        src={user.profilePictureURL || '/placeholder-avatar.jpg'}
                                        alt={user.name}
                                        className="h-8 w-8 rounded-full bg-gray-300"
                                      />
                                      <div>
                                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                                          {user.name || 'Unknown User'}
                                        </p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                          {user.email}
                                        </p>
                                      </div>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <span className={`px-2 py-1 text-xs rounded-full ${
                                        user.role === 'admin' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300' :
                                        user.role === 'merchant' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300' :
                                        'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                                      }`}>
                                        {user.role || 'student'}
                                      </span>
                                      <button
                                        onClick={() => window.open(`/profile/${user.uid}`, '_blank')}
                                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                        title="View Profile"
                                      >
                                        <Eye className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-center py-4">
                                <Users className="mx-auto h-8 w-8 text-gray-400" />
                                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                  No students found for this university
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* University Details Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="px-4 py-5 sm:p-6">
              {selectedUniversity ? (
                <div className="space-y-6">
                  {/* University Header */}
                  <div className="text-center">
                    <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <GraduationCap className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {selectedUniversity.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {selectedUniversity.shortName}
                    </p>
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${
                      selectedUniversity.isActive
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
                    }`}>
                      {selectedUniversity.isActive ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <AlertCircle className="h-3 w-3 mr-1" />
                      )}
                      {selectedUniversity.isActive ? 'Active' : 'Inactive'}
                    </div>
                  </div>

                  {/* Quick Stats */}
                  {universityStats && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900 dark:text-white">
                          {universityStats.totalUsers}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Total Users</div>
                        <div className="text-xs text-green-600 dark:text-green-400">
                          +{universityStats.recentActivity.newUsersThisWeek} this week
                        </div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900 dark:text-white">
                          {universityStats.totalListings}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Total Listings</div>
                        <div className="text-xs text-green-600 dark:text-green-400">
                          +{universityStats.recentActivity.newListingsThisWeek} this week
                        </div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900 dark:text-white">
                          {universityStats.activeUsers}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Active Users</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Last 30 days</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900 dark:text-white">
                          {universityStats.activeListings}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Active Listings</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Currently live</div>
                      </div>
                    </div>
                  )}

                  {/* University Details */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Primary Domain
                      </label>
                      <div className="mt-1 flex items-center text-sm text-gray-900 dark:text-white">
                        <Mail className="h-4 w-4 mr-2 text-gray-400" />
                        {selectedUniversity.domain}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Allowed Email Domains
                      </label>
                      <div className="mt-1 space-y-1">
                        {selectedUniversity.allowedEmailDomains.map((domain, index) => (
                          <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                            @{domain}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Created
                      </label>
                      <div className="mt-1 flex items-center text-sm text-gray-900 dark:text-white">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        {formatDate(selectedUniversity.createdAt)}
                      </div>
                    </div>

                    {selectedUniversity.settings && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Settings
                        </label>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Public Listings</span>
                            <span className={selectedUniversity.settings.allowPublicListings ? 'text-green-600' : 'text-red-600'}>
                              {selectedUniversity.settings.allowPublicListings ? 'Enabled' : 'Disabled'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Cross-University Chat</span>
                            <span className={selectedUniversity.settings.allowCrossUniversityChat ? 'text-green-600' : 'text-red-600'}>
                              {selectedUniversity.settings.allowCrossUniversityChat ? 'Enabled' : 'Disabled'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Moderation Level</span>
                            <span className="capitalize text-gray-900 dark:text-white">
                              {selectedUniversity.settings.moderationLevel}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <button className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </button>
                    <button className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <GraduationCap className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    Select a University
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Choose a university from the list to view details and manage settings.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminUniversities;
