import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { UserRole } from "./types";

// Validate if email is from an educational institution
export const isEducationalEmail = (email: string): boolean => {
  return email.endsWith('.edu') ||
         email.includes('@outlook.') ||
         email.includes('@hotmail.') ||
         email.includes('@live.') ||
         email.includes('@student.');
};

// Create a deterministic chat ID from two user IDs
export const createChatId = (uid1: string, uid2: string): string => {
  return [uid1, uid2].sort().join('_');
};

// Verify user authentication
export const verifyAuth = async (context: functions.https.CallableContext): Promise<admin.auth.DecodedIdToken> => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }
  return context.auth as unknown as admin.auth.DecodedIdToken;
};

// Verify user role
export const verifyRole = async (
  uid: string,
  allowedRoles: UserRole[]
): Promise<boolean> => {
  try {
    const userDoc = await admin.firestore().collection('users').doc(uid).get();
    
    if (!userDoc.exists) {
      return false;
    }
    
    const userData = userDoc.data() as { role: UserRole };
    return allowedRoles.includes(userData.role);
  } catch (error) {
    console.error('Error verifying user role:', error);
    return false;
  }
};

// Validate file type
export const isValidFileType = (fileName: string): boolean => {
  const validExtensions = ['.jpg', '.jpeg', '.png'];
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return validExtensions.includes(extension);
};

// Handle errors consistently
export const handleError = (error: unknown): never => {
  console.error('Function error:', error);

  if (error instanceof functions.https.HttpsError) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
  throw new functions.https.HttpsError(
    'internal',
    errorMessage,
    error
  );
};