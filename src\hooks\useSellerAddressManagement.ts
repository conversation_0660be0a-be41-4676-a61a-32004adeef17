import { useState, useEffect, useCallback } from 'react';
import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { useAuth } from './useAuth';
import { SellerAddress } from '../firebase/types';

interface UseSellerAddressManagementReturn {
  addresses: SellerAddress[];
  isLoading: boolean;
  error: string | null;
  addAddress: (address: Omit<SellerAddress, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  updateAddress: (addressId: string, updates: Partial<SellerAddress>) => Promise<void>;
  deleteAddress: (addressId: string) => Promise<void>;
  setDefaultAddress: (addressId: string) => Promise<void>;
  getDefaultAddress: () => SellerAddress | null;
  refreshAddresses: () => Promise<void>;
}

export const useSellerAddressManagement = (): UseSellerAddressManagementReturn => {
  const [addresses, setAddresses] = useState<SellerAddress[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { currentUser } = useAuth();

  // Fetch seller addresses
  const fetchAddresses = useCallback(async () => {
    if (!currentUser) {
      setAddresses([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const addressesRef = collection(firestore, 'users', currentUser.uid, 'sellerAddresses');
      const q = query(addressesRef, orderBy('createdAt', 'desc'), limit(3)); // Allow up to 3 seller addresses
      const snapshot = await getDocs(q);

      const fetchedAddresses: SellerAddress[] = [];
      snapshot.forEach((doc) => {
        fetchedAddresses.push({
          id: doc.id,
          ...doc.data()
        } as SellerAddress);
      });

      setAddresses(fetchedAddresses);
    } catch (err: any) {
      console.error('Error fetching seller addresses:', err);

      // Handle permission errors gracefully
      if (err.code === 'permission-denied') {
        console.log('No seller addresses found or permission denied - this is normal for new users');
        setAddresses([]);
        setError(null);
      } else {
        setError('Failed to load seller addresses');
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  // Add new seller address
  const addAddress = async (addressData: Omit<SellerAddress, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
    if (!currentUser) {
      throw new Error('No authenticated user');
    }

    // Check if user already has 3 addresses
    if (addresses.length >= 3) {
      throw new Error('You can only save up to 3 seller addresses. Please delete an existing address first.');
    }

    try {
      const addressesRef = collection(firestore, 'users', currentUser.uid, 'sellerAddresses');
      
      // If this is the first address or marked as default, make it default
      const isFirstAddress = addresses.length === 0;
      const shouldBeDefault = isFirstAddress || addressData.isDefault;

      // If setting as default, update other addresses to not be default
      if (shouldBeDefault && addresses.length > 0) {
        for (const addr of addresses) {
          if (addr.isDefault && addr.id) {
            await updateDoc(doc(firestore, 'users', currentUser.uid, 'sellerAddresses', addr.id), {
              isDefault: false,
              updatedAt: Timestamp.now()
            });
          }
        }
      }

      const newAddress = {
        ...addressData,
        isDefault: shouldBeDefault,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const docRef = await addDoc(addressesRef, newAddress);
      
      // Refresh addresses
      await fetchAddresses();
      
      return docRef.id;
    } catch (err) {
      console.error('Error adding seller address:', err);
      setError('Failed to add seller address');
      throw err;
    }
  };

  // Update seller address
  const updateAddress = async (addressId: string, updates: Partial<SellerAddress>): Promise<void> => {
    if (!currentUser) {
      throw new Error('No authenticated user');
    }

    try {
      const addressRef = doc(firestore, 'users', currentUser.uid, 'sellerAddresses', addressId);
      
      // If setting as default, update other addresses to not be default
      if (updates.isDefault) {
        for (const addr of addresses) {
          if (addr.id !== addressId && addr.isDefault && addr.id) {
            await updateDoc(doc(firestore, 'users', currentUser.uid, 'sellerAddresses', addr.id), {
              isDefault: false,
              updatedAt: Timestamp.now()
            });
          }
        }
      }

      await updateDoc(addressRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });

      // Refresh addresses
      await fetchAddresses();
    } catch (err) {
      console.error('Error updating seller address:', err);
      setError('Failed to update seller address');
      throw err;
    }
  };

  // Delete seller address
  const deleteAddress = async (addressId: string): Promise<void> => {
    if (!currentUser) {
      throw new Error('No authenticated user');
    }

    try {
      const addressRef = doc(firestore, 'users', currentUser.uid, 'sellerAddresses', addressId);
      await deleteDoc(addressRef);

      // If we deleted the default address and there are other addresses, make the first one default
      const deletedAddress = addresses.find(addr => addr.id === addressId);
      if (deletedAddress?.isDefault && addresses.length > 1) {
        const remainingAddresses = addresses.filter(addr => addr.id !== addressId);
        if (remainingAddresses.length > 0 && remainingAddresses[0].id) {
          await updateAddress(remainingAddresses[0].id, { isDefault: true });
        }
      }

      // Refresh addresses
      await fetchAddresses();
    } catch (err) {
      console.error('Error deleting seller address:', err);
      setError('Failed to delete seller address');
      throw err;
    }
  };

  // Set default seller address
  const setDefaultAddress = async (addressId: string): Promise<void> => {
    await updateAddress(addressId, { isDefault: true });
  };

  // Get default seller address
  const getDefaultAddress = (): SellerAddress | null => {
    return addresses.find(addr => addr.isDefault) || addresses[0] || null;
  };

  // Refresh seller addresses
  const refreshAddresses = async (): Promise<void> => {
    await fetchAddresses();
  };

  // Fetch addresses on mount and when user changes
  useEffect(() => {
    if (currentUser) {
      fetchAddresses();
    } else {
      setAddresses([]);
    }
  }, [currentUser, fetchAddresses]);

  return {
    addresses,
    isLoading,
    error,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    getDefaultAddress,
    refreshAddresses
  };
};
