<!DOCTYPE html>
<html>
<head>
    <title>Create Sample Listings</title>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js"></script>
</head>
<body>
    <h1>Create Sample Listings</h1>
    <button id="loginBtn">Login as Admin</button>
    <button id="createBtn" disabled>Create Sample Listings</button>
    <div id="status"></div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdefghijklmnop"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const functions = firebase.functions();

        const loginBtn = document.getElementById('loginBtn');
        const createBtn = document.getElementById('createBtn');
        const status = document.getElementById('status');

        // Login with admin credentials
        loginBtn.addEventListener('click', async () => {
            try {
                status.textContent = 'Logging in...';
                await auth.signInWithEmailAndPassword('<EMAIL>', 'AdminPassword123!');
                status.textContent = 'Logged in successfully!';
                createBtn.disabled = false;
                loginBtn.disabled = true;
            } catch (error) {
                status.textContent = 'Login failed: ' + error.message;
            }
        });

        // Create sample listings
        createBtn.addEventListener('click', async () => {
            try {
                status.textContent = 'Creating sample listings...';
                const createSampleListings = functions.httpsCallable('createSampleListings');
                const result = await createSampleListings();
                status.textContent = 'Success: ' + result.data.message;
                console.log('Created listings:', result.data.listings);
            } catch (error) {
                status.textContent = 'Error: ' + error.message;
                console.error('Error:', error);
            }
        });

        // Monitor auth state
        auth.onAuthStateChanged((user) => {
            if (user) {
                status.textContent = 'Logged in as: ' + user.email;
                createBtn.disabled = false;
                loginBtn.disabled = true;
            } else {
                status.textContent = 'Not logged in';
                createBtn.disabled = true;
                loginBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
