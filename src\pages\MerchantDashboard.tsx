import React, { useState } from 'react';
import { 
  Package, 
  DollarSign, 
  TrendingUp, 
  Plus,
  Edit,
  Trash2,
  BarChart3,
  Megaphone,
  Settings,
  Search,
  Star,
  ShoppingBag
} from 'lucide-react';


const MerchantDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // Real merchant data will be fetched from Firebase
  const merchantData = {
    businessName: 'Your Business',
    totalProducts: 0,
    activeProducts: 0,
    totalSales: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    averageRating: 0,
    totalReviews: 0,
    impressions: 0,
    clicks: 0,
    conversionRate: 0
  };

  // Real product data will be fetched from Firebase
  const recentProducts: any[] = [];

  // Real sales data will be fetched from Firebase
  const salesData: any[] = [];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'products', label: 'Products', icon: Package },
    { id: 'sales', label: 'Sales', icon: DollarSign },
    { id: 'marketing', label: 'Marketing', icon: Megaphone },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 dark:bg-success-900/20 text-success-700 dark:text-success-400';
      case 'low_stock':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400';
      case 'out_of_stock':
        return 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center">
              <Package className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <span className="text-sm text-success-600 dark:text-success-400 font-medium">
              +12 this week
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
            {merchantData.totalProducts}
          </div>
          <p className="text-gray-600 dark:text-gray-400">Total Products</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
            {merchantData.activeProducts} active
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-success-100 dark:bg-success-900/20 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-success-600 dark:text-success-400" />
            </div>
            <span className="text-sm text-success-600 dark:text-success-400 font-medium">
              +18.5%
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
            ${merchantData.monthlyRevenue.toLocaleString()}
          </div>
          <p className="text-gray-600 dark:text-gray-400">Monthly Revenue</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
            ${merchantData.totalRevenue.toLocaleString()} total
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-accent-100 dark:bg-accent-900/20 rounded-xl flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-accent-600 dark:text-accent-400" />
            </div>
            <span className="text-sm text-success-600 dark:text-success-400 font-medium">
              +25.3%
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
            {merchantData.totalSales.toLocaleString()}
          </div>
          <p className="text-gray-600 dark:text-gray-400">Total Sales</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
            {merchantData.conversionRate}% conversion rate
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
              <Star className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <span className="text-sm text-success-600 dark:text-success-400 font-medium">
              4.8★ avg
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
            {merchantData.totalReviews.toLocaleString()}
          </div>
          <p className="text-gray-600 dark:text-gray-400">Customer Reviews</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
            {merchantData.averageRating} average rating
          </p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <button className="bg-gradient-to-r from-primary-600 to-primary-700 text-white p-6 rounded-2xl text-left hover:from-primary-700 hover:to-primary-800 transition-all transform hover:scale-[1.02] group">
          <div className="flex items-center justify-between mb-4">
            <Plus className="w-8 h-8" />
            <div className="w-2 h-2 bg-white rounded-full opacity-50 group-hover:opacity-100 transition-opacity"></div>
          </div>
          <h3 className="text-xl font-bold mb-2">Add New Product</h3>
          <p className="text-blue-100">List a new product for students</p>
        </button>

        <button className="bg-gradient-to-r from-accent-600 to-accent-700 text-white p-6 rounded-2xl text-left hover:from-accent-700 hover:to-accent-800 transition-all transform hover:scale-[1.02] group">
          <div className="flex items-center justify-between mb-4">
            <Megaphone className="w-8 h-8" />
            <div className="w-2 h-2 bg-white rounded-full opacity-50 group-hover:opacity-100 transition-opacity"></div>
          </div>
          <h3 className="text-xl font-bold mb-2">Create Campaign</h3>
          <p className="text-orange-100">Promote your products on campus</p>
        </button>

        <button className="bg-gradient-to-r from-success-600 to-success-700 text-white p-6 rounded-2xl text-left hover:from-success-700 hover:to-success-800 transition-all transform hover:scale-[1.02] group">
          <div className="flex items-center justify-between mb-4">
            <BarChart3 className="w-8 h-8" />
            <div className="w-2 h-2 bg-white rounded-full opacity-50 group-hover:opacity-100 transition-opacity"></div>
          </div>
          <h3 className="text-xl font-bold mb-2">View Analytics</h3>
          <p className="text-green-100">Track your performance metrics</p>
        </button>
      </div>

      {/* Recent Products & Sales by University */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Products</h3>
          <div className="space-y-4">
            {recentProducts.slice(0, 5).map((product) => (
              <div key={product.id} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-12 h-12 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <p className="font-medium text-gray-900 dark:text-white text-sm">{product.name}</p>
                  <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>${product.price}</span>
                    <span>•</span>
                    <span>{product.sales} sales</span>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                  {product.status.replace('_', ' ')}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sales by University</h3>
          <div className="space-y-4">
            {salesData.map((university, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-primary-600 dark:text-primary-400">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white text-sm">{university.university}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{university.sales} sales</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">${university.revenue.toLocaleString()}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderProducts = () => (
    <div className="space-y-6">
      {/* Products Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Product Management</h2>
          <p className="text-gray-600 dark:text-gray-400">Manage your product listings and inventory</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="relative">
            <Search className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <button className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors">
            <Plus className="w-4 h-4" />
            <span>Add Product</span>
          </button>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {recentProducts.map((product) => (
          <div key={product.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all">
            <div className="relative">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-4 left-4">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                  {product.status.replace('_', ' ')}
                </span>
              </div>
              <div className="absolute top-4 right-4 flex space-x-2">
                <button className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors">
                  <Edit className="w-4 h-4 text-gray-600" />
                </button>
                <button className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors">
                  <Trash2 className="w-4 h-4 text-red-600" />
                </button>
              </div>
            </div>
            <div className="p-6">
              <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2">
                {product.name}
              </h3>
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  ${product.price}
                </span>
                {product.originalPrice > product.price && (
                  <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                    ${product.originalPrice}
                  </span>
                )}
              </div>
              <div className="grid grid-cols-3 gap-4 text-center text-sm">
                <div>
                  <p className="font-semibold text-gray-900 dark:text-white">{product.stock}</p>
                  <p className="text-gray-500 dark:text-gray-400">Stock</p>
                </div>
                <div>
                  <p className="font-semibold text-gray-900 dark:text-white">{product.views}</p>
                  <p className="text-gray-500 dark:text-gray-400">Views</p>
                </div>
                <div>
                  <p className="font-semibold text-gray-900 dark:text-white">{product.sales}</p>
                  <p className="text-gray-500 dark:text-gray-400">Sales</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'products':
        return renderProducts();
      default:
        return renderOverview();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Merchant Navigation */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                      activeTab === tab.id
                        ? 'border-accent-500 text-accent-600 dark:text-accent-400'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {renderTabContent()}
        </div>
      </div>
  );
};

export default MerchantDashboard;