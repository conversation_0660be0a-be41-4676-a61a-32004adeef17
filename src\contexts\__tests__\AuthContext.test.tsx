import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { AuthProvider, AuthContext } from '../AuthContext'
import { mockUser } from '../../test/mocks/firebase'
import { useContext } from 'react'

// Mock Firebase auth
const mockOnAuthStateChanged = vi.fn()
const mockGetUserProfile = vi.fn()

vi.mock('../../firebase', () => ({
  auth: {
    onAuthStateChanged: mockOnAuthStateChanged
  },
  getUserProfile: mockGetUserProfile
}))

// Test component that uses AuthContext
const TestComponent = () => {
  const auth = useContext(AuthContext)
  
  return (
    <div>
      <div data-testid="loading">{auth.isLoading ? 'loading' : 'loaded'}</div>
      <div data-testid="user">{auth.currentUser?.email || 'no user'}</div>
      <div data-testid="role">{auth.userRole || 'no role'}</div>
      <div data-testid="is-admin">{auth.isAdmin ? 'yes' : 'no'}</div>
      <div data-testid="is-merchant">{auth.isMerchant ? 'yes' : 'no'}</div>
      <div data-testid="is-student">{auth.isStudent ? 'yes' : 'no'}</div>
    </div>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with loading state', () => {
    // Setup mock to not call the callback immediately
    mockOnAuthStateChanged.mockImplementation(() => vi.fn())
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )
    
    expect(screen.getByTestId('loading')).toHaveTextContent('loading')
    expect(screen.getByTestId('user')).toHaveTextContent('no user')
    expect(screen.getByTestId('role')).toHaveTextContent('no role')
  })

  it('should handle authenticated user with student role', async () => {
    const mockProfile = {
      id: 'test-user-id',
      email: '<EMAIL>',
      displayName: 'Test User',
      role: 'student' as const,
      university: 'Test University',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockGetUserProfile.mockResolvedValue({
      success: true,
      data: mockProfile
    })

    mockOnAuthStateChanged.mockImplementation((callback) => {
      // Simulate immediate auth state change
      setTimeout(() => callback(mockUser), 0)
      return vi.fn() // unsubscribe function
    })

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
    })

    expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    expect(screen.getByTestId('role')).toHaveTextContent('student')
    expect(screen.getByTestId('is-student')).toHaveTextContent('yes')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('no')
    expect(screen.getByTestId('is-merchant')).toHaveTextContent('no')
  })

  it('should handle authenticated user with admin role', async () => {
    const mockProfile = {
      id: 'admin-user-id',
      email: '<EMAIL>',
      displayName: 'Admin User',
      role: 'admin' as const,
      university: 'Test University',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockGetUserProfile.mockResolvedValue({
      success: true,
      data: mockProfile
    })

    mockOnAuthStateChanged.mockImplementation((callback) => {
      setTimeout(() => callback({ ...mockUser, email: '<EMAIL>' }), 0)
      return vi.fn()
    })

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
    })

    expect(screen.getByTestId('role')).toHaveTextContent('admin')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('yes')
    expect(screen.getByTestId('is-student')).toHaveTextContent('no')
    expect(screen.getByTestId('is-merchant')).toHaveTextContent('no')
  })

  it('should handle merchant role', async () => {
    const mockProfile = {
      id: 'merchant-user-id',
      email: '<EMAIL>',
      displayName: 'Merchant User',
      role: 'merchant' as const,
      university: 'Test University',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockGetUserProfile.mockResolvedValue({
      success: true,
      data: mockProfile
    })

    mockOnAuthStateChanged.mockImplementation((callback) => {
      setTimeout(() => callback({ ...mockUser, email: '<EMAIL>' }), 0)
      return vi.fn()
    })

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('role')).toHaveTextContent('merchant')
    })

    expect(screen.getByTestId('is-merchant')).toHaveTextContent('yes')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('no')
    expect(screen.getByTestId('is-student')).toHaveTextContent('no')
  })

  it('should handle unauthenticated user', async () => {
    mockOnAuthStateChanged.mockImplementation((callback) => {
      setTimeout(() => callback(null), 0)
      return vi.fn()
    })

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
    })

    expect(screen.getByTestId('user')).toHaveTextContent('no user')
    expect(screen.getByTestId('role')).toHaveTextContent('no role')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('no')
    expect(screen.getByTestId('is-merchant')).toHaveTextContent('no')
    expect(screen.getByTestId('is-student')).toHaveTextContent('no')
  })

  it('should handle profile fetch error gracefully', async () => {
    mockGetUserProfile.mockRejectedValue(new Error('Profile fetch failed'))

    mockOnAuthStateChanged.mockImplementation((callback) => {
      setTimeout(() => callback(mockUser), 0)
      return vi.fn()
    })

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
    })

    // Should still show user email but no role
    expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    expect(screen.getByTestId('role')).toHaveTextContent('no role')
  })

  it('should handle profile fetch returning unsuccessful result', async () => {
    mockGetUserProfile.mockResolvedValue({
      success: false,
      error: 'User not found'
    })

    mockOnAuthStateChanged.mockImplementation((callback) => {
      setTimeout(() => callback(mockUser), 0)
      return vi.fn()
    })

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('loaded')
    })

    expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    expect(screen.getByTestId('role')).toHaveTextContent('no role')
  })
})