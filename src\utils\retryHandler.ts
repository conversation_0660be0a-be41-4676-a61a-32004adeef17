/**
 * Retry Handler Utility for Hive Campus
 * Provides intelligent retry mechanisms for failed operations
 */

import { captureException } from './sentry';

export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  retryCondition?: (error: Error) => boolean;
  onRetry?: (attempt: number, error: Error) => void;
}

export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  attempts: number;
}

/**
 * Default retry condition - retry on network errors and 5xx status codes
 */
const defaultRetryCondition = (error: Error): boolean => {
  const message = error.message.toLowerCase();
  
  // Network errors
  if (message.includes('network') || 
      message.includes('timeout') || 
      message.includes('connection') ||
      message.includes('fetch')) {
    return true;
  }
  
  // Firebase errors that are retryable
  if (message.includes('unavailable') || 
      message.includes('deadline-exceeded') ||
      message.includes('internal')) {
    return true;
  }
  
  // Stripe temporary errors
  if (message.includes('rate_limit') || 
      message.includes('api_connection_error') ||
      message.includes('api_error')) {
    return true;
  }
  
  return false;
};

/**
 * Retry an async operation with exponential backoff
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<RetryResult<T>> {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    retryCondition = defaultRetryCondition,
    onRetry
  } = options;

  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const result = await operation();
      return {
        success: true,
        data: result,
        attempts: attempt
      };
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Don't retry on the last attempt or if retry condition fails
      if (attempt === maxAttempts || !retryCondition(lastError)) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        baseDelay * Math.pow(backoffFactor, attempt - 1),
        maxDelay
      );
      
      // Call retry callback if provided
      if (onRetry) {
        onRetry(attempt, lastError);
      }
      
      // Log retry attempt
      console.warn(`Retry attempt ${attempt}/${maxAttempts} after ${delay}ms:`, lastError.message);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // All attempts failed
  captureException(lastError!, {
    operation: 'retry_operation',
    max_attempts: maxAttempts,
    final_attempt: maxAttempts
  });
  
  return {
    success: false,
    error: lastError!,
    attempts: maxAttempts
  };
}

/**
 * Retry specifically for payment operations
 */
export async function retryPaymentOperation<T>(
  operation: () => Promise<T>,
  operationName: string = 'payment'
): Promise<RetryResult<T>> {
  return retryOperation(operation, {
    maxAttempts: 3,
    baseDelay: 2000,
    maxDelay: 8000,
    retryCondition: (error) => {
      const message = error.message.toLowerCase();
      
      // Retry on network issues
      if (message.includes('network') || message.includes('timeout')) {
        return true;
      }
      
      // Retry on temporary Stripe errors
      if (message.includes('rate_limit') || 
          message.includes('api_connection_error') ||
          message.includes('try again')) {
        return true;
      }
      
      // Don't retry on card declined, insufficient funds, etc.
      if (message.includes('declined') || 
          message.includes('insufficient') ||
          message.includes('invalid') ||
          message.includes('expired')) {
        return false;
      }
      
      return false;
    },
    onRetry: (attempt, error) => {
      console.log(`Retrying ${operationName} (attempt ${attempt}):`, error.message);
      
      // Track retry attempts
      captureException(error, {
        operation: operationName,
        retry_attempt: attempt,
        is_retry: true
      });
    }
  });
}

/**
 * Retry for Firebase operations
 */
export async function retryFirebaseOperation<T>(
  operation: () => Promise<T>,
  operationName: string = 'firebase'
): Promise<RetryResult<T>> {
  return retryOperation(operation, {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 5000,
    retryCondition: (error) => {
      const message = error.message.toLowerCase();
      
      // Retry on temporary Firebase errors
      return message.includes('unavailable') || 
             message.includes('deadline-exceeded') ||
             message.includes('internal') ||
             message.includes('timeout');
    },
    onRetry: (attempt, error) => {
      console.log(`Retrying ${operationName} (attempt ${attempt}):`, error.message);
    }
  });
}
