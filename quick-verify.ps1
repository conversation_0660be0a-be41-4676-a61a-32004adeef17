# Simple Hive Campus Setup Verification
Write-Host "Verifying Hive Campus setup..." -ForegroundColor Cyan

$ErrorCount = 0

# Check Node.js
Write-Host "`nChecking Node.js..." -ForegroundColor Blue
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Node.js not found" -ForegroundColor Red
        $ErrorCount++
    }
} catch {
    Write-Host "❌ Node.js not found" -ForegroundColor Red
    $ErrorCount++
}

# Check npm
Write-Host "Checking npm..." -ForegroundColor Blue
try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ npm not found" -ForegroundColor Red
        $ErrorCount++
    }
} catch {
    Write-Host "❌ npm not found" -ForegroundColor Red
    $ErrorCount++
}

# Check Firebase CLI
Write-Host "Checking Firebase CLI..." -ForegroundColor Blue
try {
    $firebaseCheck = Get-Command firebase -ErrorAction SilentlyContinue
    if ($firebaseCheck) {
        Write-Host "✅ Firebase CLI installed" -ForegroundColor Green
    } else {
        Write-Host "❌ Firebase CLI not found" -ForegroundColor Red
        Write-Host "   Install with: npm install -g firebase-tools" -ForegroundColor Yellow
        $ErrorCount++
    }
} catch {
    Write-Host "❌ Firebase CLI not found" -ForegroundColor Red
    $ErrorCount++
}

# Check dependencies
Write-Host "Checking dependencies..." -ForegroundColor Blue
if ((Test-Path "package.json") -and (Test-Path "node_modules")) {
    Write-Host "✅ Frontend dependencies installed" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend dependencies missing - run: npm install" -ForegroundColor Red
    $ErrorCount++
}

if ((Test-Path "functions/package.json") -and (Test-Path "functions/node_modules")) {
    Write-Host "✅ Functions dependencies installed" -ForegroundColor Green
} else {
    Write-Host "❌ Functions dependencies missing - run: cd functions; npm install" -ForegroundColor Red
    $ErrorCount++
}

# Check key files
Write-Host "Checking configuration files..." -ForegroundColor Blue
$RequiredFiles = @(
    "firebase.json",
    "functions/src/config/environment.ts",
    ".github/workflows/deploy.yml"
)

foreach ($file in $RequiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: $file" -ForegroundColor Red
        $ErrorCount++
    }
}

# Build test
Write-Host "Testing builds..." -ForegroundColor Blue
Push-Location "functions"
$buildOutput = npm run build 2>&1
Pop-Location
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Functions build successful" -ForegroundColor Green
} else {
    Write-Host "❌ Functions build failed" -ForegroundColor Red
    $ErrorCount++
}

# Summary
Write-Host "`n========================" -ForegroundColor Cyan
Write-Host "   VERIFICATION RESULT" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

if ($ErrorCount -eq 0) {
    Write-Host "`n🎉 SUCCESS: All checks passed!" -ForegroundColor Green
    Write-Host "`nYour Hive Campus setup is ready for deployment." -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Blue
    Write-Host "1. Run: .\setup-production.sh (to configure Firebase)" -ForegroundColor White
    Write-Host "2. Run: .\deploy.sh --environment production (to deploy)" -ForegroundColor White
} else {
    Write-Host "`n❌ ISSUES FOUND: $ErrorCount error(s)" -ForegroundColor Red
    Write-Host "Please fix the errors above before proceeding." -ForegroundColor Red
}

Write-Host "`nPhase 1 setup verification complete!" -ForegroundColor Yellow