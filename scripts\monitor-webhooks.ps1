# 📊 Webhook Monitoring Script
# Monitors Firebase Functions logs for webhook processing

param(
    [int]$RefreshInterval = 5,
    [switch]$FollowLogs
)

$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"
$Red = "Red"

Write-Host "📊 Starting Webhook Monitoring" -ForegroundColor $Blue
Write-Host "Monitoring Firebase Functions logs for webhook events..." -ForegroundColor $Yellow
Write-Host "Press Ctrl+C to stop monitoring" -ForegroundColor $Yellow
Write-Host ""

# Function to check Firebase logs
function Get-WebhookLogs {
    try {
        $logs = firebase functions:log --only stripeApi 2>$null
        return $logs
    }
    catch {
        Write-Host "❌ Error fetching logs: $($_.Exception.Message)" -ForegroundColor $Red
        return $null
    }
}

# Function to parse and display relevant webhook events
function Show-WebhookEvents {
    param([string[]]$Logs)
    
    $webhookEvents = @()
    $currentEvent = @{}
    
    foreach ($line in $Logs) {
        if ($line -match "capability\.updated|transfer\.failed|payment_intent\.succeeded|account\.updated") {
            if ($line -match "(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z).*stripeApi:\s*(.*)") {
                $timestamp = $matches[1]
                $message = $matches[2]

                Write-Host "🔔 [$timestamp] $message" -ForegroundColor $Green
            }
        }
        elseif ($line -match "Error|Failed") {
            if ($line -match "(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z).*stripeApi:\s*(.*)") {
                $timestamp = $matches[1]
                $message = $matches[2]

                Write-Host "❌ [$timestamp] $message" -ForegroundColor $Red
            }
        }
    }
}

# Function to check Firestore for recent webhook data
function Check-FirestoreData {
    Write-Host "`n📊 Recent Webhook Processing Summary:" -ForegroundColor $Blue
    Write-Host "Check these Firestore collections for webhook results:" -ForegroundColor $Yellow
    Write-Host "• connectAccounts - Capability status updates" -ForegroundColor $Yellow
    Write-Host "• transfer_failures - Failed transfer logs" -ForegroundColor $Yellow
    Write-Host "• notifications - User notifications" -ForegroundColor $Yellow
    Write-Host "• webhook_errors - Processing errors" -ForegroundColor $Yellow
    Write-Host ""
    Write-Host "🌐 Firestore Console: https://console.firebase.google.com/project/h1c1-798a8/firestore" -ForegroundColor $Blue
}

# Main monitoring loop
if ($FollowLogs) {
    Write-Host "📡 Following logs in real-time..." -ForegroundColor $Blue
    
    while ($true) {
        $logs = Get-WebhookLogs
        if ($logs) {
            $recentLogs = $logs | Select-Object -Last 20
            Clear-Host
            Write-Host "📊 Webhook Monitoring - $(Get-Date)" -ForegroundColor $Blue
            Write-Host "=" * 60 -ForegroundColor $Blue
            Show-WebhookEvents $recentLogs
            Check-FirestoreData
        }
        
        Start-Sleep -Seconds $RefreshInterval
    }
}
else {
    # Single check
    Write-Host "📡 Checking recent webhook logs..." -ForegroundColor $Blue
    $logs = Get-WebhookLogs
    if ($logs) {
        $recentLogs = $logs | Select-Object -Last 50
        Show-WebhookEvents $recentLogs
        Check-FirestoreData
    }
}

Write-Host "`n🎯 Testing Instructions:" -ForegroundColor $Blue
Write-Host "1. Go to Stripe Dashboard → Webhooks" -ForegroundColor $Yellow
Write-Host "2. Click your webhook endpoint" -ForegroundColor $Yellow
Write-Host "3. Click 'Send test webhook'" -ForegroundColor $Yellow
Write-Host "4. Test these events:" -ForegroundColor $Yellow
Write-Host "   • capability.updated" -ForegroundColor $Yellow
Write-Host "   • transfer.failed" -ForegroundColor $Yellow
Write-Host "   • payment_intent.succeeded" -ForegroundColor $Yellow
Write-Host "   • account.updated" -ForegroundColor $Yellow
Write-Host ""
Write-Host "💡 Run with -FollowLogs to monitor in real-time" -ForegroundColor $Blue
