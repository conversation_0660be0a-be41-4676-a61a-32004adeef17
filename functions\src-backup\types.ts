import { Timestamp } from "firebase-admin/firestore";

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  university: string;
  role: 'student' | 'merchant' | 'admin';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Listing {
  id: string;
  title: string;
  description: string;
  price: number;
  category: string;
  condition: string;
  imageURLs: string[];
  ownerId: string;
  ownerName?: string;
  university: string;
  status: 'active' | 'sold' | 'pending' | 'inactive';
  visibility: 'university' | 'public';
  type: 'sell' | 'rent' | 'auction';
  createdAt: Timestamp;
  updatedAt: Timestamp;

  // Rent-specific fields
  rentalPeriod?: 'weekly' | 'monthly';
  weeklyPrice?: number;
  monthlyPrice?: number;
  startDate?: string;
  endDate?: string;

  // Auction-specific fields
  startingBid?: number;
  currentBid?: number;
  auctionStartDate?: string;
  auctionStartTime?: string;
  auctionEndDate?: string;
  auctionEndTime?: string;
  auctionDuration?: number;
  bidders?: string[];
  highestBidder?: string;
}

export interface ShippingAddress {
  name: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

export interface TrackingInfo {
  carrier: string;
  trackingNumber: string;
  trackingUrl?: string;
  shippedDate?: Timestamp;
  estimatedDeliveryDate?: Timestamp;
}

export type OrderStatus = 
  | 'created' 
  | 'payment_pending' 
  | 'payment_succeeded' 
  | 'payment_failed' 
  | 'shipped_pending_code' 
  | 'delivered' 
  | 'completed' 
  | 'refunded' 
  | 'canceled';

export interface Order {
  id: string;
  listingId: string;
  buyerId: string;
  sellerId: string;
  title: string;
  description?: string;
  amount: number;
  commissionRate: number;
  commissionAmount: number;
  sellerAmount: number;
  cashbackAmount: number;
  walletAmountUsed: number;
  stripeSessionId: string;
  stripePaymentIntentId?: string;
  stripeTransferId?: string;
  status: OrderStatus;
  shippingAddress?: ShippingAddress;
  trackingInfo?: TrackingInfo;
  isTextbook: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  completedAt?: Timestamp;
  releasedAt?: Timestamp;
  deliveredAt?: Timestamp;
  // Enhanced escrow fields
  sellerOnboarded: boolean;
  pendingPayout: boolean;
  stripeAccountId?: string | null;
  escrowReleaseDate?: Timestamp;
  onboardingReminderSent?: boolean;
  onboardingReminderCount?: number;
  // Order type and action details
  orderType: 'buy' | 'rent' | 'bid';
  rentalPeriod?: 'weekly' | 'monthly';
  bidAmount?: number;
  // Shipping fee
  shippingFee?: number;
}

export interface Wallet {
  userId: string;
  balance: number;
  lastUpdated: Timestamp;
}

export interface SecretCode {
  orderId: string;
  code: string;
  expiresAt: Timestamp;
  isUsed: boolean;
  usedAt?: Timestamp;
}

export interface ShippingLabel {
  orderId: string;
  carrier: string;
  trackingNumber: string;
  labelUrl: string;
  createdAt: Timestamp;
}

export interface ConnectAccount {
  userId: string;
  stripeAccountId: string;
  accountType: 'student' | 'merchant';
  isOnboarded: boolean;
  chargesEnabled?: boolean;
  payoutsEnabled?: boolean;
  detailsSubmitted?: boolean;
  onboardingUrl?: string;
  dashboardUrl?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  onboardedAt?: Timestamp;
  // Email reminder tracking
  lastReminderSent?: Timestamp;
  reminderCount?: number;
  // Mock account flag for testing
  isMockAccount?: boolean;
}

export interface CreateCheckoutSessionRequest {
  listingId: string;
  useWalletBalance: boolean;
  orderDetails?: {
    type: 'buy' | 'rent' | 'bid';
    price?: number;
    period?: 'weekly' | 'monthly';
    bidAmount?: number;
    appliedWalletCredit?: number;
    shippingAddress?: ShippingAddress;
  };
}

export interface CreateConnectAccountRequest {
  accountType: 'student' | 'merchant';
}

export interface ReleaseFundsWithCodeRequest {
  orderId: string;
  code: string;
}

export interface GenerateShippingLabelRequest {
  orderId: string;
}

export interface ApplyWalletBalanceRequest {
  amount: number;
}

export interface UpdateWalletAfterCashbackRequest {
  orderId: string;
}

// Enhanced Stripe Connect types
export interface CreateStripeAccountRequest {
  accountType: 'student' | 'merchant';
}

export interface GetStripeOnboardingLinkRequest {
  refreshUrl?: string;
  returnUrl?: string;
}

export interface StripeAccountResponse {
  accountId: string;
  onboardingUrl?: string;
  dashboardUrl?: string;
  isOnboarded: boolean;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
}

export interface PendingPayoutOrder {
  orderId: string;
  amount: number;
  commissionAmount: number;
  sellerAmount: number;
  paymentIntentId: string;
  createdAt: Timestamp;
}

export interface EmailReminderData {
  userId: string;
  email: string;
  name: string;
  pendingAmount: number;
  orderCount: number;
  onboardingUrl: string;
}