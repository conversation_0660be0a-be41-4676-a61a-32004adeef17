import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { 
  Heart, 
  Share2, 
  MessageCircle, 
  ShoppingCart, 
  ChevronLeft, 
  Star, 
  Shield, 
  MapPin, 
  Clock, 
  Tag, 
  AlertTriangle, 
  ChevronRight, 
  ArrowLeft, 
  ArrowRight,
  Truck,
  Package,
  CreditCard,
  Check,
  X,
  Flag,
  Eye
} from 'lucide-react';

// Mock product data
const mockProduct = {
  id: '123',
  title: 'iPhone 14 Pro - Excellent Condition',
  description: 'Selling my iPhone 14 Pro 128GB in excellent condition. Only 6 months old, no scratches or dents. Comes with original box, charger, and a free case. Battery health at 98%. Reason for selling: upgrading to the latest model.',
  price: 899,
  originalPrice: 1099,
  condition: 'Like New',
  category: 'Electronics',
  subcategory: 'Mobile Phones',
  brand: 'Apple',
  model: 'iPhone 14 Pro',
  color: 'Space Black',
  storage: '128GB',
  images: [
    'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/607812/pexels-photo-607812.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/404280/pexels-photo-404280.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1092644/pexels-photo-1092644.jpeg?auto=compress&cs=tinysrgb&w=800'
  ],
  seller: {
    id: 'user456',
    name: 'Alex Johnson',
    avatar: '/placeholder-avatar.svg',
    university: 'Stanford University',
    rating: 4.9,
    reviews: 27,
    verified: true,
    memberSince: 'September 2022',
    responseTime: '< 1 hour'
  },
  location: 'Stanford, CA',
  postedDate: '2023-09-15T14:30:00Z',
  expiryDate: '2023-10-15T14:30:00Z',
  views: 342,
  likes: 24,
  shipping: {
    available: true,
    cost: 15,
    estimatedDelivery: '2-3 business days'
  },
  pickup: {
    available: true,
    location: 'Stanford Campus'
  },
  paymentMethods: ['Cash', 'Venmo', 'PayPal', 'Credit Card'],
  tags: ['iPhone', 'Apple', 'Mobile Phone', 'Smartphone', 'Tech'],
  specifications: [
    { name: 'Brand', value: 'Apple' },
    { name: 'Model', value: 'iPhone 14 Pro' },
    { name: 'Color', value: 'Space Black' },
    { name: 'Storage', value: '128GB' },
    { name: 'Screen Size', value: '6.1 inches' },
    { name: 'Battery Health', value: '98%' },
    { name: 'Operating System', value: 'iOS 16' },
    { name: 'Warranty', value: 'Apple Care+ until March 2024' }
  ],
  relatedProducts: [
    {
      id: '124',
      title: 'iPhone 13 Pro Max - Good Condition',
      price: 750,
      originalPrice: 1099,
      image: 'https://images.pexels.com/photos/5750001/pexels-photo-5750001.jpeg?auto=compress&cs=tinysrgb&w=400',
      condition: 'Good'
    },
    {
      id: '125',
      title: 'AirPods Pro 2nd Generation',
      price: 180,
      originalPrice: 249,
      image: 'https://images.pexels.com/photos/3780681/pexels-photo-3780681.jpeg?auto=compress&cs=tinysrgb&w=400',
      condition: 'Like New'
    },
    {
      id: '126',
      title: 'MacBook Air M2 - Space Gray',
      price: 1200,
      originalPrice: 1499,
      image: 'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg?auto=compress&cs=tinysrgb&w=400',
      condition: 'Like New'
    }
  ]
};

const ProductDetailScreen: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [product, setProduct] = useState(mockProduct);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'specifications' | 'shipping'>('details');
  const [showImageGallery, setShowImageGallery] = useState(false);

  useEffect(() => {
    // Simulate API call to fetch product details
    const fetchProduct = async () => {
      setIsLoading(true);
      try {
        // In a real app, you would fetch the product data from an API
        // For now, we'll use the mock data and add a delay to simulate a network request
        await new Promise(resolve => setTimeout(resolve, 1000));
        setProduct(mockProduct);
        setIsLoading(false);
      } catch {
        setError('Failed to load product details. Please try again.');
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const handlePrevImage = () => {
    setCurrentImageIndex(prev => 
      prev === 0 ? product.images.length - 1 : prev - 1
    );
  };

  const handleNextImage = () => {
    setCurrentImageIndex(prev => 
      prev === product.images.length - 1 ? 0 : prev + 1
    );
  };

  const handleBuyNow = () => {
    navigate(`/checkout/${product.id}`);
  };

  const handleContactSeller = () => {
    navigate(`/messages`, { state: { sellerId: product.seller.id, productId: product.id } });
  };

  const handleToggleLike = () => {
    setIsLiked(prev => !prev);
    // In a real app, you would make an API call to update the like status
  };

  const handleShare = () => {
    // Implement share functionality
    if (navigator.share) {
      navigator.share({
        title: product.title,
        text: `Check out this ${product.title} on Hive Campus!`,
        url: window.location.href
      }).catch(err => {
        console.error('Error sharing:', err);
      });
    } else {
      // Fallback for browsers that don't support the Web Share API
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const handleReport = () => {
    // Implement report functionality
    alert('Report functionality will be implemented soon.');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const calculateTimeLeft = (expiryDateString: string) => {
    const expiryDate = new Date(expiryDateString);
    const now = new Date();
    const diffTime = expiryDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) {
      return 'Expired';
    } else if (diffDays === 1) {
      return '1 day left';
    } else {
      return `${diffDays} days left`;
    }
  };

  const discountPercentage = Math.round(
    ((product.originalPrice - product.price) / product.originalPrice) * 100
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 max-w-md w-full">
          <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 dark:bg-red-900/30 rounded-full">
            <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <h2 className="text-xl font-bold text-center text-gray-900 dark:text-white mb-2">Error</h2>
          <p className="text-center text-gray-600 dark:text-gray-400 mb-6">{error}</p>
          <button
            onClick={() => navigate(-1)}
            className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-xl font-semibold transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-16 md:pb-0 md:ml-64">
      {/* Image Gallery Modal */}
      {showImageGallery && (
        <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
          <button 
            onClick={() => setShowImageGallery(false)}
            className="absolute top-4 right-4 text-white p-2 rounded-full bg-gray-800/50 hover:bg-gray-800/80 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
          
          <div className="relative w-full max-w-4xl">
            <img 
              src={product.images[currentImageIndex]} 
              alt={product.title} 
              className="w-full h-auto max-h-[80vh] object-contain"
            />
            
            <button
              onClick={handlePrevImage}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/40 p-2 rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-white" />
            </button>
            
            <button
              onClick={handleNextImage}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/40 p-2 rounded-full transition-colors"
            >
              <ArrowRight className="w-6 h-6 text-white" />
            </button>
            
            <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
              {product.images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-2 h-2 rounded-full ${
                    index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      )}
      
      {/* Back Button and Actions */}
      <div className="sticky top-0 z-20 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            >
              <ChevronLeft className="w-5 h-5 mr-1" />
              <span className="text-sm font-medium">Back</span>
            </button>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={handleToggleLike}
                className={`p-2 rounded-full ${
                  isLiked 
                    ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                } transition-colors`}
              >
                <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
              </button>
              
              <button
                onClick={handleShare}
                className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <Share2 className="w-5 h-5" />
              </button>
              
              <button
                onClick={handleReport}
                className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <Flag className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div>
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-md mb-4">
              <img 
                src={product.images[currentImageIndex]} 
                alt={product.title} 
                className="w-full h-auto aspect-square object-cover cursor-pointer"
                onClick={() => setShowImageGallery(true)}
              />
              
              {product.images.length > 1 && (
                <>
                  <button
                    onClick={handlePrevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/70 dark:bg-gray-800/70 hover:bg-white/90 dark:hover:bg-gray-800/90 p-2 rounded-full shadow-md transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                  </button>
                  
                  <button
                    onClick={handleNextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/70 dark:bg-gray-800/70 hover:bg-white/90 dark:hover:bg-gray-800/90 p-2 rounded-full shadow-md transition-colors"
                  >
                    <ArrowRight className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                  </button>
                </>
              )}
              
              <div className="absolute top-4 left-4">
                <span className="bg-success-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {discountPercentage}% OFF
                </span>
              </div>
              
              <div className="absolute bottom-4 right-4">
                <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {calculateTimeLeft(product.expiryDate)}
                </span>
              </div>
              
              <div className="absolute bottom-4 left-4 flex items-center space-x-2">
                <span className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  {product.views}
                </span>
                
                <span className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                  <Heart className="w-4 h-4 mr-1" />
                  {product.likes}
                </span>
              </div>
            </div>
            
            {/* Thumbnail Images */}
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`relative rounded-lg overflow-hidden ${
                    index === currentImageIndex 
                      ? 'ring-2 ring-primary-500 dark:ring-primary-400' 
                      : 'hover:opacity-80'
                  } transition-all`}
                >
                  <img 
                    src={image} 
                    alt={`${product.title} - Image ${index + 1}`} 
                    className="w-full h-20 object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
          
          {/* Product Details */}
          <div>
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-2xl shadow-lg p-6 mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {product.title}
              </h1>
              
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                  ${product.price}
                </span>
                <span className="text-lg text-gray-500 dark:text-gray-400 line-through">
                  ${product.originalPrice}
                </span>
                <span className="bg-success-100 dark:bg-success-900/20 text-success-800 dark:text-success-300 px-2 py-1 rounded-md text-sm font-medium">
                  Save ${product.originalPrice - product.price}
                </span>
              </div>
              
              <div className="flex flex-wrap gap-2 mb-6">
                <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                  <Tag className="w-4 h-4 mr-1" />
                  {product.condition}
                </span>
                
                <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                  <MapPin className="w-4 h-4 mr-1" />
                  {product.location}
                </span>
                
                <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  Posted {formatDate(product.postedDate)}
                </span>
              </div>
              
              {/* Action Buttons */}
              <div className="flex space-x-4 mb-6">
                <button
                  onClick={handleBuyNow}
                  className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2 shadow-md hover:shadow-lg transform hover:translate-y-[-2px]"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>Buy Now</span>
                </button>
                
                <button
                  onClick={handleContactSeller}
                  className="flex-1 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span>Contact Seller</span>
                </button>
              </div>
              
              {/* Seller Information */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mb-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Seller Information</h2>
                <div className="flex items-center">
                  <img 
                    src={product.seller.avatar} 
                    alt={product.seller.name} 
                    className="w-12 h-12 rounded-full object-cover mr-4"
                  />
                  <div>
                    <div className="flex items-center">
                      <h3 className="font-semibold text-gray-900 dark:text-white mr-2">
                        {product.seller.name}
                      </h3>
                      {product.seller.verified && (
                        <div className="bg-primary-500 text-white p-1 rounded-full">
                          <Shield className="w-3 h-3 fill-current" />
                        </div>
                      )}
                    </div>
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                      <span>{product.seller.rating} ({product.seller.reviews} reviews)</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {product.seller.university}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                    <p className="text-xs text-gray-500 dark:text-gray-400">Member Since</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{product.seller.memberSince}</p>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                    <p className="text-xs text-gray-500 dark:text-gray-400">Response Time</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{product.seller.responseTime}</p>
                  </div>
                </div>
                
                <Link 
                  to={`/seller/${product.seller.id}`}
                  className="mt-4 inline-flex items-center text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                >
                  View seller profile
                  <ChevronRight className="w-4 h-4 ml-1" />
                </Link>
              </div>
              
              {/* Tabs */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
                  <button
                    onClick={() => setActiveTab('details')}
                    className={`pb-3 px-4 text-sm font-medium ${
                      activeTab === 'details'
                        ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                  >
                    Details
                  </button>
                  <button
                    onClick={() => setActiveTab('specifications')}
                    className={`pb-3 px-4 text-sm font-medium ${
                      activeTab === 'specifications'
                        ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                  >
                    Specifications
                  </button>
                  <button
                    onClick={() => setActiveTab('shipping')}
                    className={`pb-3 px-4 text-sm font-medium ${
                      activeTab === 'shipping'
                        ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                  >
                    Shipping & Payments
                  </button>
                </div>
                
                {activeTab === 'details' && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Description</h3>
                    <p className={`text-gray-700 dark:text-gray-300 mb-4 ${!showFullDescription && 'line-clamp-3'}`}>
                      {product.description}
                    </p>
                    {product.description.length > 150 && (
                      <button
                        onClick={() => setShowFullDescription(!showFullDescription)}
                        className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                      >
                        {showFullDescription ? 'Show less' : 'Read more'}
                      </button>
                    )}
                    
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {product.tags.map((tag, index) => (
                          <span 
                            key={index}
                            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
                
                {activeTab === 'specifications' && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Product Specifications</h3>
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {product.specifications.map((spec, index) => (
                            <tr key={index}>
                              <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                {spec.name}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">
                                {spec.value}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
                
                {activeTab === 'shipping' && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Shipping Options</h3>
                    <div className="space-y-4 mb-6">
                      {product.shipping.available && (
                        <div className="flex items-start p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                          <div className="mr-4 mt-1">
                            <Truck className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">Shipping</h4>
                            <p className="text-sm text-gray-700 dark:text-gray-300">
                              ${product.shipping.cost} • Estimated delivery: {product.shipping.estimatedDelivery}
                            </p>
                          </div>
                        </div>
                      )}
                      
                      {product.pickup.available && (
                        <div className="flex items-start p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                          <div className="mr-4 mt-1">
                            <Package className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">Local Pickup</h4>
                            <p className="text-sm text-gray-700 dark:text-gray-300">
                              Free • Location: {product.pickup.location}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Payment Methods</h3>
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                      <div className="flex flex-wrap gap-2">
                        {product.paymentMethods.map((method, index) => (
                          <div 
                            key={index}
                            className="flex items-center bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm"
                          >
                            <CreditCard className="w-4 h-4 text-primary-600 dark:text-primary-400 mr-2" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">{method}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Related Products */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Related Products</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {product.relatedProducts.map((relatedProduct) => (
              <Link 
                key={relatedProduct.id}
                to={`/listing/${relatedProduct.id}`}
                className="bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden group"
              >
                <div className="relative overflow-hidden">
                  <img
                    src={relatedProduct.image}
                    alt={relatedProduct.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-success-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {Math.round(((relatedProduct.originalPrice - relatedProduct.price) / relatedProduct.originalPrice) * 100)}% OFF
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {relatedProduct.title}
                  </h3>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                      ${relatedProduct.price}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                      ${relatedProduct.originalPrice}
                    </span>
                  </div>
                  <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
                    {relatedProduct.condition}
                  </span>
                </div>
              </Link>
            ))}
          </div>
        </div>
        
        {/* Safety Tips */}
        <div className="mt-12 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-100 dark:border-yellow-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-300 mb-3 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Safety Tips for Buyers
          </h3>
          <ul className="space-y-2 text-yellow-700 dark:text-yellow-200">
            <li className="flex items-start">
              <Check className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>Meet in a public place and inspect the item before paying</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>Use secure payment methods and avoid wire transfers</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>Check the seller's profile and reviews before purchasing</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>Report suspicious listings or behavior to our team</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailScreen;