import React from 'react';
import { FileText, Users, Shield, G<PERSON>l, Alert<PERSON>riangle, CheckCircle } from 'lucide-react';

const TermsAndConditions: React.FC = () => {
  const sections = [
    {
      icon: Users,
      title: 'User Eligibility',
      content: [
        'Must be a current student at an accredited university or college',
        'Must provide valid .edu email address for verification',
        'Must be at least 18 years old or have parental consent',
        'One account per person - no duplicate accounts allowed',
        'Account may be suspended for violation of community guidelines'
      ]
    },
    {
      icon: Shield,
      title: 'Platform Rules',
      content: [
        'Only sell items you legally own and have the right to sell',
        'Provide accurate descriptions and photos of listed items',
        'No counterfeit, stolen, or prohibited items allowed',
        'Maintain respectful communication with other users',
        'Complete transactions in good faith and within agreed timeframes',
        'Report suspicious activity or policy violations promptly'
      ]
    },
    {
      icon: Gavel,
      title: 'Transactions',
      content: [
        'All transactions are between individual users - Hive Campus facilitates connections only',
        'Payment processing is handled through secure third-party providers',
        'Platform fees may apply to certain transaction types',
        'Disputes should be resolved directly between buyer and seller first',
        'Hive Campus may assist with dispute resolution but is not liable for transaction outcomes',
        'Users are responsible for applicable taxes on their transactions'
      ]
    },
    {
      icon: AlertTriangle,
      title: 'Prohibited Activities',
      content: [
        'Creating fake accounts or impersonating others',
        'Posting false or misleading information about items',
        'Harassment, bullying, or inappropriate behavior',
        'Attempting to circumvent platform safety measures',
        'Using the platform for commercial business purposes',
        'Sharing personal contact information in public listings'
      ]
    },
    {
      icon: CheckCircle,
      title: 'Account Responsibilities',
      content: [
        'Keep your account information accurate and up to date',
        'Protect your login credentials and report unauthorized access',
        'Use your real name and current university email',
        'Respond to messages and transaction requests promptly',
        'Follow through on commitments made to other users',
        'Review and follow all community guidelines and policies'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <FileText className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Terms & Conditions</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Please read these terms carefully. By using Hive Campus, you agree to be bound by these terms and conditions.
          </p>
          <div className="mt-6 text-sm text-gray-500 dark:text-gray-400">
            Last updated: December 2024
          </div>
        </div>

        {/* Introduction */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Welcome to Hive Campus</h2>
          <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
            Hive Campus is a peer-to-peer marketplace designed exclusively for verified university students. Our platform 
            enables students to buy, sell, and trade items safely within their campus communities.
          </p>
          <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
            By accessing or using our services, you acknowledge that you have read, understood, and agree to be bound by 
            these Terms and Conditions. If you do not agree with any part of these terms, please do not use our platform.
          </p>
        </div>

        {/* Terms Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center">
                  <section.icon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{section.title}</h2>
              </div>
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700 dark:text-gray-300">{item}</p>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Limitation of Liability */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-2xl p-8 mt-8">
          <div className="flex items-start space-x-4">
            <AlertTriangle className="w-8 h-8 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-1" />
            <div>
              <h3 className="text-xl font-bold text-yellow-800 dark:text-yellow-200 mb-3">Important Disclaimer</h3>
              <p className="text-yellow-700 dark:text-yellow-300 leading-relaxed mb-4">
                Hive Campus acts as a platform to connect student buyers and sellers. We do not own, sell, or ship any items. 
                All transactions are between individual users, and we are not responsible for the quality, safety, legality, 
                or accuracy of items listed.
              </p>
              <p className="text-yellow-700 dark:text-yellow-300 leading-relaxed">
                Users engage in transactions at their own risk. We recommend meeting in safe, public locations and 
                carefully inspecting items before purchase.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl shadow-lg p-8 mt-8 text-white">
          <div className="text-center">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-90" />
            <h2 className="text-2xl font-bold mb-4">Questions About These Terms?</h2>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              If you have any questions about these Terms and Conditions or need clarification on any policies, 
              our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="bg-white text-primary-600 px-6 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-colors"
              >
                Contact Legal Team
              </a>
              <button className="border border-white/30 text-white px-6 py-3 rounded-xl font-semibold hover:bg-white/10 transition-colors">
                Download PDF
              </button>
            </div>
          </div>
        </div>

        {/* Acceptance */}
        <div className="bg-success-50 dark:bg-success-900/20 border border-success-200 dark:border-success-800 rounded-2xl p-8 mt-8">
          <div className="flex items-center space-x-4">
            <CheckCircle className="w-8 h-8 text-success-600 dark:text-success-400 flex-shrink-0" />
            <div>
              <h3 className="text-xl font-bold text-success-800 dark:text-success-200 mb-2">Acceptance of Terms</h3>
              <p className="text-success-700 dark:text-success-300">
                By continuing to use Hive Campus, you confirm that you have read, understood, and agree to abide by these 
                Terms and Conditions. These terms may be updated from time to time, and continued use of the platform 
                constitutes acceptance of any changes.
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-12 text-sm text-gray-500 dark:text-gray-400">
          <p>
            These terms are effective as of December 1, 2024. We reserve the right to modify these terms at any time, 
            with notice provided to users of any material changes.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditions;