import React from 'react';

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  href?: string;
  className?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  onClick,
  href,
  className = '',
  disabled = false,
  type = 'button'
}) => {
  const buttonContent = (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`uiverse ${className}`}
      style={{
        '--duration': '7s',
        '--easing': 'linear',
        '--c-color-1': 'rgba(255, 163, 26, 0.7)',
        '--c-color-2': '#1a23ff',
        '--c-color-3': '#e21bda',
        '--c-color-4': 'rgba(255, 232, 26, 0.7)',
        '--c-shadow': 'rgba(255, 223, 87, 0.5)',
        '--c-shadow-inset-top': 'rgba(255, 223, 52, 0.9)',
        '--c-shadow-inset-bottom': 'rgba(255, 250, 215, 0.8)',
        '--c-radial-inner': '#ffd215',
        '--c-radial-outer': '#fff172',
        '--c-color': '#fff',
        WebkitTapHighlightColor: 'transparent',
        WebkitAppearance: 'none',
        outline: 'none',
        position: 'relative',
        cursor: disabled ? 'not-allowed' : 'pointer',
        border: 'none',
        display: 'table',
        borderRadius: '24px',
        padding: 0,
        margin: 0,
        textAlign: 'center',
        fontWeight: 600,
        fontSize: '16px',
        letterSpacing: '0.02em',
        lineHeight: 1.5,
        color: 'var(--c-color)',
        background: 'radial-gradient(circle, var(--c-radial-inner), var(--c-radial-outer) 80%)',
        boxShadow: '0 0 14px var(--c-shadow)',
        opacity: disabled ? 0.5 : 1
      } as React.CSSProperties}
    >
      <div className="wrapper" style={{
        WebkitMaskImage: '-webkit-radial-gradient(white, black)',
        overflow: 'hidden',
        borderRadius: '24px',
        minWidth: '132px',
        padding: '12px 0'
      }}>
        <span style={{
          display: 'inline-block',
          position: 'relative',
          zIndex: 1
        }}>
          {children}
        </span>
        
        {/* Animated circles */}
        <div className="circle circle-1" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(8px)',
          background: 'var(--c-color-4)',
          transform: 'translate(0px, -40px) translateZ(0)',
          animation: 'circle-1 var(--duration) var(--easing) infinite'
        }} />
        
        <div className="circle circle-2" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(12px)',
          background: 'var(--c-color-1)',
          transform: 'translate(92px, 8px) translateZ(0)',
          animation: 'circle-2 var(--duration) var(--easing) infinite'
        }} />
        
        <div className="circle circle-3" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(14px)',
          background: 'var(--c-color-2)',
          transform: 'translate(-12px, -12px) translateZ(0)',
          animation: 'circle-3 var(--duration) var(--easing) infinite'
        }} />
        
        <div className="circle circle-4" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(14px)',
          background: 'var(--c-color-2)',
          transform: 'translate(80px, -12px) translateZ(0)',
          animation: 'circle-4 var(--duration) var(--easing) infinite'
        }} />
        
        <div className="circle circle-5" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(16px)',
          background: 'var(--c-color-3)',
          transform: 'translate(12px, -4px) translateZ(0)',
          animation: 'circle-5 var(--duration) var(--easing) infinite'
        }} />
        
        <div className="circle circle-6" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(16px)',
          background: 'var(--c-color-3)',
          transform: 'translate(56px, 16px) translateZ(0)',
          animation: 'circle-6 var(--duration) var(--easing) infinite'
        }} />

        <div className="circle circle-7" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(12px)',
          background: 'var(--c-color-1)',
          transform: 'translate(8px, 28px) translateZ(0)',
          animation: 'circle-7 var(--duration) var(--easing) infinite'
        }} />

        <div className="circle circle-8" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(12px)',
          background: 'var(--c-color-1)',
          transform: 'translate(28px, -4px) translateZ(0)',
          animation: 'circle-8 var(--duration) var(--easing) infinite'
        }} />

        <div className="circle circle-9" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(8px)',
          background: 'var(--c-color-4)',
          transform: 'translate(20px, -12px) translateZ(0)',
          animation: 'circle-9 var(--duration) var(--easing) infinite'
        }} />

        <div className="circle circle-10" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(8px)',
          background: 'var(--c-color-4)',
          transform: 'translate(64px, 16px) translateZ(0)',
          animation: 'circle-10 var(--duration) var(--easing) infinite'
        }} />

        <div className="circle circle-11" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(12px)',
          background: 'var(--c-color-1)',
          transform: 'translate(4px, 4px) translateZ(0)',
          animation: 'circle-11 var(--duration) var(--easing) infinite'
        }} />

        <div className="circle circle-12" style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          filter: 'blur(14px)',
          background: 'var(--c-color-1)',
          transform: 'translate(52px, 4px) translateZ(0)',
          animation: 'circle-12 var(--duration) var(--easing) infinite'
        }} />
      </div>
    </button>
  );

  if (href) {
    return (
      <a href={href} className="inline-block">
        {buttonContent}
      </a>
    );
  }

  return buttonContent;
};

export default AnimatedButton;
