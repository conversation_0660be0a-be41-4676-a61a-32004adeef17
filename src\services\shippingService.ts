// import { httpsCallable } from 'firebase/functions';
// import { functions } from '../firebase/config';

export interface ShippingAddress {
  name: string;
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone?: string;
}

export interface PackageDetails {
  useCustom: boolean;
  preset?: string;
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: 'in' | 'cm';
  };
  weight?: {
    value: number;
    unit: 'oz' | 'lb';
  };
}

export interface ShippingRate {
  carrier: string;
  service: string;
  amount: number;
  currency: string;
  estimatedDays: number;
  rateId: string;
}

export interface GetShippingRatesRequest {
  fromAddress: ShippingAddress;
  toAddress: ShippingAddress;
  packageDetails: PackageDetails;
}

export interface GetShippingRatesResponse {
  success: boolean;
  rates: ShippingRate[];
  error?: string;
}

// Package presets that match the backend
export const PACKAGE_PRESETS = {
  'Small Box': { 
    dimensions: { length: 9, width: 6, height: 2, unit: 'in' as const },
    weight: { value: 10, unit: 'oz' as const }
  },
  'Medium Box': { 
    dimensions: { length: 12, width: 10, height: 4, unit: 'in' as const },
    weight: { value: 2, unit: 'lb' as const }
  },
  'Poly Mailer': { 
    dimensions: { length: 10, width: 13, height: 1, unit: 'in' as const },
    weight: { value: 8, unit: 'oz' as const }
  }
};

/**
 * Get real-time shipping rates from Shippo
 */
export const getShippingRates = async (
  fromAddress: ShippingAddress,
  toAddress: ShippingAddress,
  packageDetails: PackageDetails
): Promise<ShippingRate[]> => {
  try {
    // For now, return mock rates based on package details until Cloud Function is deployed
    console.log('Getting shipping rates for:', { fromAddress, toAddress, packageDetails });

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Calculate estimated rates based on package details
    let baseRate = 6.50; // Default medium package rate

    if (packageDetails.useCustom && packageDetails.weight) {
      // Calculate based on weight
      const weightInOz = packageDetails.weight.unit === 'lb'
        ? packageDetails.weight.value * 16
        : packageDetails.weight.value;

      if (weightInOz <= 4) baseRate = 4.50;
      else if (weightInOz <= 16) baseRate = 6.50;
      else baseRate = 9.00;
    } else if (packageDetails.preset) {
      // Calculate based on preset
      switch (packageDetails.preset) {
        case 'Small Box': baseRate = 4.50; break;
        case 'Medium Box': baseRate = 6.50; break;
        case 'Poly Mailer': baseRate = 4.00; break;
        default: baseRate = 6.50;
      }
    }

    // Return mock rates with realistic variations
    return [
      {
        carrier: 'USPS',
        service: 'Ground Advantage',
        amount: baseRate,
        currency: 'USD',
        estimatedDays: 3,
        rateId: 'usps_ground'
      },
      {
        carrier: 'USPS',
        service: 'Priority Mail',
        amount: baseRate + 2.50,
        currency: 'USD',
        estimatedDays: 2,
        rateId: 'usps_priority'
      },
      {
        carrier: 'UPS',
        service: 'Ground',
        amount: baseRate + 1.50,
        currency: 'USD',
        estimatedDays: 4,
        rateId: 'ups_ground'
      }
    ];
  } catch (error: any) {
    console.error('Error getting shipping rates:', error);
    throw error;
  }
};

/**
 * Convert listing shipping options to package details format
 */
export const convertListingToPackageDetails = (listing: any): PackageDetails | null => {
  if (!listing?.shippingOptions?.packageDetails) {
    // Fallback to legacy package size
    const packageSize = listing?.shippingOptions?.packageSize || 'medium';
    const presetMap: { [key: string]: string } = {
      small: 'Small Box',
      medium: 'Medium Box',
      large: 'Medium Box' // Use Medium Box for large as well
    };
    
    return {
      useCustom: false,
      preset: presetMap[packageSize] || 'Medium Box'
    };
  }

  const packageDetails = listing.shippingOptions.packageDetails;

  if (packageDetails.presetUsed) {
    return {
      useCustom: false,
      preset: packageDetails.presetUsed
    };
  }

  if (packageDetails.dimensions && packageDetails.weight) {
    return {
      useCustom: true,
      dimensions: packageDetails.dimensions,
      weight: packageDetails.weight
    };
  }

  // Fallback
  return {
    useCustom: false,
    preset: 'Medium Box'
  };
};

/**
 * Convert seller address from listing to shipping address format
 */
export const convertSellerAddress = (listing: any): ShippingAddress | null => {
  const sellerAddress = listing?.shippingOptions?.sellerAddress;
  
  if (!sellerAddress) {
    return null;
  }

  return {
    name: sellerAddress.name,
    street1: sellerAddress.street1,
    street2: sellerAddress.street2,
    city: sellerAddress.city,
    state: sellerAddress.state,
    zip: sellerAddress.zip,
    country: sellerAddress.country || 'US',
    phone: sellerAddress.phone
  };
};

/**
 * Convert user address to shipping address format
 */
export const convertUserAddress = (userAddress: any): ShippingAddress => {
  return {
    name: userAddress.fullName,
    street1: userAddress.line1,
    street2: userAddress.line2,
    city: userAddress.city,
    state: userAddress.state,
    zip: userAddress.zipCode,
    country: userAddress.country || 'US',
    phone: userAddress.phone
  };
};

/**
 * Estimate shipping cost based on package size (fallback)
 */
export const estimateShippingCost = (packageSize: string): { min: number; max: number } => {
  const estimates = {
    small: { min: 4.50, max: 6.00 },
    medium: { min: 6.50, max: 8.50 },
    large: { min: 8.00, max: 12.00 }
  };

  return estimates[packageSize as keyof typeof estimates] || estimates.medium;
};

/**
 * Format shipping rate for display
 */
export const formatShippingRate = (rate: ShippingRate): string => {
  const days = rate.estimatedDays;
  const dayText = days === 1 ? '1 day' : `${days} days`;
  return `${rate.carrier} ${rate.service} - $${rate.amount.toFixed(2)} (${dayText})`;
};

/**
 * Get the cheapest shipping rate
 */
export const getCheapestRate = (rates: ShippingRate[]): ShippingRate | null => {
  if (rates.length === 0) return null;
  
  return rates.reduce((cheapest, current) => 
    current.amount < cheapest.amount ? current : cheapest
  );
};
