rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isValidImage() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size <= 10 * 1024 * 1024; // 10MB max
    }
    
    // User profile pictures
    match /users/{userId}/profile/{fileName} {
      allow read;
      allow write: if isOwner(userId) && isValidImage();
    }
    
    // Listing images
    match /listings/{userId}/{fileName} {
      allow read;
      allow write: if isOwner(userId) && isValidImage();
      allow delete: if isOwner(userId) || isAdmin();
    }
    
    // Issue screenshots
    match /issues/{userId}/{fileName} {
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isOwner(userId) && isValidImage();
      allow delete: if isOwner(userId) || isAdmin();
    }

    // Chat images
    match /chats/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isOwner(userId) && isValidImage();
      allow delete: if isOwner(userId) || isAdmin();
    }
  }
}