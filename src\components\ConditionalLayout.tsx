import React from 'react';
import { useAuth } from '../hooks/useAuth';
import Layout from './Layout';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

const ConditionalLayout: React.FC<ConditionalLayoutProps> = ({ children }) => {
  const { currentUser } = useAuth();

  // If user is not logged in, render without layout
  if (!currentUser) {
    return <>{children}</>;
  }

  // Use integrated Layout for all logged-in users

  // Render appropriate layout based on user role - now using integrated Layout for all roles
  return (
    <Layout>
      {children}
    </Layout>
  );
};

export default ConditionalLayout;