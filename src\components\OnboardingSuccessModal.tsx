import React, { useEffect, useState } from 'react';
import { 
  CheckCircle, 
  DollarSign, 
  Zap, 
  ArrowRight,
  X,
  Sparkles
} from 'lucide-react';

interface OnboardingSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  pendingAmount?: number;
  orderCount?: number;
  userName?: string;
}

const OnboardingSuccessModal: React.FC<OnboardingSuccessModalProps> = ({
  isOpen,
  onClose,
  pendingAmount = 0,
  orderCount = 0,
  userName = 'there'
}) => {
  const [showAnimation, setShowAnimation] = useState(false);
  const [step, setStep] = useState<'animation' | 'details'>('animation');

  useEffect(() => {
    if (isOpen) {
      setShowAnimation(true);
      setStep('animation');
      
      // Show animation for 2 seconds, then show details
      const timer = setTimeout(() => {
        setStep('details');
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      setShowAnimation(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {step === 'animation' ? 'Setting Up...' : 'Congratulations!'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'animation' && (
            <div className="text-center py-8">
              {/* Animated Success Icon */}
              <div className="relative mx-auto mb-6">
                <div className={`w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center transition-all duration-1000 ${
                  showAnimation ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
                }`}>
                  <CheckCircle className="w-10 h-10 text-white" />
                </div>
                
                {/* Sparkle animations */}
                <div className={`absolute -top-2 -right-2 transition-all duration-1000 delay-500 ${
                  showAnimation ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
                }`}>
                  <Sparkles className="w-6 h-6 text-yellow-400 animate-pulse" />
                </div>
                <div className={`absolute -bottom-2 -left-2 transition-all duration-1000 delay-700 ${
                  showAnimation ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
                }`}>
                  <Sparkles className="w-4 h-4 text-blue-400 animate-pulse" />
                </div>
                <div className={`absolute top-0 -left-4 transition-all duration-1000 delay-300 ${
                  showAnimation ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
                }`}>
                  <Sparkles className="w-5 h-5 text-purple-400 animate-pulse" />
                </div>
              </div>

              <h3 className={`text-2xl font-bold text-gray-900 dark:text-white mb-2 transition-all duration-1000 delay-200 ${
                showAnimation ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
              }`}>
                Payment Setup Complete!
              </h3>
              
              <p className={`text-gray-600 dark:text-gray-400 transition-all duration-1000 delay-400 ${
                showAnimation ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
              }`}>
                Processing your pending payments...
              </p>
            </div>
          )}

          {step === 'details' && (
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Welcome to payouts, {userName}! 🎉
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Your Stripe account is now active and ready to receive payments.
              </p>

              {pendingAmount > 0 && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <span className="text-xl font-bold text-green-800 dark:text-green-200">
                      ${pendingAmount.toFixed(2)}
                    </span>
                  </div>
                  <p className="text-sm text-green-700 dark:text-green-300 mb-2">
                    Your pending payments are being processed!
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    From {orderCount} recent {orderCount === 1 ? 'sale' : 'sales'}
                  </p>
                </div>
              )}

              {/* What's Next */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-3">
                  What happens next?
                </h4>
                <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                  <div className="flex items-center space-x-2">
                    <Zap className="w-4 h-4" />
                    <span>Pending payments will be transferred to your account</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4" />
                    <span>Future sales will be paid out automatically</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4" />
                    <span>You can track all payments in your Stripe dashboard</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={() => window.open('https://dashboard.stripe.com', '_blank')}
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 px-6 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2"
                >
                  <span>View Stripe Dashboard</span>
                  <ArrowRight className="w-5 h-5" />
                </button>
                
                <button
                  onClick={onClose}
                  className="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-3 px-6 rounded-xl font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
                >
                  Continue to Dashboard
                </button>
              </div>

              {/* Pro Tip */}
              <div className="mt-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <p className="text-xs text-yellow-800 dark:text-yellow-200">
                  💡 <strong>Pro tip:</strong> You can view all your earnings and payout history 
                  in Settings → Payment Settings anytime.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OnboardingSuccessModal;
