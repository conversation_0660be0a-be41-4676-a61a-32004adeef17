# Hive Campus UI Screens

This directory contains the UI screens for Hive Campus, a student-exclusive marketplace app built with React Native using Expo and Tailwind CSS. The design follows a liquid glass aesthetic with blur effects and modern UI principles.

## Design System

The design system is defined in `src/design-system/HiveCampusDesignSystem.md` and includes:

- **Color Palette**: Primary blue, accent orange, success green, and other utility colors
- **Typography**: Font families, sizes, and weights
- **Spacing System**: Based on a 4px grid
- **Border Radius**: Small (6px), medium (12px), large (16px), XLarge (24px), and pill (9999px)
- **Shadows**: Various shadow levels for different elevations
- **Animations**: Fade-in, slide-up, scale-in, and bounce effects
- **Core Components**: Buttons, cards, form elements, and navigation components

## Screens

### Authentication Screens

- **LoginScreen**: User login with email/password and social login options
- **SignupScreen**: Multi-step user registration with verification

### Marketplace Screens

- **ProductDetailScreen**: Detailed view of a product listing with image gallery, seller info, and purchase options
- **CheckoutScreen**: Complete purchase flow with shipping, payment, and order confirmation
- **ProfileScreen**: User profile with listings, sales history, and account management

## Key Features

### Liquid Glass Aesthetic

The UI uses a liquid glass aesthetic with:

- Semi-transparent backgrounds with backdrop blur
- Subtle borders for depth
- Smooth animations and transitions
- Gradient backgrounds for visual interest

### Mobile-First Design

All screens are designed with a mobile-first approach:

- Responsive layouts that adapt to different screen sizes
- Touch-friendly interactive elements
- Bottom navigation for mobile users
- Sidebar navigation for desktop users

### Dark Mode Support

All components support dark mode with:

- Dark mode variants for all colors
- Appropriate contrast ratios for accessibility
- Consistent visual hierarchy in both light and dark modes

### Micro-interactions

The UI includes subtle micro-interactions:

- Button hover and active states
- Card hover effects
- Form field focus states
- Loading and transition animations

## Implementation Notes

- The screens use Tailwind CSS for styling
- The liquid glass effect is achieved using backdrop-filter and semi-transparent backgrounds
- SVG icons from Lucide React are used throughout the UI
- All screens are responsive and work on mobile, tablet, and desktop

## Usage

Import these screens into your React Native application and connect them to your navigation system. Make sure to install the required dependencies and set up the appropriate context providers for authentication, theme, etc.

```jsx
import LoginScreen from './screens/auth/LoginScreen';
import SignupScreen from './screens/auth/SignupScreen';
import ProductDetailScreen from './screens/product/ProductDetailScreen';
import CheckoutScreen from './screens/checkout/CheckoutScreen';
import ProfileScreen from './screens/profile/ProfileScreen';

// Use with your navigation system
```