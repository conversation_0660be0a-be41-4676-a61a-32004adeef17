import React, { useState } from 'react';
import { Mail, Phone, MapPin, Clock, Send, MessageCircle, Users, Headphones } from 'lucide-react';


const MerchantContactUs: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    category: ''
  });

  const contactMethods = [
    {
      icon: Mail,
      title: 'Partner Support',
      description: 'Get help with your merchant account',
      contact: '<EMAIL>',
      responseTime: 'Within 12 hours'
    },
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Chat with our partner success team',
      contact: 'Available 9 AM - 6 PM PST',
      responseTime: 'Instant response'
    },
    {
      icon: Phone,
      title: 'Partner Hotline',
      description: 'Call our dedicated partner line',
      contact: '+1 (555) 123-PARTNER',
      responseTime: 'Mon-Fri, 9 AM - 6 PM PST'
    }
  ];

  const departments = [
    {
      icon: Headphones,
      title: 'Partner Support',
      description: 'Account issues, technical problems, billing questions',
      email: '<EMAIL>'
    },
    {
      icon: Users,
      title: 'Business Development',
      description: 'Partnership opportunities, collaboration inquiries',
      email: '<EMAIL>'
    },
    {
      icon: MessageCircle,
      title: 'Marketing Support',
      description: 'Promotional campaigns, advertising assistance',
      email: '<EMAIL>'
    }
  ];

  const categories = [
    'General Inquiry',
    'Technical Support',
    'Account Issues',
    'Billing Question',
    'Partnership Opportunity',
    'Marketing Support',
    'Other'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Contact form submitted:', formData);
    alert('Thank you for contacting us! We\'ll get back to you soon.');
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: '',
      category: ''
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="w-16 h-16 bg-gradient-to-r from-accent-500 to-orange-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
              <MessageCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Partner Support</h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              We're here to help you succeed! Reach out to our dedicated partner support team.
            </p>
          </div>

          {/* Contact Methods */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon;
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 text-center hover:shadow-xl transition-all duration-300">
                  <div className="w-16 h-16 bg-accent-100 dark:bg-accent-900/20 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                    <IconComponent className="w-8 h-8 text-accent-600 dark:text-accent-400" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">{method.title}</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">{method.description}</p>
                  <p className="font-semibold text-accent-600 dark:text-accent-400 mb-2">{method.contact}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-500">{method.responseTime}</p>
                </div>
              );
            })}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Send us a Message</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      placeholder="Your full name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Category
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    required
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Subject
                  </label>
                  <input
                    type="text"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Brief description of your inquiry"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Message
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                    placeholder="Please provide details about your inquiry..."
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-accent-600 to-accent-700 text-white py-4 rounded-xl font-bold text-lg hover:from-accent-700 hover:to-accent-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg flex items-center justify-center space-x-2"
                >
                  <Send className="w-5 h-5" />
                  <span>Send Message</span>
                </button>
              </form>
            </div>

            {/* Department Info & Office Hours */}
            <div className="space-y-8">
              {/* Departments */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Departments</h2>
                <div className="space-y-6">
                  {departments.map((dept, index) => {
                    const IconComponent = dept.icon;
                    return (
                      <div key={index} className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-accent-100 dark:bg-accent-900/20 rounded-xl flex items-center justify-center flex-shrink-0">
                          <IconComponent className="w-6 h-6 text-accent-600 dark:text-accent-400" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{dept.title}</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{dept.description}</p>
                          <a
                            href={`mailto:${dept.email}`}
                            className="text-sm text-accent-600 dark:text-accent-400 hover:underline"
                          >
                            {dept.email}
                          </a>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Office Hours */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <Clock className="w-6 h-6 text-accent-600 dark:text-accent-400" />
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Partner Support Hours</h2>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Monday - Friday</span>
                    <span className="font-semibold text-gray-900 dark:text-white">9:00 AM - 6:00 PM PST</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Saturday</span>
                    <span className="font-semibold text-gray-900 dark:text-white">10:00 AM - 4:00 PM PST</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Sunday</span>
                    <span className="font-semibold text-gray-900 dark:text-white">Closed</span>
                  </div>
                </div>
                <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl">
                  <p className="text-sm text-blue-800 dark:text-blue-300">
                    <strong>Priority Support:</strong> As a verified partner, you have access to priority support with faster response times.
                  </p>
                </div>
              </div>

              {/* Location */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <MapPin className="w-6 h-6 text-accent-600 dark:text-accent-400" />
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Our Location</h2>
                </div>
                <div className="space-y-2">
                  <p className="font-semibold text-gray-900 dark:text-white">Hive Campus HQ</p>
                  <p className="text-gray-600 dark:text-gray-400">
                    123 Innovation Drive<br />
                    Palo Alto, CA 94301<br />
                    United States
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default MerchantContactUs;