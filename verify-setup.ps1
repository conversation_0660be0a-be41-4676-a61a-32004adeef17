# Hive Campus Setup Verification Script
param(
    [switch]$Detailed
)

Write-Host "🔍 Verifying Hive Campus setup..." -ForegroundColor Cyan

$ErrorCount = 0

# Function to write colored output
function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Status
    )
    
    if ($Status -eq "Success") {
        Write-Host "✅ $Message" -ForegroundColor Green
    } elseif ($Status -eq "Error") {
        Write-Host "❌ $Message" -ForegroundColor Red
        $script:ErrorCount++
    } elseif ($Status -eq "Warning") {
        Write-Host "⚠️  $Message" -ForegroundColor Yellow
    } else {
        Write-Host "ℹ️  $Message" -ForegroundColor Blue
    }
}

# Check Node.js version
Write-Host "`n🔍 Checking Node.js version..." -ForegroundColor Blue
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-ColoredOutput "Node.js version: $nodeVersion" "Success"
    } else {
        Write-ColoredOutput "Node.js is not installed" "Error"
    }
} catch {
    Write-ColoredOutput "Node.js is not installed" "Error"
}

# Check npm
Write-Host "`n🔍 Checking npm..." -ForegroundColor Blue
try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-ColoredOutput "npm version: $npmVersion" "Success"
    } else {
        Write-ColoredOutput "npm is not available" "Error"
    }
} catch {
    Write-ColoredOutput "npm is not available" "Error"
}

# Check Firebase CLI
Write-Host "`n🔍 Checking Firebase CLI..." -ForegroundColor Blue
try {
    $firebaseVersion = firebase --version 2>$null
    if ($firebaseVersion) {
        Write-ColoredOutput "Firebase CLI: $firebaseVersion" "Success"
    } else {
        Write-ColoredOutput "Firebase CLI is not installed" "Error"
        Write-Host "   Install with: npm install -g firebase-tools" -ForegroundColor Yellow
    }
} catch {
    Write-ColoredOutput "Firebase CLI is not installed" "Error"
    Write-Host "   Install with: npm install -g firebase-tools" -ForegroundColor Yellow
}

# Check project dependencies
Write-Host "`n🔍 Checking project dependencies..." -ForegroundColor Blue
if ((Test-Path "package.json") -and (Test-Path "node_modules")) {
    Write-ColoredOutput "Frontend dependencies installed" "Success"
} else {
    Write-ColoredOutput "Frontend dependencies missing" "Error"
    Write-Host "   Run: npm install" -ForegroundColor Yellow
}

if ((Test-Path "functions/package.json") -and (Test-Path "functions/node_modules")) {
    Write-ColoredOutput "Functions dependencies installed" "Success"
} else {
    Write-ColoredOutput "Functions dependencies missing" "Error"
    Write-Host "   Run: cd functions; npm install" -ForegroundColor Yellow
}

# Check TypeScript compilation
Write-Host "`n🔍 Checking TypeScript compilation..." -ForegroundColor Blue
Push-Location "functions"
try {
    $buildResult = npm run build 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-ColoredOutput "Functions TypeScript compilation successful" "Success"
    } else {
        Write-ColoredOutput "Functions TypeScript compilation failed" "Error"
    }
} catch {
    Write-ColoredOutput "Functions TypeScript compilation failed" "Error"
}
Pop-Location

# Check frontend build
Write-Host "`n🔍 Checking frontend build..." -ForegroundColor Blue
try {
    $buildResult = npm run build 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-ColoredOutput "Frontend build successful" "Success"
    } else {
        Write-ColoredOutput "Frontend build failed" "Error"
    }
} catch {
    Write-ColoredOutput "Frontend build failed" "Error"
}

# Check configuration files
Write-Host "`n🔍 Checking configuration files..." -ForegroundColor Blue

$ConfigFiles = @(
    "firebase.json",
    "firestore.rules", 
    "storage.rules",
    "functions/src/config/environment.ts",
    ".github/workflows/deploy.yml",
    ".github/workflows/staging.yml"
)

foreach ($file in $ConfigFiles) {
    if (Test-Path $file) {
        Write-ColoredOutput "$file exists" "Success"
    } else {
        Write-ColoredOutput "$file missing" "Error"
    }
}

# Check environment configuration
Write-Host "`n🔍 Checking environment configuration..." -ForegroundColor Blue
if (Test-Path "functions/.env.example") {
    Write-ColoredOutput "Environment template exists" "Success"
} else {
    Write-ColoredOutput "Environment template missing" "Error"
}

# Check deployment scripts
Write-Host "`n🔍 Checking deployment scripts..." -ForegroundColor Blue
$DeploymentFiles = @(
    "deploy.sh",
    "setup-production.sh",
    "verify-setup.ps1"
)

foreach ($file in $DeploymentFiles) {
    if (Test-Path $file) {
        Write-ColoredOutput "$file exists" "Success"
    } else {
        Write-ColoredOutput "$file missing" "Warning"
    }
}

# Summary
Write-Host "`n=======================" -ForegroundColor Cyan
Write-Host "  VERIFICATION SUMMARY" -ForegroundColor Cyan
Write-Host "=======================" -ForegroundColor Cyan

if ($ErrorCount -eq 0) {
    Write-Host "`n🎉 All checks passed! Your setup is ready for deployment." -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Blue
    Write-Host "1. Set up your Firebase project: .\setup-production.sh" -ForegroundColor White
    Write-Host "2. Configure environment variables" -ForegroundColor White
    Write-Host "3. Deploy: .\deploy.sh --environment production" -ForegroundColor White
} else {
    Write-Host "`n❌ Found $ErrorCount error(s). Please fix the issues above before deploying." -ForegroundColor Red
    exit 1
}

Write-Host "`n🚀 Ready for production deployment!" -ForegroundColor Yellow

if ($Detailed) {
    Write-Host "`n📋 Detailed System Information:" -ForegroundColor Blue
    Write-Host "OS: $([System.Environment]::OSVersion.VersionString)" -ForegroundColor White
    Write-Host "PowerShell: $($PSVersionTable.PSVersion)" -ForegroundColor White
    Write-Host "Current Directory: $(Get-Location)" -ForegroundColor White
}