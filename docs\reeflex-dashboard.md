# ReeFlex Dashboard Documentation

## Overview

The ReeFlex Dashboard provides administrators with a comprehensive view of application health, user behavior, and feedback. It leverages the data collected by the ReeFlex observability system to generate actionable insights and help identify issues before they impact users.

## Features

### 1. Intelligence Reports

- **AI-Powered Summaries**: Daily reports generated with OpenAI that analyze app usage patterns, errors, and feedback
- **Key Metrics**: Overview of page views, errors, feedback, and user interactions
- **Trend Analysis**: Identification of patterns and anomalies in user behavior

### 2. Activity Monitoring

- **Real-time Activity Feed**: View recent user actions, errors, and page views
- **Filtering**: Filter activity by event type and route
- **Error Tracking**: Detailed information about JavaScript errors, API failures, and performance issues

### 3. Feedback Analysis

- **User Sentiment**: Breakdown of positive, negative, and neutral feedback
- **Category Analysis**: Identification of common feedback categories
- **User Quotes**: Direct feedback from users to understand their experience

### 4. Confusion Points

- **Problem Areas**: Pages with the most negative feedback
- **Common Issues**: Frequently reported problems and confusion points
- **Actionable Insights**: Clear indicators of where to focus improvement efforts

## Implementation Details

### Data Sources

The dashboard pulls data from three Firestore collections:

1. `reeflex_activity`: User interactions, page views, errors, and performance metrics
2. `reeflex_feedback`: Structured user feedback with categories and sentiment
3. `reeflex_reports`: Daily AI-generated reports and insights

### Real-time Updates

The dashboard uses Firestore queries to fetch the most recent data:

```typescript
// Fetch latest report
const reportsQuery = query(
  collection(firestore, 'reeflex_reports'),
  orderBy('timestamp', 'desc'),
  limit(1)
);

// Fetch recent activity
const activityQuery = query(
  collection(firestore, 'reeflex_activity'),
  orderBy('timestamp', 'desc'),
  limit(50)
);

// Fetch recent feedback
const feedbackQuery = query(
  collection(firestore, 'reeflex_feedback'),
  orderBy('timestamp', 'desc'),
  limit(20)
);
```

### Security

The dashboard is protected with role-based access control:

```typescript
// In App.tsx
<Route path="/admin/reeflex" element={
  <ProtectedRoute allowedRoles={['admin']}>
    <ReeFlexDashboard />
  </ProtectedRoute>
} />

// In ReeFlexDashboard.tsx
useEffect(() => {
  // Redirect if not admin
  if (!isLoading && !isAdmin) {
    navigate('/');
    return;
  }
  
  // Fetch data...
}, [isAdmin, isLoading, navigate]);
```

### Filtering and Visualization

The dashboard provides filtering capabilities to help administrators focus on specific issues:

- Filter by event type (errors, page views, interactions, performance)
- Filter by route to analyze specific pages
- Expandable/collapsible sections for better organization

## Backend Services

### Daily Reports

The `sendReeFlexReport` Firebase function runs daily to generate reports:

1. Collects the last 24 hours of activity and feedback data
2. Aggregates statistics on errors, performance, user behavior, and feedback
3. Uses OpenAI to generate an intelligent summary with actionable insights
4. Stores the report in Firestore and sends an email notification

### Real-time Alerts

Several scheduled functions monitor for critical issues:

1. `checkForCriticalErrors`: Monitors for clusters of errors (5+ of the same type)
2. `checkForPerformanceIssues`: Identifies slow pages and resources
3. `checkForPaymentFailures`: Detects payment processing issues
4. `checkForFeedbackPatterns`: Identifies patterns in negative feedback

When issues are detected, alerts are sent via Slack and email with detailed context.

## Usage Guide

1. **Accessing the Dashboard**: Navigate to `/admin/reeflex` while logged in as an admin
2. **Reviewing Reports**: Check the latest AI summary for a high-level overview
3. **Investigating Issues**: Use the activity feed and filtering to drill down into specific problems
4. **Analyzing Feedback**: Review user feedback to understand pain points
5. **Identifying Trends**: Look for patterns in the confusion points section

## Future Enhancements

Planned improvements for the ReeFlex dashboard:

1. **Interactive Charts**: Visual representations of trends over time
2. **User Session Replay**: Ability to reconstruct and replay user sessions
3. **Anomaly Detection**: Automatic highlighting of unusual patterns
4. **Predictive Analytics**: Forecasting of potential issues before they occur
5. **Integration with CI/CD**: Correlation of issues with recent deployments