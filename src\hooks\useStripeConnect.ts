import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { Timestamp } from 'firebase/firestore';

interface StripeAccountStatus {
  accountId: string;
  onboardingUrl?: string;
  dashboardUrl?: string;
  isOnboarded: boolean;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
}

interface PendingPayout {
  orderId: string;
  amount: number;
  commissionAmount: number;
  sellerAmount: number;
  paymentIntentId: string;
  createdAt: Timestamp | Date | string;
}

interface UseStripeConnectReturn {
  // Account status
  accountStatus: StripeAccountStatus | null;
  isLoading: boolean;
  error: string | null;

  // Pending payouts
  pendingPayouts: PendingPayout[];
  totalPendingAmount: number;
  pendingOrderCount: number;

  // Actions
  createAccount: (accountType: 'student' | 'merchant') => Promise<{ accountId: string; onboardingUrl: string }>;
  getOnboardingLink: (refreshUrl?: string, returnUrl?: string) => Promise<{ onboardingUrl: string }>;
  refreshAccountStatus: () => Promise<void>;
  refreshPendingPayouts: () => Promise<void>;
  loadInitialData: () => Promise<void>;

  // Utilities
  checkOnboardingStatus: () => Promise<boolean>;
}



export const useStripeConnect = (options?: { autoLoad?: boolean }): UseStripeConnectReturn => {
  const { userProfile } = useAuth();
  const { autoLoad = false } = options || {};
  const [accountStatus, setAccountStatus] = useState<StripeAccountStatus | null>(null);
  const [pendingPayouts, setPendingPayouts] = useState<PendingPayout[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const [isRateLimited, setIsRateLimited] = useState(false);

  // Firebase Functions
  const createStripeConnectAccount = httpsCallable(functions, 'createStripeConnectAccount');
  const getStripeConnectOnboardingLink = httpsCallable(functions, 'getStripeConnectOnboardingLink');
  const getStripeConnectAccountStatus = httpsCallable(functions, 'getStripeConnectAccountStatus');
  const getSellerPendingPayouts = httpsCallable(functions, 'getSellerPendingPayouts');



  // Real Stripe Connect data will be fetched from live API

  // Calculate totals
  const totalPendingAmount = pendingPayouts.reduce((total, payout) => total + payout.sellerAmount, 0);
  const pendingOrderCount = pendingPayouts.length;

  // Create Stripe Connect account
  const createAccount = useCallback(async (accountType: 'student' | 'merchant') => {
    if (!userProfile) {
      throw new Error('User not authenticated');
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await createStripeConnectAccount({ accountType });
      const data = result.data as { accountId: string; onboardingUrl: string };

      // Refresh account status after creation
      await refreshAccountStatus();

      // Return with consistent naming
      return {
        accountId: data.accountId,
        onboardingUrl: data.onboardingUrl
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create account';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [userProfile, createStripeConnectAccount]);

  // Get onboarding link
  const getOnboardingLink = useCallback(async (refreshUrl?: string, returnUrl?: string) => {
    if (!userProfile) {
      throw new Error('User not authenticated');
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await getStripeConnectOnboardingLink({ refreshUrl, returnUrl });
      const data = result.data as { onboardingUrl: string };
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get onboarding link';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [userProfile, getStripeConnectOnboardingLink]);

  // Refresh account status with rate limiting
  const refreshAccountStatus = useCallback(async () => {
    if (!userProfile) {
      setAccountStatus(null);
      return;
    }

    // Rate limiting: prevent calls more frequent than every 5 seconds
    const now = Date.now();
    if (now - lastFetchTime < 5000) {
      console.log('Rate limited: skipping account status fetch');
      return;
    }

    if (isRateLimited) {
      console.log('Currently rate limited, skipping fetch');
      return;
    }

    setIsLoading(true);
    setError(null);
    setLastFetchTime(now);

    try {
      console.log('Fetching Stripe Connect account status for user:', userProfile.uid);
      const result = await getStripeConnectAccountStatus({});
      console.log('Stripe Connect account status result:', result);
      const data = result.data as StripeAccountStatus | null;
      setAccountStatus(data);
      setIsRateLimited(false); // Reset rate limit on success
    } catch (error: any) {
      console.error('Error fetching account status:', error);

      // Handle specific Firebase Function errors
      if (error?.code === 'functions/unauthenticated') {
        setError('Authentication required. Please log in again.');
      } else if (error?.code === 'functions/internal') {
        setError('Service temporarily unavailable. Please try again later.');
        setIsRateLimited(true); // Rate limit on internal errors
        setTimeout(() => setIsRateLimited(false), 30000); // Reset after 30 seconds
      } else if (error?.code === 'functions/not-found') {
        setError('Stripe Connect service not available.');
      } else if (error?.message?.includes('ERR_INSUFFICIENT_RESOURCES')) {
        setError('Service overloaded. Please try again in a moment.');
        setIsRateLimited(true);
        setTimeout(() => setIsRateLimited(false), 60000); // Reset after 1 minute
      } else {
        setError('Failed to load account status. Please try again.');
      }

      setAccountStatus(null);
    } finally {
      setIsLoading(false);
    }
  }, [userProfile, getStripeConnectAccountStatus, lastFetchTime, isRateLimited]);

  // Refresh pending payouts with rate limiting
  const refreshPendingPayouts = useCallback(async () => {
    if (!userProfile) {
      setPendingPayouts([]);
      return;
    }

    // Rate limiting: prevent calls more frequent than every 5 seconds
    const now = Date.now();
    if (now - lastFetchTime < 5000 || isRateLimited) {
      console.log('Rate limited: skipping pending payouts fetch');
      return;
    }

    try {
      const result = await getSellerPendingPayouts({});
      const data = result.data as PendingPayout[];
      setPendingPayouts(data || []);
    } catch (error: any) {
      console.error('Error fetching pending payouts:', error);

      // Handle resource errors
      if (error?.message?.includes('ERR_INSUFFICIENT_RESOURCES') || error?.code === 'functions/internal') {
        setIsRateLimited(true);
        setTimeout(() => setIsRateLimited(false), 30000); // Reset after 30 seconds
      }

      setPendingPayouts([]);
    }
  }, [userProfile, getSellerPendingPayouts, lastFetchTime, isRateLimited]);

  // Load initial data (account status and pending payouts)
  const loadInitialData = useCallback(async () => {
    if (userProfile) {
      await Promise.all([
        refreshAccountStatus(),
        refreshPendingPayouts()
      ]);
    }
  }, [userProfile, refreshAccountStatus, refreshPendingPayouts]);

  // Auto-load data if autoLoad is enabled
  useEffect(() => {
    if (autoLoad && userProfile) {
      loadInitialData();
    }
  }, [autoLoad, userProfile]); // Remove loadInitialData from dependencies to prevent infinite loop

  // Check onboarding status
  const checkOnboardingStatus = useCallback(async (): Promise<boolean> => {
    if (!accountStatus) {
      await refreshAccountStatus();
    }
    return accountStatus?.isOnboarded || false;
  }, [accountStatus, refreshAccountStatus]);

  // Load initial data when user changes (but not if there's already a network error)
  // Reset data when user logs out
  useEffect(() => {
    if (!userProfile) {
      setAccountStatus(null);
      setPendingPayouts([]);
      setError(null);
    }
  }, [userProfile]);

  return {
    // Account status
    accountStatus,
    isLoading,
    error,
    
    // Pending payouts
    pendingPayouts,
    totalPendingAmount,
    pendingOrderCount,
    
    // Actions
    createAccount,
    getOnboardingLink,
    refreshAccountStatus,
    refreshPendingPayouts,
    loadInitialData,

    // Utilities
    checkOnboardingStatus,
  };
};
