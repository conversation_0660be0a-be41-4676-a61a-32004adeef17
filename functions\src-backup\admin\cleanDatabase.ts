import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Clean database function - removes all fake data
export const cleanDatabase = functions.https.onCall(async (data, context) => {
  try {
    // Verify admin access
    if (!context.auth || !context.auth.token.admin) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can clean the database'
      );
    }

    const db = admin.firestore();
    const auth = admin.auth();
    
    console.log('Starting database cleanup...');

    // Delete all listings
    const listingsSnapshot = await db.collection('listings').get();
    const listingDeletePromises = listingsSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(listingDeletePromises);
    console.log(`Deleted ${listingsSnapshot.size} listings`);

    // Delete all chats
    const chatsSnapshot = await db.collection('chats').get();
    const chatDeletePromises = chatsSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(chatDeletePromises);
    console.log(`Deleted ${chatsSnapshot.size} chats`);

    // Delete all orders
    const ordersSnapshot = await db.collection('orders').get();
    const orderDeletePromises = ordersSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(orderDeletePromises);
    console.log(`Deleted ${ordersSnapshot.size} orders`);

    // Delete all feedback
    const feedbackSnapshot = await db.collection('feedback').get();
    const feedbackDeletePromises = feedbackSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(feedbackDeletePromises);
    console.log(`Deleted ${feedbackSnapshot.size} feedback entries`);

    // Get all users except admins
    const usersSnapshot = await db.collection('users').get();
    const nonAdminUsers = usersSnapshot.docs.filter(doc => {
      const userData = doc.data();
      return userData.role !== 'admin';
    });

    // Delete user accounts from Auth and Firestore
    for (const userDoc of nonAdminUsers) {
      try {
        await auth.deleteUser(userDoc.id);
        await userDoc.ref.delete();
      } catch (error) {
        console.error(`Error deleting user ${userDoc.id}:`, error);
      }
    }
    console.log(`Deleted ${nonAdminUsers.length} user accounts`);

    return {
      success: true,
      message: 'Database cleaned successfully',
      deletedCounts: {
        listings: listingsSnapshot.size,
        chats: chatsSnapshot.size,
        orders: ordersSnapshot.size,
        feedback: feedbackSnapshot.size,
        users: nonAdminUsers.length
      }
    };

  } catch (error) {
    console.error('Error cleaning database:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to clean database',
      error
    );
  }
});




