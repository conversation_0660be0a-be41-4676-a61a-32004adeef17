import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Star, Users, Shield, Zap, TrendingUp, Heart, Eye } from 'lucide-react';
import { Link } from 'react-router-dom';
import AnimatedButton from '../components/AnimatedButton';

const LandingPage: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const heroSlides = [
    {
      id: 1,
      title: 'Buy & Sell with Fellow Students',
      subtitle: 'The trusted marketplace for college communities',
      image: 'https://images.pexels.com/photos/1438081/pexels-photo-1438081.jpeg?auto=compress&cs=tinysrgb&w=1200',
      cta: 'Start Shopping',
      highlight: 'Over 50,000 verified students'
    },
    {
      id: 2,
      title: 'Textbooks at Unbeatable Prices',
      subtitle: 'Save up to 80% on textbooks from students',
      image: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=1200',
      cta: 'Browse Textbooks',
      highlight: 'Average savings: $200/semester'
    },
    {
      id: 3,
      title: 'Tech & Electronics Hub',
      subtitle: 'Find the latest gadgets from trusted sellers',
      image: 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=1200',
      cta: 'Shop Electronics',
      highlight: 'All items verified & guaranteed'
    },
    {
      id: 4,
      title: 'Campus Fashion Exchange',
      subtitle: 'Discover unique styles from your campus',
      image: 'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=1200',
      cta: 'Explore Fashion',
      highlight: 'Trending styles from 200+ campuses'
    }
  ];

  // TODO: Fetch real featured listings from Firebase
  const featuredListings: any[] = [];

  const stats = [
    { number: '50K+', label: 'Verified Students', icon: Users },
    { number: '200+', label: 'Universities', icon: Shield },
    { number: '1M+', label: 'Items Sold', icon: TrendingUp },
    { number: '4.9★', label: 'Average Rating', icon: Star }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 overflow-x-hidden">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 w-full">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <img
                src="/hive-campus-logo.svg"
                alt="Hive Campus Logo"
                className="w-8 h-8"
              />
              <span className="text-xl font-bold text-gray-900 dark:text-white">Hive Campus</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link 
                to="/login-type" 
                className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white font-medium"
              >
                Sign In
              </Link>
              <Link to="/login-type">
                <AnimatedButton>
                  Get Started
                </AnimatedButton>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Carousel */}
      <div className="relative h-[600px] overflow-hidden">
        {heroSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
              index === currentSlide ? 'translate-x-0' : 
              index < currentSlide ? '-translate-x-full' : 'translate-x-full'
            }`}
          >
            <div className="relative h-full">
              <img
                src={slide.image}
                alt={slide.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white max-w-4xl mx-auto px-2 sm:px-4 w-full">
                  <div className="mb-4">
                    <span className="bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                      {slide.highlight}
                    </span>
                  </div>
                  <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
                    {slide.title}
                  </h1>
                  <p className="text-xl md:text-2xl mb-8 text-gray-200 animate-slide-up">
                    {slide.subtitle}
                  </p>
                  <Link to="/login-type" className="inline-block">
                    <AnimatedButton className="px-8 py-4 text-lg font-bold">
                      {slide.cta}
                    </AnimatedButton>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Carousel Controls */}
        <button
          onClick={prevSlide}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
        >
          <ChevronRight className="w-6 h-6" />
        </button>

        {/* Carousel Indicators */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all ${
                index === currentSlide ? 'bg-white' : 'bg-white/50'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-gray-50 dark:bg-gray-800 py-16">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 w-full">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                    <IconComponent className="w-8 h-8 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {stat.number}
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">{stat.label}</p>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Featured Listings */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 w-full">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Trending on Campus
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto px-2">
              Discover the hottest deals from verified students across top universities
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredListings.map((listing) => (
              <div key={listing.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden group">
                <div className="relative overflow-hidden">
                  <img
                    src={listing.image}
                    alt={listing.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-success-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {Math.round(((listing.originalPrice - listing.price) / listing.originalPrice) * 100)}% OFF
                    </span>
                  </div>
                  <div className="absolute top-4 right-4 flex space-x-2">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                      <Heart className="w-3 h-3 text-red-500" />
                      <span className="text-xs font-medium">{listing.likes}</span>
                    </div>
                    <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                      <Eye className="w-3 h-3 text-gray-600" />
                      <span className="text-xs font-medium">{listing.views}</span>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2">
                    {listing.title}
                  </h3>
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                      ${listing.price}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                      ${listing.originalPrice}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{listing.seller}</p>
                        <div className="flex items-center">
                          <Star className="w-3 h-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">{listing.rating}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
                        {listing.condition}
                      </span>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{listing.university}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              to="/login-type"
              className="inline-block bg-gradient-to-r from-primary-600 to-primary-700 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-primary-700 hover:to-primary-800 transform hover:scale-105 transition-all duration-200 shadow-lg"
            >
              View All Listings
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gray-50 dark:bg-gray-800 py-16">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 w-full">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Why Choose Hive Campus?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400">
              The safest and most trusted way to buy and sell on campus
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900/20 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                <Shield className="w-10 h-10 text-primary-600 dark:text-primary-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Verified Students Only</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Every user is verified with their .edu email address, ensuring you're only dealing with real students from your campus community.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-success-100 dark:bg-success-900/20 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                <Zap className="w-10 h-10 text-success-600 dark:text-success-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Lightning Fast</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Find what you need instantly with our smart search and campus-specific filters. Get items delivered or pick them up the same day.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-accent-100 dark:bg-accent-900/20 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                <TrendingUp className="w-10 h-10 text-accent-600 dark:text-accent-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Best Prices</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Save up to 80% compared to retail prices. Students offer the best deals because they understand the student budget.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-primary-600 via-primary-700 to-accent-600 py-16">
        <div className="max-w-4xl mx-auto text-center px-2 sm:px-4 lg:px-6 xl:px-8 w-full">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Join Your Campus Community?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of students who are already buying, selling, and saving on Hive Campus
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/login-type">
              <AnimatedButton className="px-8 py-4 text-lg font-bold">
                Get Started Free
              </AnimatedButton>
            </Link>
            <Link
              to="/login-type"
              className="border-2 border-white text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white hover:text-primary-600 transform hover:scale-105 transition-all duration-200"
            >
              Sign In
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 w-full">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <img
                  src="/hive-campus-logo.svg"
                  alt="Hive Campus Logo"
                  className="w-8 h-8"
                />
                <span className="text-xl font-bold">Hive Campus</span>
              </div>
              <p className="text-gray-400">
                The trusted marketplace connecting students across universities nationwide.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">For Students</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">How it Works</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Safety Tips</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Student Verification</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Campus Pickup</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Report Issue</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Guidelines</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Hive Campus. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;