import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Package,
  Truck,
  CheckCircle,
  MapPin,
  User,
  MessageCircle,
  Phone,
  TestTube,
  Star,
  AlertCircle,
  Calendar,
  Loader,
  Lock,
  Check
} from 'lucide-react';
import { useStripeCheckout } from '../hooks';
import { useAuth } from '../hooks/useAuth';
import { useLiveTracking } from '../hooks/useLiveTracking';
import { OrderStatus } from '../firebase/types';
import { logCodeConfirmed } from '../utils/adminLogger';

const OrderTracking: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const { userProfile } = useAuth();
  const { releaseFundsWithCode } = useStripeCheckout();

  // Use live tracking hook for real-time updates
  const {
    order,
    trackingUpdates: _trackingUpdates,
    isLoading: _isTrackingLoading,
    error: _trackingError,
    simulateDelivery
  } = useLiveTracking(orderId || '');

  const [showContactSeller, setShowContactSeller] = useState(false);
  const [secretCode, setSecretCode] = useState('');
  const [isSubmittingCode, setIsSubmittingCode] = useState(false);
  const [codeError, setCodeError] = useState<string | null>(null);
  const [codeSuccess, setCodeSuccess] = useState(false);

  // Utility function to safely convert dates (handles both Firestore Timestamps and ISO strings)
  const safeToDate = (dateValue: any): Date => {
    if (!dateValue) return new Date();

    // If it's a Firestore Timestamp, use toDate()
    if (dateValue && typeof dateValue.toDate === 'function') {
      return dateValue.toDate();
    }

    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue;
    }

    // If it's a string (ISO string), parse it
    if (typeof dateValue === 'string') {
      return new Date(dateValue);
    }

    // Fallback to current date
    return new Date();
  };

  // Handle secret code submission
  const handleSubmitCode = async () => {
    if (!orderId || !secretCode || secretCode.length !== 6) {
      setCodeError('Please enter a valid 6-digit code');
      return;
    }

    setIsSubmittingCode(true);
    setCodeError(null);

    try {
      // Handle mock orders for testing
      if (orderId.startsWith('order_test_') && order) {
        // Check if the entered code matches the mock delivery code
        if (secretCode === order.deliveryCode) {
          setCodeSuccess(true);

          // Update mock order status
          const updatedOrder = {
            ...order,
            status: 'completed',
            completedAt: new Date().toISOString()
          };

          localStorage.setItem(`mock_order_${orderId}`, JSON.stringify(updatedOrder));

          // Show success message for testing
          alert('🎉 Mock delivery code verified successfully!\n\nFunds would be released to seller in production.');
          return;
        } else {
          setCodeError('Invalid delivery code. Please check the code and try again.');
          return;
        }
      }

      // For real orders, use the existing function
      const success = await releaseFundsWithCode(orderId, secretCode);

      if (success) {
        setCodeSuccess(true);

        // Log the code confirmation for admin audit
        if (order) {
          await logCodeConfirmed(orderId, order.buyerId, {
            secretCode: secretCode,
            orderAmount: order.amount,
            confirmationTime: new Date().toISOString()
          });
        }
      }
    } catch (error: unknown) {
      console.error('Error submitting code:', error);
      setCodeError(error instanceof Error ? error.message : 'Failed to verify code');
    } finally {
      setIsSubmittingCode(false);
    }
  };

  // Enhanced order data from Firebase
  const orderData = {
    id: orderId || order?.id || 'Unknown',
    status: order?.status || 'pending' as OrderStatus,
    orderType: order?.orderType || 'buy',
    rentalPeriod: order?.rentalPeriod,
    bidAmount: order?.bidAmount,
    shippingFee: order?.shippingFee || 0,
    item: {
      title: order?.title || 'Item',
      price: order?.amount || 0,
      image: '/placeholder-image.jpg', // TODO: Add image URL to order data
      condition: 'Unknown' // TODO: Add condition to order data
    },
    seller: {
      name: 'Seller', // TODO: Add seller info to order data
      avatar: '/placeholder-avatar.jpg',
      rating: 4.5, // TODO: Get real seller rating
      phone: 'Not provided',
      verified: true
    },
    buyer: {
      name: 'Buyer', // TODO: Add buyer info to order data
      address: order?.shippingAddress ?
        `${order.shippingAddress.name}\n${order.shippingAddress.line1}${order.shippingAddress.line2 ? '\n' + order.shippingAddress.line2 : ''}\n${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postalCode}` :
        'Address not provided'
    },
    orderDate: safeToDate(order?.createdAt).toISOString(),
    estimatedDelivery: safeToDate(order?.trackingInfo?.estimatedDeliveryDate || new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)).toISOString(),
    deliveryMethod: order?.deliveryMethod || 'Standard Delivery',
    trackingSteps: [
      {
        id: 1,
        title: 'Order Confirmed',
        description: 'Your order has been confirmed and payment processed',
        timestamp: safeToDate(order?.createdAt).toISOString(),
        status: 'completed',
        icon: CheckCircle
      },
      {
        id: 2,
        title: 'Seller Notified',
        description: 'Seller has been notified and is preparing your item',
        timestamp: safeToDate(order?.updatedAt).toISOString(),
        status: order?.status === 'payment_succeeded' || order?.status === 'shipped_pending_code' || order?.status === 'delivered' || order?.status === 'completed' ? 'completed' : 'pending',
        icon: User
      },
      {
        id: 3,
        title: 'Item Prepared',
        description: 'Item has been packaged and is ready for pickup/delivery',
        timestamp: safeToDate(order?.trackingInfo?.shippedDate).toISOString(),
        status: order?.status === 'shipped_pending_code' || order?.status === 'delivered' || order?.status === 'completed' ? 'completed' : 'pending',
        icon: Package
      },
      {
        id: 4,
        title: 'Out for Delivery',
        description: 'Your item is on the way to your delivery address',
        timestamp: safeToDate(order?.trackingInfo?.shippedDate).toISOString(),
        status: order?.status === 'shipped_pending_code' ? 'current' : order?.status === 'delivered' || order?.status === 'completed' ? 'completed' : 'pending',
        icon: Truck
      },
      {
        id: 5,
        title: 'Delivered',
        description: 'Item has been delivered to your specified location',
        timestamp: order?.status === 'delivered' || order?.status === 'completed' ? new Date().toISOString() : null,
        status: order?.status === 'delivered' || order?.status === 'completed' ? 'completed' : 'pending',
        icon: MapPin
      }
    ],
    liveLocation: null // Removed currentLocation as it doesn't exist in TrackingInfo
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-success-600 dark:text-success-400 bg-success-100 dark:bg-success-900/20';
      case 'current':
        return 'text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/20';
      case 'pending':
        return 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-700';
      default:
        return 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-700';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const currentStep = orderData.trackingSteps.find(step => step.status === 'current');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-6xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Order Tracking</h1>
          <p className="text-gray-600 dark:text-gray-400">Track your order #{orderData.id}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Tracking Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Current Status Card */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {currentStep?.title || 'Order Processing'}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    {currentStep?.description || 'Your order is being processed'}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Estimated Delivery</div>
                  <div className="text-lg font-semibold text-primary-600 dark:text-primary-400">
                    {formatDate(orderData.estimatedDelivery)}
                  </div>
                </div>
              </div>

              {/* Test Mode Delivery Simulation */}
              {order?.paymentMethod === 'test_mode' && order?.status !== 'delivered' && order?.status !== 'completed' && (
                <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6 mb-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center">
                      <TestTube className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-orange-900 dark:text-orange-200">🧪 Test Mode</h3>
                      <p className="text-sm text-orange-700 dark:text-orange-300">
                        This is a test order. You can simulate delivery to test the escrow system.
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={simulateDelivery}
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                  >
                    <Package className="w-4 h-4" />
                    <span>🚚 Simulate Package Delivery</span>
                  </button>
                  <p className="text-xs text-orange-600 dark:text-orange-400 mt-2 text-center">
                    This will mark the order as delivered and trigger the 72-hour escrow countdown
                  </p>
                </div>
              )}

              {/* Delivery Confirmation (when delivered) */}
              {order?.status === 'delivered' && !codeSuccess && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6 mb-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-green-900 dark:text-green-200">📦 Package Delivered!</h3>
                      <p className="text-sm text-green-700 dark:text-green-300">
                        Your order has been delivered. Enter the 6-digit code to release payment to the seller.
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-green-800 dark:text-green-200 mb-2">
                        6-Digit Secret Code
                      </label>
                      <input
                        type="text"
                        value={secretCode}
                        onChange={(e) => setSecretCode(e.target.value)}
                        placeholder="Enter 6-digit code"
                        maxLength={6}
                        className="w-full px-4 py-3 border border-green-300 dark:border-green-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-center text-lg font-mono"
                      />
                    </div>

                    {codeError && (
                      <div className="text-red-600 dark:text-red-400 text-sm">{codeError}</div>
                    )}

                    <button
                      onClick={handleSubmitCode}
                      disabled={isSubmittingCode || secretCode.length !== 6}
                      className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                    >
                      {isSubmittingCode ? (
                        <>
                          <Loader className="w-4 h-4 animate-spin" />
                          <span>Verifying...</span>
                        </>
                      ) : (
                        <>
                          <Lock className="w-4 h-4" />
                          <span>Release Payment</span>
                        </>
                      )}
                    </button>

                    <div className="bg-green-100 dark:bg-green-900/50 rounded-lg p-3">
                      <p className="text-xs text-green-700 dark:text-green-300">
                        <strong>Auto-Release:</strong> If you don't enter the code within 72 hours,
                        payment will be automatically released to the seller.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Success Message */}
              {codeSuccess && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6 mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                      <Check className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-green-900 dark:text-green-200">✅ Payment Released!</h3>
                      <p className="text-sm text-green-700 dark:text-green-300">
                        Payment has been successfully released to the seller. Thank you for your purchase!
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Progress Bar */}
              <div className="relative">
                <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
                <div className="space-y-8">
                  {orderData.trackingSteps.map((step) => {
                    const IconComponent = step.icon;
                    return (
                      <div key={step.id} className="relative flex items-start space-x-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getStatusColor(step.status)} relative z-10`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h3 className={`font-semibold ${
                              step.status === 'completed' || step.status === 'current' 
                                ? 'text-gray-900 dark:text-white' 
                                : 'text-gray-500 dark:text-gray-400'
                            }`}>
                              {step.title}
                            </h3>
                            {step.timestamp && (
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDate(step.timestamp)}
                              </span>
                            )}
                          </div>
                          <p className={`text-sm mt-1 ${
                            step.status === 'completed' || step.status === 'current'
                              ? 'text-gray-600 dark:text-gray-400'
                              : 'text-gray-500 dark:text-gray-500'
                          }`}>
                            {step.description}
                          </p>
                          {step.status === 'current' && (
                            <div className="mt-2 flex items-center space-x-2">
                              <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                              <span className="text-sm text-primary-600 dark:text-primary-400 font-medium">
                                In Progress
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Delivery Instructions */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Delivery Instructions</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Delivery Address</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                      {orderData.buyer.address}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Truck className="w-5 h-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Delivery Method</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{orderData.deliveryMethod}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Calendar className="w-5 h-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Special Instructions</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Please call when you arrive. Leave at front desk if no answer.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Mock Testing Info (only for test orders) */}
            {order && order.id.startsWith('order_test_') && order.deliveryCode && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-2xl shadow-lg p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <TestTube className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                  <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-200">
                    Testing Mode - Mock Order
                  </h3>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-yellow-200 dark:border-yellow-700">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    This is a mock order for testing. Use this delivery code to simulate package receipt:
                  </p>
                  <div className="flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                    <span className="text-2xl font-mono font-bold text-gray-900 dark:text-white tracking-wider">
                      {order.deliveryCode}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                    Copy this code and enter it below to test the delivery confirmation flow
                  </p>
                </div>

                {/* Simulate Delivery Button for Testing */}
                <div className="mt-4 pt-4 border-t border-yellow-200 dark:border-yellow-700">
                  <button
                    onClick={simulateDelivery}
                    className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                  >
                    <Truck className="w-4 h-4" />
                    <span>Simulate Package Delivery</span>
                  </button>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-2 text-center">
                    Click to simulate package delivery and update tracking status
                  </p>
                </div>
              </div>
            )}

            {/* Secret Code Verification (only shown to buyer when order is shipped_pending_code) */}
            {order && userProfile && order.buyerId === userProfile.uid && order.status === 'shipped_pending_code' && (
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Confirm Receipt</h3>
                
                {codeSuccess ? (
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6 text-center">
                    <div className="w-16 h-16 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Check className="w-8 h-8 text-green-600 dark:text-green-400" />
                    </div>
                    <h4 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-2">
                      Payment Released!
                    </h4>
                    <p className="text-green-700 dark:text-green-300">
                      You've successfully confirmed receipt of your item. The payment has been released to the seller.
                    </p>
                  </div>
                ) : (
                  <>
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                      <div className="flex items-center space-x-3">
                        <Lock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        <div>
                          <p className="font-medium text-blue-900 dark:text-blue-200">Enter Secret Code</p>
                          <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                            Once you've received your item, enter the 6-digit code that was sent to you to release payment to the seller.
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    {codeError && (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                        <div className="flex items-start space-x-3">
                          <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
                          <div>
                            <p className="font-medium text-red-900 dark:text-red-200">Error</p>
                            <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                              {codeError}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex space-x-4">
                      <input
                        type="text"
                        value={secretCode}
                        onChange={(e) => setSecretCode(e.target.value.replace(/\D/g, '').substring(0, 6))}
                        placeholder="Enter 6-digit code"
                        className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-center text-lg font-mono"
                        maxLength={6}
                        disabled={isSubmittingCode}
                      />
                      <button
                        onClick={handleSubmitCode}
                        disabled={isSubmittingCode || secretCode.length !== 6}
                        className="px-6 py-3 bg-primary-600 text-white rounded-xl font-semibold hover:bg-primary-700 transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        {isSubmittingCode ? (
                          <Loader className="w-5 h-5 animate-spin" />
                        ) : (
                          'Verify'
                        )}
                      </button>
                    </div>
                    
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                      Note: If you don't enter the code, payment will automatically be released to the seller after 3 days.
                    </p>
                  </>
                )}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h3>
              <div className="flex items-center space-x-4 mb-4">
                <img
                  src={orderData.item.image}
                  alt={orderData.item.title}
                  className="w-16 h-16 rounded-xl object-cover"
                />
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                    {orderData.item.title}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{orderData.item.condition}</p>
                  <p className="text-lg font-bold text-primary-600 dark:text-primary-400">
                    ${orderData.item.price}
                    {orderData.orderType === 'rent' && orderData.rentalPeriod && (
                      <span className="text-sm text-gray-500 ml-1">/ {orderData.rentalPeriod.replace('ly', '')}</span>
                    )}
                    {orderData.orderType === 'bid' && (
                      <span className="text-sm text-gray-500 ml-1">(Bid)</span>
                    )}
                  </p>
                </div>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600 dark:text-gray-400">Order Date</span>
                  <span className="text-gray-900 dark:text-white">
                    {formatDate(orderData.orderDate)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Order ID</span>
                  <span className="text-gray-900 dark:text-white font-mono text-xs">
                    {orderData.id}
                  </span>
                </div>
              </div>
            </div>

            {/* Seller Info */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Seller Information</h3>
              <div className="flex items-center space-x-3 mb-4">
                <img
                  src={orderData.seller.avatar}
                  alt={orderData.seller.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      {orderData.seller.name}
                    </h4>
                    {orderData.seller.verified && (
                      <div className="bg-primary-500 text-white p-1 rounded-full">
                        <CheckCircle className="w-3 h-3" />
                      </div>
                    )}
                  </div>
                  <div className="flex items-center">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="ml-1 text-sm text-gray-600 dark:text-gray-400">
                      {orderData.seller.rating}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => setShowContactSeller(!showContactSeller)}
                  className="w-full bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 py-3 rounded-xl font-semibold hover:bg-primary-200 dark:hover:bg-primary-900/30 transition-all flex items-center justify-center space-x-2"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span>Contact Seller</span>
                </button>

                {showContactSeller && (
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 space-y-3">
                    <button className="w-full text-left flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                      <MessageCircle className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      <span className="text-gray-900 dark:text-white">Send Message</span>
                    </button>
                    <button className="w-full text-left flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                      <Phone className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      <span className="text-gray-900 dark:text-white">{orderData.seller.phone}</span>
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Help & Support */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Need Help?</h3>
              <div className="space-y-3">
                <button className="w-full text-left flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <AlertCircle className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <span className="text-gray-900 dark:text-white">Report an Issue</span>
                </button>
                <button className="w-full text-left flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <MessageCircle className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <span className="text-gray-900 dark:text-white">Contact Support</span>
                </button>
                <button className="w-full text-left flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <Package className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <span className="text-gray-900 dark:text-white">Delivery FAQ</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderTracking;