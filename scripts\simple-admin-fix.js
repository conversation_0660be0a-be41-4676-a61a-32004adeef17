// Simple script to run in Firebase Functions Shell
// Usage: 
// 1. cd functions
// 2. firebase functions:shell
// 3. Copy and paste this entire script
// 4. Call: fixAdminNow()

const admin = require('firebase-admin');

// Initialize if not already done
if (!admin.apps.length) {
  admin.initializeApp();
}

async function fixAdminNow() {
  try {
    const adminEmail = '<EMAIL>';
    const adminUID = 'CrFp8zLvGof9FkzDLz7wodmFmEy2';
    
    console.log('🔧 Fixing admin user:', adminEmail);
    
    // Set custom claims
    await admin.auth().setCustomUserClaims(adminUID, {
      admin: true,
      role: 'admin'
    });
    console.log('✅ Custom claims set');
    
    // Update Firestore document
    await admin.firestore().collection('users').doc(adminUID).set({
      uid: adminUID,
      name: 'Admin User',
      email: adminEmail,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });
    console.log('✅ Firestore document updated');
    
    // Create missing collections
    const collections = ['reports', 'shippingLabels', 'walletReports', 'universityAnalytics', 'systemMetrics', 'adminLogs'];
    
    for (const collectionName of collections) {
      await admin.firestore().collection(collectionName).doc('default').set({
        created: admin.firestore.Timestamp.now(),
        type: 'system'
      });
    }
    console.log('✅ Missing collections created');
    
    // Verify the fix
    const userRecord = await admin.auth().getUser(adminUID);
    console.log('🔍 Verification:');
    console.log('- Custom Claims:', userRecord.customClaims);
    
    const userDoc = await admin.firestore().collection('users').doc(adminUID).get();
    console.log('- Firestore Role:', userDoc.data()?.role);
    
    console.log('🎉 Admin user fixed successfully!');
    console.log('📋 Next steps:');
    console.log('1. Clear browser cache');
    console.log('2. Login with', adminEmail);
    console.log('3. Navigate to admin dashboard');
    console.log('4. All permission errors should be resolved');
    
    return { success: true, message: 'Admin user fixed successfully' };
    
  } catch (error) {
    console.error('❌ Error:', error);
    return { success: false, error: error.message };
  }
}

// Export for use in functions shell
module.exports = { fixAdminNow };
