import React from 'react';
import { monitoring } from './monitoring';

// API monitoring wrapper for Firebase calls
export const monitoredAPICall = async <T,>(
  operation: () => Promise<T>,
  endpoint: string,
  method: string = 'GET'
): Promise<T> => {
  const startTime = performance.now();
  let error: Error | undefined;
  let result: T;

  try {
    result = await operation();
    const duration = performance.now() - startTime;
    
    // Track successful API call
    monitoring.trackAPICall(endpoint, method, 200, duration);
    
    return result;
  } catch (err) {
    error = err instanceof Error ? err : new Error(String(err));
    const duration = performance.now() - startTime;
    
    // Determine status code from error
    let status = 500; // Default to server error
    if (error.message.includes('permission-denied')) status = 403;
    if (error.message.includes('not-found')) status = 404;
    if (error.message.includes('invalid-argument')) status = 400;
    if (error.message.includes('unauthenticated')) status = 401;
    
    // Track failed API call
    monitoring.trackAPICall(endpoint, method, status, duration, error);
    
    throw error;
  }
};

// Firebase operation wrappers
export const FirebaseMonitoring = {
  // Auth operations
  auth: {
    signIn: (operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, '/auth/signin', 'POST'),
    
    signUp: (operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, '/auth/signup', 'POST'),
    
    signOut: (operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, '/auth/signout', 'POST'),
    
    resetPassword: (operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, '/auth/reset-password', 'POST'),
  },

  // Firestore operations
  firestore: {
    get: (collection: string, operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, `/firestore/${collection}`, 'GET'),
    
    create: (collection: string, operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, `/firestore/${collection}`, 'POST'),
    
    update: (collection: string, operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, `/firestore/${collection}`, 'PUT'),
    
    delete: (collection: string, operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, `/firestore/${collection}`, 'DELETE'),
    
    query: (collection: string, operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, `/firestore/${collection}/query`, 'POST'),
  },

  // Storage operations
  storage: {
    upload: (operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, '/storage/upload', 'POST'),
    
    download: (operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, '/storage/download', 'GET'),
    
    delete: (operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, '/storage/delete', 'DELETE'),
  },

  // Functions operations
  functions: {
    call: (functionName: string, operation: () => Promise<unknown>) => 
      monitoredAPICall(operation, `/functions/${functionName}`, 'POST'),
  },
};

// HTTP request monitoring
export const monitorFetch = (url: string, options: RequestInit = {}): Promise<Response> => {
  const startTime = performance.now();
  const method = options.method || 'GET';

  return fetch(url, options)
    .then(response => {
      const duration = performance.now() - startTime;
      monitoring.trackAPICall(url, method, response.status, duration);
      return response;
    })
    .catch(error => {
      const duration = performance.now() - startTime;
      monitoring.trackAPICall(url, method, 0, duration, error);
      throw error;
    });
};

// Axios interceptor (if using axios)
export const setupAxiosMonitoring = (axiosInstance: {
  interceptors: {
    request: { use: (success: (config: unknown) => unknown, error: (error: unknown) => unknown) => void };
    response: { use: (success: (response: unknown) => unknown, error: (error: unknown) => unknown) => void };
  };
}) => {
  // Request interceptor
  axiosInstance.interceptors.request.use(
    (config: { metadata?: { startTime: number } }) => {
      config.metadata = { startTime: performance.now() };
      return config;
    },
    (error: unknown) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor
  axiosInstance.interceptors.response.use(
    (response: { config: { metadata: { startTime: number }; url: string; method: string }; status: number }) => {
      const duration = performance.now() - response.config.metadata.startTime;
      monitoring.trackAPICall(
        response.config.url,
        response.config.method.toUpperCase(),
        response.status,
        duration
      );
      return response;
    },
    (error: { config?: { metadata?: { startTime: number }; url: string; method: string }; response?: { status: number } }) => {
      if (error.config && error.config.metadata) {
        const duration = performance.now() - error.config.metadata.startTime;
        monitoring.trackAPICall(
          error.config.url,
          error.config.method.toUpperCase(),
          error.response?.status || 0,
          duration,
          error as Error
        );
      }
      return Promise.reject(error);
    }
  );
};

// React component monitoring
export const withAPIMonitoring = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  return (props: P) => {
    React.useEffect(() => {
      monitoring.trackUserAction(`${componentName}_mounted`);
      
      return () => {
        monitoring.trackUserAction(`${componentName}_unmounted`);
      };
    }, []);

    return React.createElement(WrappedComponent, props);
  };
};

// Performance monitoring hooks
export const useAPIMonitoring = (componentName: string) => {
  React.useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      monitoring.trackPerformance(`${componentName}_render_time`, duration);
    };
  }, [componentName]);

  const trackAction = React.useCallback((action: string, properties?: Record<string, unknown>) => {
    monitoring.trackUserAction(`${componentName}_${action}`, properties);
  }, [componentName]);

  const trackError = React.useCallback((error: Error, context?: Record<string, unknown>) => {
    monitoring.trackError(error, { component: componentName, ...context });
  }, [componentName]);

  return { trackAction, trackError };
};

// Error boundary with monitoring
export class MonitoredErrorBoundary extends React.Component<
  { children: React.ReactNode; componentName: string },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; componentName: string }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    monitoring.trackError(error, {
      component: this.props.componentName,
      errorInfo: errorInfo.componentStack,
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary p-4 bg-red-50 border border-red-200 rounded-lg">
          <h2 className="text-red-800 font-semibold">Something went wrong</h2>
          <p className="text-red-600 mt-2">
            An error occurred in the {this.props.componentName} component.
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

export default {
  monitoredAPICall,
  FirebaseMonitoring,
  monitorFetch,
  setupAxiosMonitoring,
  withAPIMonitoring,
  useAPIMonitoring,
  MonitoredErrorBoundary,
};