import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';



// Function to set admin PIN
export const setAdminPin = functions.https.onCall(async (data, context) => {
  try {
    // Verify the user is authenticated and is an admin
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'User must be authenticated'
      );
    }

    const { pin } = data;
    
    if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'PIN must be exactly 8 digits'
      );
    }

    // Verify user is admin
    const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admin users can set PIN'
      );
    }

    // Hash the PIN for security
    const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');

    // Store the hashed PIN in admin settings
    await admin.firestore().collection('adminSettings').doc('security').set({
      adminPin: hashedPin,
      pinSetAt: admin.firestore.Timestamp.now(),
      pinSetBy: context.auth.uid
    }, { merge: true });

    console.log(`Admin PIN set by user: ${context.auth.uid}`);
    
    return {
      success: true,
      message: 'Admin PIN set successfully'
    };

  } catch (error) {
    console.error('Error setting admin PIN:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to set admin PIN',
      error
    );
  }
});

// Function to verify admin PIN
export const verifyAdminPin = functions.https.onCall(async (data, context) => {
  try {
    // Verify the user is authenticated and is an admin
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'User must be authenticated'
      );
    }

    const { pin } = data;
    
    if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'PIN must be exactly 8 digits'
      );
    }

    // Verify user is admin
    const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admin users can verify PIN'
      );
    }

    // Get stored PIN hash
    const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
    if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
      throw new functions.https.HttpsError(
        'not-found',
        'Admin PIN not set. Please set up your PIN first.'
      );
    }

    // Hash the provided PIN and compare
    const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');
    const storedPin = securityDoc.data()?.adminPin;

    if (hashedPin !== storedPin) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Invalid PIN'
      );
    }

    // Update last access time
    await admin.firestore().collection('users').doc(context.auth.uid).update({
      lastAdminAccess: admin.firestore.Timestamp.now()
    });

    console.log(`Admin PIN verified for user: ${context.auth.uid}`);
    
    return {
      success: true,
      message: 'PIN verified successfully'
    };

  } catch (error) {
    console.error('Error verifying admin PIN:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to verify admin PIN',
      error
    );
  }
});

// Function to fix existing admin user (for setup)
export const fixAdminUser = functions.https.onCall(async (data, _context) => {
  try {
    const { email } = data;
    
    if (!email) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email is required'
      );
    }

    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);
    
    // Set custom claims with both admin and role
    await admin.auth().setCustomUserClaims(userRecord.uid, { 
      admin: true,
      role: 'admin'
    });
    
    // Update or create user profile in Firestore with complete admin setup
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });

    console.log(`Admin user fixed for: ${email}`);
    
    return {
      success: true,
      message: `Admin user fixed for ${email}`,
      uid: userRecord.uid
    };

  } catch (error) {
    console.error('Error fixing admin user:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to fix admin user',
      error
    );
  }
});
