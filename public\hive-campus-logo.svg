<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="hexGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f9a826;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff6b35;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background hexagons pattern -->
  <g fill="none" stroke="url(#hexGradient)" stroke-width="2" opacity="0.3">
    <!-- Top row -->
    <polygon points="100,50 150,25 200,50 200,100 150,125 100,100" />
    <polygon points="200,50 250,25 300,50 300,100 250,125 200,100" />
    
    <!-- Second row -->
    <polygon points="50,125 100,100 150,125 150,175 100,200 50,175" />
    <polygon points="150,125 200,100 250,125 250,175 200,200 150,175" />
    <polygon points="250,125 300,100 350,125 350,175 300,200 250,175" />
    
    <!-- Third row (center) -->
    <polygon points="100,200 150,175 200,200 200,250 150,275 100,250" />
    <polygon points="200,200 250,175 300,200 300,250 250,275 200,250" />
    
    <!-- Fourth row -->
    <polygon points="50,275 100,250 150,275 150,325 100,350 50,325" />
    <polygon points="150,275 200,250 250,275 250,325 200,350 150,325" />
    <polygon points="250,275 300,250 350,275 350,325 300,350 250,325" />
    
    <!-- Bottom row -->
    <polygon points="100,350 150,325 200,350 200,400 150,425 100,400" />
    <polygon points="200,350 250,325 300,350 300,400 250,425 200,400" />
  </g>
</svg>
