import { useState, useCallback } from 'react';
import { 
  submitFeedback, 
  reportIssue, 
  getFeedback, 
  getIssueReports, 
  updateIssueStatus, 
  getUserIssueReports 
} from '../firebase';
import { Feedback, IssueReport } from '../firebase/types';



export const useFeedback = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [feedback, setFeedback] = useState<Feedback[]>([]);
  const [issues, setIssues] = useState<IssueReport[]>([]);
  const [lastVisible, setLastVisible] = useState<string | null>(null);

  // Submit user feedback
  const sendFeedback = useCallback(async (rating: number, message: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await submitFeedback(rating, message);
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to submit feedback';
      setError(errorMsg);
      throw err;
    }
  }, []);

  // Report an issue
  const reportProblem = useCallback(async (
    category: string,
    title: string,
    description: string,
    screenshotURL?: string
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await reportIssue(category, title, description, screenshotURL);
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to report issue';
      setError(errorMsg);
      throw err;
    }
  }, []);

  // Get all feedback (admin only)
  const getAllFeedback = useCallback(async (
    limit: number = 50,
    loadMore: boolean = false
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // If loading more, use the last visible ID from previous results
      const lastVisibleId = loadMore ? lastVisible : undefined;
      
      const result = await getFeedback(limit, lastVisibleId);
      
      if (result && result.success) {
        if (loadMore) {
          // Append to existing feedback
          setFeedback(prev => [...prev, ...result.data.feedback]);
        } else {
          // Replace existing feedback
          setFeedback(result.data.feedback);
        }
        
        setLastVisible(result.data.lastVisible);
      }
      
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to get feedback';
      setError(errorMsg);
      throw err;
    }
  }, [lastVisible]);

  // Get all issue reports (admin only)
  const getAllIssues = useCallback(async (
    status?: 'open' | 'in_progress' | 'resolved' | 'closed',
    limit: number = 50,
    loadMore: boolean = false
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // If loading more, use the last visible ID from previous results
      const lastVisibleId = loadMore ? lastVisible : undefined;
      
      const result = await getIssueReports(status, limit, lastVisibleId);
      
      if (result && result.success) {
        if (loadMore) {
          // Append to existing issues
          setIssues(prev => [...prev, ...result.data.issues]);
        } else {
          // Replace existing issues
          setIssues(result.data.issues);
        }
        
        setLastVisible(result.data.lastVisible);
      }
      
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to get issues';
      setError(errorMsg);
      throw err;
    }
  }, [lastVisible]);

  // Update issue status (admin only)
  const updateIssue = useCallback(async (
    issueId: string,
    status: 'open' | 'in_progress' | 'resolved' | 'closed'
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await updateIssueStatus(issueId, status);
      
      // Update the issue in the local state
      if (result && result.success) {
        setIssues(prev => 
          prev.map(issue => 
            issue.id === issueId ? { ...issue, status } : issue
          )
        );
      }
      
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to update issue';
      setError(errorMsg);
      throw err;
    }
  }, []);

  // Get user's own issue reports
  const getUserIssues = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getUserIssueReports();
      
      if (result && result.success) {
        setIssues(result.data.issues);
      }
      
      setIsLoading(false);
      return result;
    } catch (err: unknown) {
      setIsLoading(false);
      const errorMsg = err instanceof Error ? err.message : 'Failed to get user issues';
      setError(errorMsg);
      throw err;
    }
  }, []);

  return {
    isLoading,
    error,
    feedback,
    issues,
    lastVisible,
    hasMore: lastVisible !== null,
    sendFeedback,
    reportProblem,
    getAllFeedback,
    getAllIssues,
    updateIssue,
    getUserIssues
  };
};