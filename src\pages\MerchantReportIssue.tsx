import React, { useState } from 'react';
import { AlertTriangle, Upload, Send, Bug, Shield, MessageSquare, CreditCard } from 'lucide-react';


const MerchantReportIssue: React.FC = () => {
  const [issueType, setIssueType] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('medium');
  const [attachments, setAttachments] = useState<File[]>([]);

  const issueTypes = [
    {
      id: 'technical',
      icon: Bug,
      title: 'Technical Issue',
      description: 'Dashboard problems, bugs, or system errors'
    },
    {
      id: 'account',
      icon: Shield,
      title: 'Account Issue',
      description: 'Login problems, verification, or access issues'
    },
    {
      id: 'payment',
      icon: CreditCard,
      title: 'Payment Problem',
      description: 'Billing issues, payment processing errors'
    },
    {
      id: 'support',
      icon: MessageSquare,
      title: 'Support Request',
      description: 'General help or guidance needed'
    }
  ];

  const priorityLevels = [
    { id: 'low', label: 'Low', color: 'text-green-600 bg-green-100 dark:bg-green-900/20' },
    { id: 'medium', label: 'Medium', color: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20' },
    { id: 'high', label: 'High', color: 'text-red-600 bg-red-100 dark:bg-red-900/20' }
  ];

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setAttachments([...attachments, ...files]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Issue reported:', {
      type: issueType,
      priority,
      description,
      attachments: attachments.length
    });
    alert('Issue reported successfully! Our partner support team will get back to you soon.');
  };

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Report an Issue</h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Help us maintain a reliable partner platform by reporting any issues you encounter
            </p>
          </div>

          {/* Report Form */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Issue Type Selection */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-6">
                  What type of issue are you reporting?
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {issueTypes.map((type) => {
                    const IconComponent = type.icon;
                    return (
                      <button
                        key={type.id}
                        type="button"
                        onClick={() => setIssueType(type.id)}
                        className={`p-6 text-left rounded-2xl border-2 transition-all ${
                          issueType === type.id
                            ? 'border-accent-500 bg-accent-50 dark:bg-accent-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                        }`}
                      >
                        <div className="flex items-start space-x-4">
                          <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                            issueType === type.id
                              ? 'bg-accent-100 dark:bg-accent-800'
                              : 'bg-gray-100 dark:bg-gray-700'
                          }`}>
                            <IconComponent className={`w-6 h-6 ${
                              issueType === type.id
                                ? 'text-accent-600 dark:text-accent-400'
                                : 'text-gray-600 dark:text-gray-400'
                            }`} />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                              {type.title}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {type.description}
                            </p>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Priority Level */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Priority Level
                </label>
                <div className="flex space-x-4">
                  {priorityLevels.map((level) => (
                    <button
                      key={level.id}
                      type="button"
                      onClick={() => setPriority(level.id)}
                      className={`px-6 py-3 rounded-xl font-medium transition-all ${
                        priority === level.id
                          ? level.color
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {level.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Describe the issue in detail
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                  placeholder="Please provide as much detail as possible about the issue you're experiencing. Include steps to reproduce the problem if applicable..."
                  required
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {description.length}/1000 characters
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Be specific to help us resolve the issue faster
                  </p>
                </div>
              </div>

              {/* File Attachments */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Attachments (optional)
                </label>
                <div className="space-y-4">
                  {/* Upload Area */}
                  <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl cursor-pointer hover:border-accent-500 transition-colors">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        <span className="font-semibold">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Screenshots, videos, or documents (Max 10MB each)
                      </p>
                    </div>
                    <input
                      type="file"
                      multiple
                      accept="image/*,video/*,.pdf,.doc,.docx"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </label>

                  {/* Attachment List */}
                  {attachments.length > 0 && (
                    <div className="space-y-2">
                      {attachments.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                          <button
                            type="button"
                            onClick={() => removeAttachment(index)}
                            className="text-red-500 hover:text-red-700 text-sm"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Submit Button */}
              <div className="text-center">
                <button
                  type="submit"
                  disabled={!issueType || !description.trim()}
                  className="bg-gradient-to-r from-red-600 to-orange-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-red-700 hover:to-orange-700 transform hover:scale-[1.02] transition-all duration-200 shadow-lg flex items-center space-x-2 mx-auto disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-5 h-5" />
                  <span>Submit Report</span>
                </button>
              </div>
            </form>
          </div>

          {/* Support Info */}
          <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-3">
              What happens next?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800 dark:text-blue-300">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-blue-200 dark:bg-blue-800 rounded-full flex items-center justify-center text-xs font-bold">1</div>
                <span>Our partner support team will review your report within 12 hours</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-blue-200 dark:bg-blue-800 rounded-full flex items-center justify-center text-xs font-bold">2</div>
                <span>We'll investigate and work on a solution</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-blue-200 dark:bg-blue-800 rounded-full flex items-center justify-center text-xs font-bold">3</div>
                <span>You'll receive updates via email and dashboard notifications</span>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default MerchantReportIssue;