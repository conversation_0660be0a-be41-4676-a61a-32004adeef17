import React, { useState } from 'react';
import { RefreshCw, X, Mail, ExternalLink } from 'lucide-react';
import { getUserFriendlyError, getErrorSeverityColor, getErrorSeverityIcon } from '../utils/userFriendlyErrors';
import { retryOperation } from '../utils/retryHandler';

interface ErrorNotificationProps {
  error: Error | string | null;
  onRetry?: () => Promise<void>;
  onDismiss?: () => void;
  className?: string;
  showContactSupport?: boolean;
}

const ErrorNotification: React.FC<ErrorNotificationProps> = ({
  error,
  onRetry,
  onDismiss,
  className = '',
  showContactSupport = true
}) => {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  if (!error) return null;

  const friendlyError = getUserFriendlyError(error);
  const colorClasses = getErrorSeverityColor(friendlyError.severity);
  const icon = getErrorSeverityIcon(friendlyError.severity);

  const handleRetry = async () => {
    if (!onRetry || !friendlyError.canRetry) return;

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      const result = await retryOperation(onRetry, {
        maxAttempts: 1, // Single retry from UI
        baseDelay: 0,
        onRetry: (attempt, error) => {
          console.log(`UI retry attempt ${attempt}:`, error.message);
        }
      });

      if (result.success && onDismiss) {
        onDismiss();
      }
    } catch (retryError) {
      console.error('Retry failed:', retryError);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleContactSupport = () => {
    const subject = encodeURIComponent(`Support Request: ${friendlyError.title}`);
    const body = encodeURIComponent(
      `Hi Hive Campus Support,\n\n` +
      `I encountered an error while using the app:\n\n` +
      `Error: ${friendlyError.title}\n` +
      `Message: ${friendlyError.message}\n` +
      `Technical Details: ${typeof error === 'string' ? error : error.message}\n\n` +
      `Please help me resolve this issue.\n\n` +
      `Thank you!`
    );
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`, '_blank');
  };

  return (
    <div className={`rounded-xl border p-4 ${colorClasses} ${className}`}>
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          <span className="text-lg" role="img" aria-label="Error severity">
            {icon}
          </span>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-sm font-semibold">
                {friendlyError.title}
              </h3>
              <p className="text-sm mt-1 opacity-90">
                {friendlyError.message}
              </p>
              {friendlyError.action && (
                <p className="text-sm mt-2 font-medium">
                  {friendlyError.action}
                </p>
              )}
            </div>

            {/* Dismiss Button */}
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="flex-shrink-0 ml-3 p-1 rounded-md hover:bg-black/10 transition-colors"
                aria-label="Dismiss error"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-3 mt-4">
            {/* Retry Button */}
            {friendlyError.canRetry && onRetry && (
              <button
                onClick={handleRetry}
                disabled={isRetrying}
                className="inline-flex items-center space-x-2 px-3 py-1.5 text-sm font-medium bg-white/20 hover:bg-white/30 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
              >
                <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
                <span>
                  {isRetrying ? 'Retrying...' : retryCount > 0 ? `Try Again (${retryCount})` : 'Try Again'}
                </span>
              </button>
            )}

            {/* Contact Support Button */}
            {friendlyError.contactSupport && showContactSupport && (
              <button
                onClick={handleContactSupport}
                className="inline-flex items-center space-x-2 px-3 py-1.5 text-sm font-medium bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
              >
                <Mail className="w-4 h-4" />
                <span>Contact Support</span>
                <ExternalLink className="w-3 h-3" />
              </button>
            )}
          </div>

          {/* Technical Details (Development Only) */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-3">
              <summary className="text-xs cursor-pointer opacity-70 hover:opacity-100">
                Technical Details
              </summary>
              <pre className="text-xs mt-2 p-2 bg-black/10 rounded overflow-auto max-h-32">
                {typeof error === 'string' ? error : error.stack || error.message}
              </pre>
            </details>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorNotification;
